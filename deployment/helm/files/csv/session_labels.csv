"id","name","description","display_text","default_threat_score","default_trust_score","threat_score","trust_score","created_time","updated_time","family_id","category","created_by"
"1","确认黑名单会话","Marked Black Session","确认黑名单会话","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"2","只在非工作时间出联","Appear Only in Working Hour","只在非工作时间出联","50","1","50","1","","","0","KNOWLEDGE_BASE","0"
"3","短Cipher","Short Cipher","短Cipher","10","0","10","0","","","0","APT","0"
"4","短包长","Short Pkt","短包长","10","0","10","0","","","0","APT","0"
"5","中包长","Medium Pkt","中包长","10","0","10","0","","","0","APT","0"
"6","长包长","Long Pkt","长包长","10","0","10","0","","","0","APT","0"
"7","MAC通信异常会话","Baseline Abnormal Mac2Mac Session","MAC通信异常会话","40","0","40","0","","","0","KNOWLEDGE_BASE","0"
"8","指纹基线异常会话","Baseline Abnormal Finger Session","指纹基线异常会话","30","0","30","0","","","0","KNOWLEDGE_BASE","0"
"9","HTTP参数基线异常","Baseline Abnormal HTTP Arg Session","HTTP参数基线异常","40","0","40","0","","","0","KNOWLEDGE_BASE","0"
"10","Covenant控制会话","Covenant","Covenant控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"11","Shad0w控制会话","Shad0w","Shad0w控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"12","PoshC2控制会话","PoshC2","PoshC2控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"13","Octopus控制会话","Octopus","Octopus控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"14","Godoh控制会话","Godoh","Godoh控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"15","Venom控制会话","Venom","Venom控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"16","Slackor控制会话","Slackor","Slackor控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"17","Gorsh控制会话","Gorsh","Gorsh控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"18","DeimosC2控制会话","DeimosC2","DeimosC2控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"19","Emp3r0r控制会话","Emp3r0r","Emp3r0r控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"20","ToRat控制会话","ToRat","ToRat控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"21","TheFatRat控制会话","TheFatRat","TheFatRat控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"22","Ares控制会话","Ares","Ares控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"23","Quasar控制会话","Quasar","Quasar控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"24","Sliver控制会话","Sliver","Sliver控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"25","HTTP隐蔽信道","HTTP隐蔽隧道会话","HTTP隐蔽信道","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"26","DNS隐蔽信道","DNS Tunnel Session","DNS隐蔽信道","80","0","80","0","","","0","HIGH_DIMENSION_LABEL","0"
"27","CobaltStrike控制会话","CobaltStrike Beacon Session","CobaltStrike控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"28","白象APT控制工具会话","Patchwork C&C Session","白象APT控制工具会话","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"29","加密Tor会话","Encrypted TOR","加密Tor会话","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"30","Meek机制Tor会话","Tor Meek Session","Meek机制Tor会话","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"31","FTE机制Tor会话","Tor FTE  Session","FTE机制Tor会话","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"32","OR2/3机制Tor会话","Tor OR2&3 Session","OR2/3机制Tor会话","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"33","Obfs4机制Tor会话","Tor Obfs4 Session","Obfs4机制Tor会话","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"34","ZeroNet暗网会话","ZeroNet Session","ZeroNet暗网会话","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"35","APT28 Zebrocy控制会话","APT28 Zebrocy Session","APT28 Zebrocy控制会话","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"36","APT-C-39远控会话","APT-C-39 C&C Session","APT-C-39远控会话","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"37","APT32 denesRAT","APT32 denesRAT","APT32 denesRAT","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"38","APT29 WellMess控制会话","APT29 WellMess控制会话","APT29 WellMess控制会话","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"39","Emotet控制会话","Emotet","Emotet控制会话","80","0","80","0","","","0","HIGH_DIMENSION_LABEL","0"
"40","Qakbot控制会话","Qakbot","Qakbot控制会话","80","0","80","0","","","0","HIGH_DIMENSION_LABEL","0"
"41","Hancitor控制会话","Hancitor","Hancitor控制会话","80","0","80","0","","","0","HIGH_DIMENSION_LABEL","0"
"42","Trickbot控制会话","Trickbot","Trickbot控制会话","80","0","80","0","","","0","HIGH_DIMENSION_LABEL","0"
"43","挖矿地址通讯","Mine Domain Session","挖矿地址通讯","50","0","50","0","","","0","KNOWLEDGE_BASE","0"
"44","挖矿协议通讯","Mine Protocol","挖矿协议通讯","50","0","50","0","","","0","KNOWLEDGE_BASE","0"
"45","序列字符负载","VCDM Payload","序列字符负载","30","0","30","0","","","0","KNOWLEDGE_BASE","0"
"46","VCMD负载","SeqChr Payload","VCMD负载","30","0","30","0","","","0","KNOWLEDGE_BASE","0"
"47","APT29 加密控制会话","APT29 加密控制会话","APT29 加密控制会话","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"48","恶意加密连接","Malware Encrypted Session","恶意加密连接","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"49","Gcat控制会话","Gcat","Gcat控制会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"50","恶意域名关联会话","ThreatInfo Domain Related Session","恶意域名关联会话","40","0","40","0","","","0","KNOWLEDGE_BASE","0"
"51","基线访问服务异常","Baseline Abnormal Access Service","基线访问服务异常","30","0","30","0","","","0","KNOWLEDGE_BASE","0"
"52","基线开放服务异常","Baseline Abnormal Open Service","基线开放服务异常","30","0","30","0","","","0","KNOWLEDGE_BASE","0"
"53","基线Mac通信异常","Baseline Abnormal Mac2Mac","基线Mac通信异常","30","0","30","0","","","0","KNOWLEDGE_BASE","0"
"54","基线网段通讯异常","Baseline Abnormal Net2Net","基线网段通讯异常","30","0","30","0","","","0","KNOWLEDGE_BASE","0"
"55","基线指纹使用异常","Baseline Abnormal Finger","基线指纹使用异常","30","0","30","0","","","0","KNOWLEDGE_BASE","0"
"56","挖矿连接通讯","Mine Protocol Session","挖矿连接通讯","50","0","50","0","","","0","KNOWLEDGE_BASE","0"
"57","EW端口转发","EW Protocol Trans Session","EW端口转发","50","0","50","0","","","0","KNOWLEDGE_BASE","0"
"58","操作系统信息传输","OS Info Trans Session","操作系统信息传输","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"59","激活回连通信","Activate Session","激活回连通信","75","0","75","0","","","0","KNOWLEDGE_BASE","0"
"60","包间隔异会话","Abnormal Pkt Time Interval","包间隔异会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"61","飞秋通信","FeiQ UDP Session","飞秋通信","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"62","布谷鸟通信","Sisen Session","布谷鸟通信","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"63","向日葵通信","Oray Session","向日葵通信","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"64","密码套件随机性","指纹随机化登录中检测出密码套件随机性","密码套件随机性","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"65","加密扩展随机性","指纹随机化登录中检测出加密扩展随机性","加密扩展随机性","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"66","向日葵远控UDP会话","Sunlogin UDP","向日葵远控UDP会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"67","向日葵远控TCP会话","Sunlogin TCP","向日葵远控TCP会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"68","Teamviewer远控会话","Teamviewer","Teamviewer远控会话","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"69","Web登录爆破","web login brute","Web登录爆破","50","0","50","0","","","0","KNOWLEDGE_BASE","0"
"70","端口扫描","port scan","端口扫描","40","0","40","0","","","0","KNOWLEDGE_BASE","0"
"71","跨域资源读取","Cross-origin resource sharing","跨域资源读取","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"72","mysql登录爆破","mysql登录爆破","mysql登录爆破","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"73","mssql登陆爆破","Mssql Login Brute","mssql登陆爆破","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"74","oracle登录爆破","oracle登录爆破","oracle登录爆破","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"75","mongoDB登陆爆破","mongoDB Login Brute","mongoDB登陆爆破","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"76","redis登陆爆破","Redis Login Brute","redis登陆爆破","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"77","ftp登陆爆破","Ftp Login Brute","ftp登陆爆破","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"78","ssh登录爆破","ssh登录爆破","ssh登录爆破","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"79","rdp登录爆破","rdp登录爆破","rdp登录爆破","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"80","smb登录爆破","smb登录爆破","smb登录爆破","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"81","客户端为常见服务端口","Client Use Server Port","客户端为常见服务端口","20","0","20","0","","","0","KNOWLEDGE_BASE","0"
"82","ms14_068","ms14_068","ms14_068","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"83","域用户名枚举","Username Enum","域用户名枚举","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"84","域用户密码爆破"," Password Spraying","域用户密码爆破","90","0","90","0","","","0","KNOWLEDGE_BASE","0"
"85","黄金票据","Golden Ticket","黄金票据","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"86","白银票据","Silver Ticket","白银票据","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"87","CVE-2021-42287/CVE-2021-42278域内提权","CVE-2021-42287/CVE-2021-42278","CVE-2021-42287/CVE-2021-42278域内提权","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"88","CVE-2020-1472","CVE-2020-1472 Zero Logon","CVE-2020-1472","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"89","约束委派攻击"," Constrained Delegation Attack ","约束委派攻击","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"90","非约束委派攻击","Unconstrained Delegation Attack ","非约束委派攻击","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"91","登陆脚本定向挂马","Landing script directional Launch Payload","登陆脚本定向挂马","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"92","域证书攻击","AD CA Attack","域证书攻击","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"93","票据传递","Pass The Ticket","票据传递","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"94","NTLM-RELAY","NTLM-RELAY","NTLM-RELAY","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"95","哈希传递","Pass The Hash","哈希传递","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"96","AS-REPRoasting","AS-REPRoasting","AS-REPRoasting","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"97","kerberosting","kerberosting","kerberosting","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"98","SID劫持","SID Hijack","SID劫持","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"99","SPN扫描","SPN Scan","SPN扫描","50","0","50","0","","","0","KNOWLEDGE_BASE","0"
"100","frp隧道","frp","frp隧道","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"101","NPS隧道","NPS隧道","NPS隧道","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"102","ew隧道","ew隧道","ew隧道","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"103","reGeorg隧道","reGeorg","reGeorg隧道","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"104","spp隧道","spp","spp隧道","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"105","Pystinger代理隧道","Pystinger","Pystinger代理隧道","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"106","GoProxy代理隧道","GoProxy代理隧道","GoProxy代理隧道","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"107","PingTunnel代理隧道","PingTunnel代理隧道","PingTunnel代理隧道","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"108","加密webshell连接"," Encrypted Webshell Connection ","加密webshell连接","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"109","非加密webshell连接","Unencrypted Webshell Connection","非加密webshell连接","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"110","metasploit行为","metasploit行为","metasploit行为","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"111","Empire后渗透框架","Empire后渗透框架","Empire后渗透框架","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"112","Web弱口令","Web weak password","Web弱口令","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"113","服务端请求伪造","Server-side request forgery (SSRF)","服务端请求伪造","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"114","文件上传漏洞","File upload","文件上传漏洞","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"115","命令执行","Remote Command Execute ","命令执行","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"116","服务端模板注入","Server-Side Template Injection","服务端模板注入","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"117","反序列化","Insecure deserialization","反序列化","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"118","文件读取","File Read","文件读取","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"119","访问控制漏洞","Access control vulnerabilities","访问控制漏洞","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"120","目录遍历","Directory traversal","目录遍历","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"121","代码执行","Remote Code Execute ","代码执行","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"122","外部实体注入","XML External Entity（XXE）","外部实体注入","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"123","JNDI注入","None","JNDI注入","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"124","文件包含","File Include","文件包含","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"125","shiro反序列化RCE漏洞","Shiro deserialization RCE","shiro反序列化RCE漏洞","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"126","shiro硬编码密钥","Shiro Hard Coding","shiro硬编码密钥","50","0","50","0","","","0","KNOWLEDGE_BASE","0"
"127","weblogic IIOP反序列化漏洞","weblogic IIOP Deserialization RCE","weblogic IIOP反序列化漏洞","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"128","Weblogic XML反序列化漏洞","Weblogic XML Deserialization RCE","Weblogic XML反序列化漏洞","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"129","Xstream反序列化漏洞","Xstream Deserialization RCE","Xstream反序列化漏洞","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"130","反弹shell","Recommend Shell","反弹shell","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"131","Citrix SD-WAN Center RCE","Citrix SD-WAN Center RCE","Citrix SD-WAN Center RCE","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"132","Microsoft Exchange Serve XXE","Microsoft Exchange Serve XXE","Microsoft Exchange Serve XXE","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"133","Apache Unomi RCE(CVE-2020-13942)","Apache Unomi RCE(CVE-2020-13942)","Apache Unomi RCE(CVE-2020-13942)","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"134","SolarWinds Orion身份验证绕过(CVE-2020-10148 )","SolarWinds Orion身份验证绕过(CVE-2020-10148 )","SolarWinds Orion身份验证绕过(CVE-2020-10148 )","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"135","向日葵完全版","向日葵完全版","向日葵完全版","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"136","向日葵-SOS 主控","向日葵-SOS 主控","向日葵-SOS 主控","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"137","向日葵-SOS 被控","向日葵-SOS 被控","向日葵-SOS 被控","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"138","ToDesk精简版","ToDesk精简版","ToDesk精简版","80","0","80","0","","","0","HIGH_DIMENSION_LABEL","0"
"139","Teamviewer","Teamviewer","Teamviewer","80","0","80","0","","","0","HIGH_DIMENSION_LABEL","0"
"140","ToDesk辅助规则","ToDesk辅助规则","ToDesk辅助规则","80","0","80","0","","","0","HIGH_DIMENSION_LABEL","0"
"141","ToDesk正式版小IP主控","ToDesk正式版小IP主控","ToDesk正式版小IP主控","80","0","80","0","","","0","HIGH_DIMENSION_LABEL","0"
"142","ToDesk正式版大IP主控","ToDesk正式版大IP主控","ToDesk正式版大IP主控","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"143","wmi横向渗透","WMI","wmi横向渗透","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"144","ms17_010横向渗透","ms17_010横向渗透","ms17_010横向渗透","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"145","psexec横向渗透","psexec","psexec横向渗透","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"146","上传文件行为","Upload File","上传文件行为","10","10","10","10","","","0","KNOWLEDGE_BASE","0"
"147","页面报错","页面报错","页面报错","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"148","LOLBins","LOLBins","LOLBins","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"149","phpinfo泄漏","phpinfo泄露","phpinfo泄漏","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"150","ajp协议","ajp协议","ajp协议","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"151","chunked编码","chunked编码","chunked编码","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"152","application/x-www-form-urlencoded","application/x-www-form-urlencoded","application/x-www-form-urlencoded","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"153","multipart/form-data","multipart/form-data","multipart/form-data","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"154","application/json","application/json","application/json","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"155","application/xml","application/xml","application/xml","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"156","application/octet-stream","octet_stream","application/octet-stream","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"157","TLS代理会话","TLS VPN Session","TLS代理会话","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"158","原始Tor会话","Raw Tor Session","原始Tor会话","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"159","Obfs3机制Tor会话","Tor Obfs3 Session","Obfs3机制Tor会话","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"160","挖矿病毒威胁会话","命中挖矿病毒威胁情报IOC","挖矿病毒威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"161","常规木马威胁会话","命中常规木马威胁情报IOC","常规木马威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"162","网银木马威胁会话","命中网银木马威胁情报IOC","网银木马威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"163","网络钓鱼威胁会话","命中网络钓鱼威胁情报IOC","网络钓鱼威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"164","疑似威胁会话","命中疑似威胁情报IOC","疑似威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"165","漏洞利用威胁会话","命中漏洞利用威胁情报IOC","漏洞利用威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"166","间谍木马威胁会话","命中间谍木马威胁情报IOC","间谍木马威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"167","APT攻击威胁会话","命中APT攻击威胁情报IOC","APT攻击威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"168","后门木马威胁会话","命中后门木马威胁情报IOC","后门木马威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"169","勒索软件威胁会话","命中勒索软件威胁情报IOC","勒索软件威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"170","黑客工具威胁会话","命中黑客工具威胁情报IOC","黑客工具威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"171","后门程序威胁会话","命中后门程序威胁情报IOC","后门程序威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"172","蠕虫病毒威胁会话","命中蠕虫病毒威胁情报IOC","蠕虫病毒威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"173","远控木马威胁会话","命中远控木马威胁情报IOC","远控木马威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"174","远控工具威胁会话","命中远控工具威胁情报IOC","远控工具威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"175","恶意下载威胁会话","命中恶意下载威胁情报IOC","恶意下载威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"176","僵尸网络威胁会话","命中僵尸网络威胁情报IOC","僵尸网络威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"177","挖矿软件威胁会话","命中挖矿软件威胁情报IOC","挖矿软件威胁会话","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"178","mssql登录成功","TDS7登录成功标志","mssql登录成功","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"179","mssql-sa用户登录","TDS7协议sa用户登录","mssql-sa用户登录","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"180","mssql密码为sa","TDS7协议登录密码为sa","mssql密码为sa","30","0","30","0","","","0","KNOWLEDGE_BASE","0"
"181","mssql登录失败","TDS7登录失败标志","mssql登录失败","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"182","mssql弱口令","mssql使用弱口令登录且登录成功","mssql弱口令","80","20","80","20","","","0","KNOWLEDGE_BASE","0"
"183","redis未授权访问","redis未使用口令链接","redis未授权访问","60","20","60","20","","","0","KNOWLEDGE_BASE","0"
"184","ES未授权访问","ES未授权访问","ES未授权访问","60","20","60","20","","","0","KNOWLEDGE_BASE","0"
"185","异常HTTP头","http头异常","异常HTTP头","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"186","高端口运行web/TCP服务","高端口运行web/TCP服务","高端口运行web/TCP服务","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"187","ICMP包异常","ICMP包大小异常","ICMP包异常","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"188","无证书","无证书 ","无证书","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"189","同一端口提供不同服务","同一端口提供不同服务","同一端口提供不同服务","40","0","40","0","","","0","KNOWLEDGE_BASE","0"
"190","mysql写入general_log_file","mysql写入general_log_file","mysql写入general_log_file","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"191","Weblogic Console 验证绕过漏洞","Weblogic Console 验证绕过漏洞","Weblogic Console 验证绕过漏洞","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"192","Weblogic JNDI注入","Weblogic JNDI注入","Weblogic JNDI注入","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"193","异常HTTP头字段","Abnormal Http Field","异常HTTP头字段","20","0","20","0","","","0","KNOWLEDGE_BASE","0"
"194","Neoregeo","Neoregeo","Neoregeo","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"195","Authorization认证","Authorization认证","Authorization认证","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"196","Memcache未授权访问","memcache空口令链接","Memcache未授权访问","60","20","60","20","","","0","KNOWLEDGE_BASE","0"
"197","mysql root用户登陆","mysql root用户登陆","mysql root用户登陆","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"198","shiro攻击工具","负载中包含rememberMe=1（Shiro550检测工具特征）","shiro攻击工具","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"199","mysql udf提权","返回包中含6372656174652066756e6374696f6e207379735f6576616c2072657475726e7320737472696e6720736f6e616d652027","mysql udf提权","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"200","mssql命令执行-CLR","mssql clr命令执行","mssql命令执行-CLR","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"201","mssql命令执行-oacreate","mssql oacreate命令执行","mssql命令执行-oacreate","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"202","mssql命令执行-xp_cmdshell","mssql oacreate命令执行","mssql命令执行-xp_cmdshell","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"203","中国菜刀","中国菜刀webshell","中国菜刀","80","0","80","0","","","0","KNOWLEDGE_BASE","0"
"204","pop3弱口令","使用123456等弱口令登陆pop3","pop3弱口令","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"205","pop3登陆失败","pop3登陆失败","pop3登陆失败","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"206","oracle登陆失败","oracle登陆失败","oracle登陆失败","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"207","smtp登陆行为","smtp登陆行为","smtp登陆行为","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"208","域用户名枚举","域用户名枚举","域用户名枚举","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"209","域用户登陆失败","域用户登陆失败","域用户登陆失败","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"210","redis登陆成功","redis登陆成功","redis登陆成功","10","10","10","10","","","0","KNOWLEDGE_BASE","0"
"211","redis登陆失败","redis登陆失败","redis登陆失败","10","10","10","10","","","0","KNOWLEDGE_BASE","0"
"212","redis弱口令","redis使用弱口令且登陆成功","redis弱口令","60","10","60","10","","","0","KNOWLEDGE_BASE","0"
"213","ADwind C&C","ADwind C&C","ADwind C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"214","AKBuilder C&C","AKBuilder C&C","AKBuilder C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"215","AZORult C&C","AZORult C&C","AZORult C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"216","BazaLoader C&C","BazaLoader C&C","BazaLoader C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"217","BumbleBee C&C","BumbleBee C&C","BumbleBee C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"218","COVID19","COVID19","COVID19","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"219","CobaltStrike C&C","CobaltStrike C&C","CobaltStrike C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"220","CoinMiner C&C","CoinMiner C&C","CoinMiner C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"221","Dridex","Dridex","Dridex","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"222","Dridex malware distribution","Dridex malware distribution","Dridex malware distribution","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"223","Gootkit C&C","Gootkit C&C","Gootkit C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"224","Gozi C&C","Gozi C&C","Gozi C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"225","IcedID C&C","IcedID C&C","IcedID C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"226","Loki C&C","Loki C&C","Loki C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"227","Neutrino C&C","Neutrino C&C","Neutrino C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"228","PandaZeuS C&C","PandaZeuS C&C","PandaZeuS C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"229","PsiXBot C&C","PsiXBot C&C","PsiXBot C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"230","Qabkot C&C","Qabkot C&C","Qabkot C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"231","Ransomware C&C","Ransomware C&C","Ransomware C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"232","Ransomware.DarkSide C&C","Ransomware.DarkSide C&C","Ransomware.DarkSide C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"233","RevengeRAT C&C","RevengeRAT C&C","RevengeRAT C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"234","Smoke Loader C&C","Smoke Loader C&C","Smoke Loader C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"235","TrickBot C&C","TrickBot C&C","TrickBot C&C","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"236","Upatre C&C","Upatre C&C","Upatre C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"237","Vawtrak C&C","Vawtrak C&C","Vawtrak C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"238","Vawtrak MITM","Vawtrak MITM","Vawtrak MITM","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"239","ZLoader C&C","ZLoader C&C","ZLoader C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"240","ZeuS C&C","ZeuS C&C","ZeuS C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"241","Adware","Adware","Adware","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"242","AsyncRat","AsyncRat","AsyncRat","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"243","BitRAT","BitRAT","BitRAT","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"244","BumbleBee C&C","BumbleBee C&C","BumbleBee C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"245","Dridex","Dridex","Dridex","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"246","Eris","Eris","Eris","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"247","FRP隧道","FRP隧道","FRP隧道","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"248","GUILDMA","GUILDMA","GUILDMA","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"249","Gootkit","Gootkit","Gootkit","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"250","Guloader C&C","Guloader C&C","Guloader C&C","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"251","Qakbot","Qakbot","Qakbot","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"252","Qbot","Qbot","Qbot","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"253","Revil","Revil","Revil","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"254","Tofsee","Tofsee","Tofsee","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"255","Ursnif","Ursnif","Ursnif","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"256","bokbot","bokbot","bokbot","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"257","emotet","emotet","emotet","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"258","spambot","spambot","spambot","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"259","trickbot","trickbot","trickbot","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"260","DGA通信","DGA通信","DGA通信","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"261","DNS隧道","DNS隧道","DNS隧道","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"262","CobaltStrike控制会话","CobaltStrike控制会话","CobaltStrike控制会话","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"263","msf控制会话","msf控制会话","msf控制会话","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"264","Azorult木马","Azorult木马","Azorult木马","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"265","raccoon stealer","raccoon stealer","raccoon stealer","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"266","挖矿地址","挖矿地址","挖矿地址","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"267","awvs扫描","awvs扫描","awvs扫描","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"268","tor会话","tor会话","tor会话","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"269","挖矿病毒","挖矿病毒","挖矿病毒","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"270","Meterpreter控制会话","Meterpreter控制会话","Meterpreter控制会话","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"271","merlin控制会话","merlin控制会话","merlin控制会话","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"272","awvs扫描器","awvs扫描器","awvs扫描器","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"273","ICMP白负载","ICMP白负载","ICMP白负载","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"274","rdp登录请求","rdp登录请求","rdp登录请求","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"275","rdp登录返回","rdp登录返回","rdp登录返回","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"276","SMB登录请求","SMB登录请求","SMB登录请求","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"277","SMB登录失败","SMB登录失败","SMB登录失败","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"278","oralce连接请求","oralce连接请求","oralce连接请求","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"279","oracle登录失败","oracle登录失败","oracle登录失败","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"280","Pystinger代理隧道","Pystinger代理隧道","Pystinger代理隧道","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"281","MySQL登录失败","MySQL登录失败","MySQL登录失败","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"282","哥斯拉-php","哥斯拉-php","哥斯拉-php","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"283","suo5代理隧道","suo5代理隧道","suo5代理隧道","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"284","冰蝎3","冰蝎3","冰蝎3","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"285","冰蝎4","冰蝎4","冰蝎4","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"286","哥斯拉","哥斯拉","哥斯拉","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"287","antsword中间标签","antsword中间标签","antsword中间标签","45","0","45","0","","","0","KNOWLEDGE_BASE","0"
"288","antSword_PHP自定义编码请求","antSword_PHP自定义编码请求","antSword_PHP自定义编码请求","45","0","45","0","","","0","KNOWLEDGE_BASE","0"
"289","antSword_PHP自定义编码响应","antSword_PHP自定义编码响应","antSword_PHP自定义编码响应","45","0","45","0","","","0","KNOWLEDGE_BASE","0"
"290","访问C&C","AccessC2","访问C&C","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"291","访问DGA域名","AccessDGADomain","访问DGA域名","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"292","访问恶意域名","AccessMaliciousDomain","访问恶意域名","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"293","访问恶意IP","AccessMaliciousIP","访问恶意IP","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"294","访问矿池域名","AccessMiningPoolDomain","访问矿池域名","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"295","暴力破解","BruteForce","暴力破解","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"296","请求C&C域名解析完成","CompleteRequestC2Domain","请求C&C域名解析完成","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"297","分布式拒绝服务攻击","DDOS","分布式拒绝服务攻击","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"298","目录暴力破解","DIRBruteForce","目录暴力破解","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"299","下载木马","DownloadTrojans","下载木马","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"300","FTP暴力破解","FTPBruteForce","FTP暴力破解","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"301","远控恶意代码心跳行为","HeartBeat","远控恶意代码心跳行为","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"302","邮件传播","MailSpread","邮件传播","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"303","上线行为","OnlineC2Response","上线行为","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"304","端口扫描","PortScan","端口扫描","80","0","80","0","","","0","HIGH_DIMENSION_LABEL","0"
"305","下发木马控制指令","TrojanCommand","下发木马控制指令","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"306","WebShell通信","WebShellCommunication","WebShell通信","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"307","漏洞入侵","VulnerabilityIntrusion ","漏洞入侵","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"308","TCP握手缺失","TCP Handshake Missing","TCP握手缺失","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"309","伪造域名","Fake Domain","伪造域名","100","0","100","0","","","0","HIGH_DIMENSION_LABEL","0"
"310","证书碰撞","Certificate Collision","证书碰撞","100","0","100","0","","","0","BASIC_ATTRIBUTES","0"
"311","证书CN伪造","Fake Certificate CN","证书CN伪造","100","0","100","0","","","0","BASIC_ATTRIBUTES","0"
"312","包序列异常","Abnormal Pkt Sequence","包序列异常","100","0","100","0","","","0","BASIC_ATTRIBUTES","0"
"313","不安全加密扩展","Unsafe Encryption Extension","不安全加密扩展","100","0","100","0","","","0","BASIC_ATTRIBUTES","0"
"314","可疑客户端指纹","Suspicious Client SSL Fingerprint","可疑客户端指纹","100","0","100","0","","","0","BASIC_ATTRIBUTES","0"
"315","电商类APP","E-commerce APP","电商类APP","0","0","0","0","","","0","THREAT","0"
"316","娱乐类APP","Entertainment APP","娱乐类APP","0","0","0","0","","","0","THREAT","0"
"317","工具类APP","Tools APP","工具类APP","0","0","0","0","","","0","THREAT","0"
"318","教育类APP","Education APP","教育类APP","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"319","办公类APP","Office APP","办公类APP","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"320","黑名单会话","Black Session","黑名单会话","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"321","关联白名单","White Target Related Session","关联白名单","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"322","关联重点目标","Attention Target Related Session","关联重点目标","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"323","IP分片","IP Shard","IP分片","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"324","TCP启用保留字段","TCP Flag Over Zero","TCP启用保留字段","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"325","TCP-Flag异常","Abnormal Tcp-falg","TCP-Flag异常","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"326","SYN负载","SYN Payload","SYN负载","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"327","SYN-SSL负载","SYN SSL Payload","SYN-SSL负载","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"328","SYN加密负载","SYN Encrypted Payload","SYN加密负载","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"329","TCP握手完整","Completed SYN TCP","TCP握手完整","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"330","TCP复位","TCP RST","TCP复位","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"331","源端SYN无选项","TCP SYN No Option","源端SYN无选项","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"332","目的端SYN无选项","TCP S-ACK No Option","目的端SYN无选项","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"333","IP选项","IP Option","IP选项","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"334","ClientHello无扩展","SSL_C No Extension","ClientHello无扩展","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"335","ServerHello无扩展","SSL_S No Extension","ServerHello无扩展","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"336","TCP最大发送包长不足1千","TCP Src Mac Len <1000","TCP最大发送包长不足1千","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"337","接收最大包长小于500","Max Dst Len <500","接收最大包长小于500","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"338","会话时长>30分钟","Session Duration >30m","会话时长>30分钟","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"339","会话时长>60分钟","Session Duration >1h","会话时长>60分钟","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"340","上行大于下行","Send Over Download","上行大于下行","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"341","流量大于1GB","Total Bytes > 1G","流量大于1GB","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"342","流量大于100MB","Total Bytes > 100M","流量大于100MB","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"343","跨境会话","Global Session","跨境会话","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"344","大端口服务","Big Server Port","大端口服务","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"345","小客户端口","Little Server Port ","小客户端口","10","0","10","0","","","0","KNOWLEDGE_BASE","0"
"346","标准端口","Protocol Match Port ","标准端口","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"347","多会话聚合","Multi Session Cluster","多会话聚合","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"348","单向会话","One Side Session","单向会话","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"349","应用协议变化","APP Protocol Change","应用协议变化","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"350","端口探测","Port Detect","端口探测","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"351","文件传输","File Transfer >1M","文件传输","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"352","大文件传输","File Transfer >100M","大文件传输","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"353","多文件传输","Multi File Transfer","多文件传输","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"354","路由波动","Route Wave","路由波动","40","0","40","0","","","0","KNOWLEDGE_BASE","0"
"355","未知协议","Unknown Protocol","未知协议","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"356","HTTP请求头重复","Duplicate HTTP Header","HTTP请求头重复","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"357","XFF代理","XFF Proxy","XFF代理","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"358","负载格式欺骗","Inconsistent Payload ","负载格式欺骗","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"359","HOST为空","Empty Host","HOST为空","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"360","HOST为IP","Host is IP","HOST为IP","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"361","关联Top1K_HOST","Host Top 1K Related","关联Top1K_HOST","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"362","站内跳转","Inner Link Transfer","站内跳转","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"363","站外跳转","Outer Link Transfer","站外跳转","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"364","手机流量","Mobile Traffic","手机流量","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"365","Linux会话","HTTP Linux UA","Linux会话","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"366","Windows会话","HTTP Windows UA","Windows会话","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"367","OS会话","HTTP OS UA","OS会话","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"368","IOS会话","HTTP IOS UA","IOS会话","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"369","Android会话","HTTP Android UA","Android会话","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"370","存在Cookie","HTTP Cookie Exists","存在Cookie","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"371","HTTP负载分离","HTTP Payload out of Header","HTTP负载分离","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"372","单记录","DNS with Single Log","单记录","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"373","域名链","Dns with CNane","域名链","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"374","多层IP","DNS with Multi IP Answer","多层IP","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"375","解析失败","DNS NXDomain","解析失败","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"376","Top1K域名","DNS Top 1K","Top1K域名","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"377","DNS未知解析类型","DNS Unknown Answer Type","DNS未知解析类型","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"378","UDP协议挂接","Append Data After Protocol","UDP协议挂接","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"379","域名TTL小于60","DNS TTL<60","域名TTL小于60","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"380","域名TTL小于600","DNS TTL<600","域名TTL小于600","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"381","域名TTL小于3600","DNS TTL<3600","域名TTL小于3600","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"382","SNI为空","SSL SNI is Empty","SNI为空","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"383","SNI为IP","SSL SNI is IP","SNI为IP","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"384","关联TOP1K_SNI","SL SNI in Alexa Top 1K","关联TOP1K_SNI","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"385","未知加密套件","Unknown Cipher","未知加密套件","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"386","时间戳集中","Close SSL TimeStmp","时间戳集中","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"387","时间戳分散","Different SSL Timestmp","时间戳分散","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"388","使用SessionID","SSL with SessionId","使用SessionID","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"389","使用SessionTicket","SSL with SessionTicket","使用SessionTicket","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"390","无证书","SSL without Cert","无证书","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"391","单证书","SSL with Single Cert","单证书","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"392","证书链","SSL with Multi Cert","证书链","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"393","常见证书连","SSL Legal Cert","常见证书连","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"394","AlexTop1K证书","SSL Cert in Alexa 1k","AlexTop1K证书","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"395","ALPN为WEB","SSL ALPN Web","ALPN为WEB","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"396","浏览器指纹","SSL Browser Finger","浏览器指纹","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"397","服务器验证证书标记","SSL With Root Cert","服务器验证证书标记","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"398","无TCP分包","SSL Not Split Packet","无TCP分包","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"399","SessionTicket周期1小时","SSL Ticket <1h","SessionTicket周期1小时","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"400","SessionTicket周期1天","SSL Ticket < 1day","SessionTicket周期1天","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"401","SessionTicket周期1周","SSL Ticket < 1week","SessionTicket周期1周","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"402","SessionTicket长周期","SSL Ticket > 1week","SessionTicket长周期","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"403","单个加密套件","Single Cipher","单个加密套件","0","0","0","0","","","0","PROXY","0"
"404","ICMP查询报文","ICMP Query Pkt","ICMP查询报文","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"405","ICMP差错报文","ICMP Accident Pkt","ICMP差错报文","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"406","ICMP目标不可达","ICMP UnAvailable Target","ICMP目标不可达","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"407","ICMP源站抑制","ICMP Origin Reject","ICMP源站抑制","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"408","ICMP重定向","ICMP Redirect","ICMP重定向","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"409","ICMP数据包超时","ICMP Due Time","ICMP数据包超时","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"410","ICMP数据包格式错误","ICMP Pkt Format Error","ICMP数据包格式错误","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"411","ICMP网络查询","ICMP Network Query","ICMP网络查询","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"412","ICMP路由查询","ICMP Route Query","ICMP路由查询","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"413","ICMP时间戳查询","ICMP Timestmp Query","ICMP时间戳查询","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"414","ICMP信息查询","ICMP Info Query","ICMP信息查询","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"415","ICMP掩码查询","ICMP Mask Query","ICMP掩码查询","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"416","ICMP未知类型","ICMP Unknown Type","ICMP未知类型","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"417","上行IPID递增","SRC IPID ADD","上行IPID递增","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"418","上行IPID随机","SRC IPID Random","上行IPID随机","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"419","上行无乱序","SRC ORDER","上行无乱序","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"420","上行少量乱序","SRC RANDOM","上行少量乱序","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"421","上行中度乱序","SRC Medium RANDOM","上行中度乱序","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"422","上行严重乱序","SRC High RANDOM","上行严重乱序","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"423","下行IPID递增","DST IPID ADD","下行IPID递增","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"424","下行IPID随机","DST IPID RANDOM","下行IPID随机","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"425","下行无乱序","DST ORDER","下行无乱序","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"426","下行少量乱序","DST RANDOM","下行少量乱序","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"427","下行中度乱序","DST Medium Random","下行中度乱序","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"428","下行严重乱序","DST High RANDOM","下行严重乱序","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"429","非操作系统分配端口","Not OS Apported Src Port","非操作系统分配端口","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"430","服务器TTL32","Server TTL 32","服务器TTL32","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"431","服务器TTL64","Server TTL 64","服务器TTL64","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"432","服务器TTL128","Server TTL 128","服务器TTL128","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"433","服务器TTL256","Server TTL 256","服务器TTL256","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"434","客户端TTL32","Client TTL 32","客户端TTL32","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"435","客户端TTL64","Client TTL 64","客户端TTL64","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"436","客户端TTL128","Client TTL 128","客户端TTL128","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"437","客户端TTL256","Client TTL 256","客户端TTL256","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"438","ICMP分片","ICMP Fragment","ICMP分片","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"439","ICMP报文大小限制","ICMP MTU Limit","ICMP报文大小限制","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"440","TCP最大接受包长不足1千","TCP Dst Mac Len <1000","TCP最大接受包长不足1千","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"441","发送最大包长小于500","Max Src Len <500","发送最大包长小于500","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"442","非标常见端口","Normal Port no Match Pro ","非标常见端口","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"443","非常见端口","Uncommon Port","非常见端口","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"444","无查询","No Query DNS","无查询","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"445","单次查询","Once Query DNS","单次查询","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"446","2-8次查询","Over Two Query DNS","2-8次查询","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"447","9-16次查询","Over Nine Query DNS","9-16次查询","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"448","多于16次查询","Over Sixteen Query DNS","多于16次查询","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"449","无响应","#ERROR!","无响应","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"450","单次响应","Once Answer DNS","单次响应","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"451","2-8次响应","Over Two Answer DNS","2-8次响应","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"452","9-16次响应","Over Nine Answer DNS","9-16次响应","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"453","多于16次响应","Over Sixteen Answer DNS","多于16次响应","0","0","0","0","","","0","HIGH_DIMENSION_LABEL","0"
"454","DES弱加密算法","DES弱加密算法","DES弱加密算法","0","0","0","0","","","0","BASIC_ATTRIBUTES","0"
"455","RC4弱加密算法","RC4弱加密算法","RC4弱加密算法","0","0","0","0","","","0","BASIC_ATTRIBUTES","0"
"456","Base64加密","Base64加密","Base64加密","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"457","TLS弱加密算法","TLS弱加密算法","TLS弱加密算法","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"458","客户端证书会话","客户端证书会话","客户端证书会话","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"459","目的IP和HOST不一致","目的IP和HOST不一致","目的IP和HOST不一致","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"460","SNI伪造","SNI伪造","SNI伪造","60","0","60","0","","","0","BASIC_ATTRIBUTES","0"
"461","HTTPs会话","HTTPS","HTTPs会话","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"462","SMTPs会话","SMTPS","SMTPs会话","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"463","POP3s会话","POP3S","POP3s会话","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"464","IMAPs会话","IMAPS","IMAPs会话","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"465","FTPs会话","FTPS","FTPs会话","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
"466","FTP匿名访问","FTP ANONYMOUS","FTP匿名访问","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"467","FTP弱口令","FTP Weak Pass","FTP弱口令","60","0","60","0","","","0","KNOWLEDGE_BASE","0"
"468","frp负载0x17","frp payload 0x17","frp负载0x17","100","0","100","0","","","0","KNOWLEDGE_BASE","0"
"469","可疑URL访问","内置了历史出现过漏洞的URL，并使用此知识库对流量进行检测","可疑URL访问","50","0","50","0","","","0","KNOWLEDGE_BASE","0"
"470","Windows命令行标志符","Windows命令行标志符，如：(c) 2013 Microsoft Corporation","Windows命令行标志符","30","0","30","0","","","0","KNOWLEDGE_BASE","0"
"471","测试_上行流量异常","Test Abnormal Upload Packets","测试_上行流量异常","0","0","0","0","","","0","KNOWLEDGE_BASE","0"
