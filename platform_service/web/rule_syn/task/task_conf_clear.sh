#!/bin/bash
cd  /opt/GeekSec/web/rule_syn/task/
PARAM=$1

if [ ! -n "$PARAM" ]; then
    echo "参数不能为空!"
    exit 1
fi


#echo ${#PARAM}

if ((${#PARAM} < 5)); then
    echo "参数长度错误!${#PARAM}"
    exit 1
fi
cd /opt/GeekSec/web/rule_syn/task/
#python3 /opt/GeekSec/web/rule_syn/th_restart/th_stop.py $2 $3
echo "python3 /opt/GeekSec/web/rule_syn/task/task_conf_clear.py $PARAM $2  $3"
python3 /opt/GeekSec/web/rule_syn/task/task_conf_clear.py $PARAM $2  $3
#python3 /opt/GeekSec/web/rule_syn/th_restart/th_start.py $2 $3 
#python3 /opt/GeekSec/web/rule_syn/task/jc_rule_sync.py $2 $3
