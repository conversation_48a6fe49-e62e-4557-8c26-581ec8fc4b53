apiVersion: v1
kind: ConfigMap
metadata:
  name: flink-cert-labels-cdc-config
  namespace: {{ .Values.namespace }}
  labels:
    app: flink-cert-labels-cdc
    component: config
data:
  flink-conf.yaml: |
    # Flink 基础配置
    jobmanager.rpc.address: flink-jobmanager
    jobmanager.rpc.port: 6123
    jobmanager.memory.process.size: 1600m
    taskmanager.memory.process.size: 1728m
    taskmanager.numberOfTaskSlots: 2
    parallelism.default: 2
    
    # 检查点配置
    execution.checkpointing.interval: 60000
    execution.checkpointing.mode: EXACTLY_ONCE
    execution.checkpointing.timeout: 300000
    execution.checkpointing.max-concurrent-checkpoints: 1
    execution.checkpointing.min-pause: 30000
    
    # 状态后端配置
    state.backend: rocksdb
    state.checkpoints.dir: file:///opt/flink/checkpoints
    state.savepoints.dir: file:///opt/flink/savepoints
    
    # 重启策略配置
    restart-strategy: failure-rate
    restart-strategy.failure-rate.max-failures-per-interval: 3
    restart-strategy.failure-rate.failure-rate-interval: 300s
    restart-strategy.failure-rate.delay: 30s
    
    # 网络配置
    taskmanager.network.memory.fraction: 0.1
    taskmanager.network.memory.min: 64mb
    taskmanager.network.memory.max: 1gb
    
  application.yaml: |
    # PostgreSQL CDC 配置
    postgresql:
      hostname: {{ include "nta.postgresqlConfig" . | fromYaml | get "host" }}
      port: {{ include "nta.postgresqlConfig" . | fromYaml | get "port" }}
      database: {{ include "nta.postgresqlConfig" . | fromYaml | get "database" }}
      schema: public
      table-list: public.cert_labels
      username: ${POSTGRESQL_USERNAME}
      password: ${POSTGRESQL_PASSWORD}
      slot-name: cert_labels_cdc_slot
      
    # Doris 配置
    doris:
      fenodes: {{ include "nta.dorisConfig" . | fromYaml | get "feNodes" }}
      table-identifier: nta.dim_cert
      username: ${DORIS_USERNAME}
      password: ${DORIS_PASSWORD}
      batch-size: 1000
      batch-interval-ms: 5000
      max-retries: 3
      
    # 聚合器配置
    aggregator:
      batch-interval-ms: 5000
      state-ttl-ms: 2592000000  # 30天
      
    # 检查点配置
    checkpoint:
      interval: 60000
      timeout: 300000
      min-pause: 30000
      
    # 重启策略配置
    restart:
      failure-rate:
        max-failures: 3
        failure-rate-interval: 300
        delay: 30
        
    # 监控配置
    metrics:
      enabled: true
      reporters: prometheus
      
  log4j2.xml: |
    <?xml version="1.0" encoding="UTF-8"?>
    <Configuration status="WARN">
      <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
          <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%t] %c{1}:%L - %m%n"/>
        </Console>
      </Appenders>
      <Loggers>
        <Logger name="com.geeksec.flink.cert" level="INFO" additivity="false">
          <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="org.apache.flink.cdc" level="INFO" additivity="false">
          <AppenderRef ref="Console"/>
        </Logger>
        <Logger name="org.apache.doris.flink" level="INFO" additivity="false">
          <AppenderRef ref="Console"/>
        </Logger>
        <Root level="WARN">
          <AppenderRef ref="Console"/>
        </Root>
      </Loggers>
    </Configuration>
