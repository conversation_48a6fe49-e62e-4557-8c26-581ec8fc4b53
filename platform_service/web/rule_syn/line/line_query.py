#ne_analyze _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/15 10:53
# 文件名称 : line_syn.py
# 开发工具 : PyCharm
import pymysql.cursors,json
import os,sys
from loadMysqlPasswd import mysql_passwd
passwd =  mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
cursor.execute("select type_name,text,type from tb_line_analyze;")
results = cursor.fetchall()
# 更新启动线路分析mysql的json
lineJson = ''
if results[0]['type'] == 0:
    if os.path.exists("/opt/GeekSec/th/bin/SegmentInfor.txt"):
        f = open("/opt/GeekSec/th/bin/SegmentInfor.txt","r")
        lineJson = f.readline()
        f.close()
        cursor.execute("update tb_line_analyze set text = '" + lineJson + "'")
        db.commit()
# 更新启动网络探针mysql的json
if results[0]['type'] == 1:
    cursor.execute("update tb_line_analyze set text = ''")
    db.commit()
db.close()
