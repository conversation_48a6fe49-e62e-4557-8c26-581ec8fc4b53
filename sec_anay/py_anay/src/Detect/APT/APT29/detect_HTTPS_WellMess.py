import sys
sys.path.append("./")
from Detect.Mining.CheckMineAlarm import insert_nebula_edge
from Detect.APT.APT28.detect_28_Zebrocy import get_cert_from_ES


import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper



def detectWellMessCerts(task_id,batch_id):
    tags = ["证书链缺失","KeyID异常","SAN包含IP","泛域名签发者","长有效期证书"]
    tag_ids = [tag_helper.Tag_Text_Map[tag_text]["Tag_Id"] for tag_text in tags]
    certs = get_cert_from_ES(table="cert_user",tag_ids=tag_ids)
    for cert_hash in certs:
        insert_nebula_edge(cert_hash,"280","CERT")# APT29证书

    if len(certs)==0:
        return
    # 打会话标签
    table,detail = eh.get_basic_query(es_type="ssl",batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"terms": {"sCertHash.keyword":certs}})     
    result = eh.load_es_data_to_DF(table,detail=detail)
    if result is not None and result.shape[0]>0:
        result.apply(lambda x : tag_helper.add_tag_for_session("APT29 加密控制会话",x),axis=1)        

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detectWellMessCerts(task_id,batch_id)
