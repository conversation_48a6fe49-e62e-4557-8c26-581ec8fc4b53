##
# @file hours_bytes_pkt.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2021-08-04
from elasticsearch import Elasticsearch
from datetime import datetime, date, timedelta
import json

task_id = "1"
if len(sys.argv) != 1:
    print("参数错误, 请输入任务ID")
    task_id = sys.argv[1]
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])
### 获取前一天的索引
### 
yesterday = (date.today() + timedelta(days = -1)).strftime("%Y%m%d")
index_name = "connectinfo_"+task_id+"_*_"+yesterday
#print(index_name)
body_ar = {
  "query": {
    "bool": {
      "must": []
    }
  },
  "aggs": {
    "prices": {
      "histogram": {
        "field": "StartTime",
        "interval": 3600,
        "order": {
          "_key": "desc"
        }
      },
      "aggs": {
        "sumPK": {
          "sum": {
            "field": "SumPkt"
          }
        }
      }
    }
  },"size":0
}
batch_flow_hit ={}
result = es.search(index=index_name,body=body_ar)
pkt_row  = result["aggregations"]["prices"]["buckets"]
for row in pkt_row :
    s_time = str(int(int(row["key"]/3600)%24))
    sbytes = str(int(row["sumPK"]["value"])) 
    if s_time not in  batch_flow_hit:
        batch_flow_hit[s_time]= {"connect_num":int(sbytes),"time_hour":int(s_time)}
    else :
        batch_flow_hit[s_time]["connect_num"] = int(sbytes) + batch_flow_hit[s_time]["connect_num"]


body_ar = {
  "query": {
    "bool": {
      "must": []
    }
  },
  "aggs": {
    "prices": {
      "histogram": {
        "field": "StartTime",
        "interval": 3600,
        "order": {
          "_key": "desc"
        }
      },
      "aggs": {
        "sumPK": {
          "sum": {
            "field": "SumBytes"
          }
        }
      }
    }
  },"size":0
}
result = es.search(index=index_name,body=body_ar)
pkt_row  = result["aggregations"]["prices"]["buckets"]
for row in pkt_row :
    s_time = str(int(int(row["key"]/3600)%24))
    sbytes = str(int(row["sumPK"]["value"])) 
    if s_time not in  batch_flow_hit:
        batch_flow_hit[s_time]= {"bytes":int(sbytes),"time_hour":int(s_time)}
    else :
        if  "bytes" not in  batch_flow_hit[s_time] :
            batch_flow_hit[s_time]["bytes"] = int(sbytes) 
        else:
            batch_flow_hit[s_time]["bytes"] = int(sbytes) + batch_flow_hit[s_time]["bytes"]
path = "/data/File/"+task_id+"batch_flow_hit"
json.dump(batch_flow_hit ,open(path,"w+"))
print(batch_flow_hit)
