package com.geeksec.alarm.processor.pipeline.processor.impl;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.alarm.processor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 隐蔽隧道告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CovertTunnelProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.COVERT_TUNNEL;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        String tunnelType = getTunnelType(alarm);
        String tunnelProtocol = getTunnelProtocol(alarm);
        
        // 隐蔽隧道通信检测
        reasons.add(createDetectionReason(
                "隐蔽隧道通信",
                String.format("检测到%s类型的隐蔽隧道通信", tunnelType),
                "正常协议通信",
                String.format("隧道协议: %s", tunnelProtocol),
                "标准协议通信",
                8
        ));
        
        // 协议异常分析
        if (hasProtocolAnomaly(alarm)) {
            reasons.add(createDetectionReason(
                    "协议异常特征",
                    "检测到协议使用异常，可能被用于隧道传输",
                    "正常协议使用",
                    getProtocolAnomalyDescription(alarm),
                    "标准协议行为",
                    7
            ));
        }
        
        // 数据包特征分析
        if (hasPacketAnomaly(alarm)) {
            reasons.add(createDetectionReason(
                    "数据包异常特征",
                    "数据包大小、频率等特征异常",
                    "正常数据包特征",
                    getPacketAnomalyDescription(alarm),
                    "标准数据包模式",
                    6
            ));
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        String tunnelType = getTunnelType(alarm);
        String tunnelProtocol = getTunnelProtocol(alarm);
        
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        String.format("立即阻断%s隧道通信", tunnelType),
                        String.format("分析%s协议的异常使用", tunnelProtocol),
                        "检查隧道两端的主机安全状态",
                        "监控相关IP的后续活动"
                ))
                .investigationSteps(Arrays.asList(
                        "确认隧道的建立目的和传输内容",
                        "分析隧道建立的时间和触发条件",
                        "检查是否有数据泄露或恶意传输",
                        "确认隧道建立者的身份和权限",
                        "分析隧道技术的复杂程度"
                ))
                .preventionMeasures(Arrays.asList(
                        "加强对异常协议使用的检测",
                        "更新深度包检测(DPI)规则",
                        "实施协议白名单策略",
                        "加强网络流量基线监控",
                        "定期进行隧道检测技术更新"
                ))
                .recoverySteps(Arrays.asList(
                        "关闭所有未授权的隧道连接",
                        "清理隧道相关的恶意软件和工具",
                        "重新配置网络安全策略和防火墙规则",
                        "修复被利用的协议漏洞",
                        "恢复正常的网络通信路径",
                        "加强对隧道协议的监控和审计"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        String tunnelType = getTunnelType(alarm);
        String tunnelProtocol = getTunnelProtocol(alarm);
        return String.format("攻击者利用%s协议建立%s隐蔽隧道，" +
                "绕过网络安全设备的检测，进行数据传输或建立持久化通信通道。", 
                tunnelProtocol, tunnelType);
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "通过深度包检测技术分析协议使用模式、数据包特征、通信频率等指标，" +
                "识别被滥用于隧道传输的正常协议。";
    }
    
    @Override
    public List<Map<String, Object>> generateAttackRoute(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Map<String, Object>> attackRoute = new ArrayList<>();
        
        // 隧道建立阶段
        Map<String, Object> step1 = new HashMap<>();
        step1.put("step", 1);
        step1.put("action", "隧道建立");
        step1.put("description", String.format("在%s和%s之间建立隐蔽隧道", 
                alarm.getSrcIp(), alarm.getDstIp()));
        step1.put("timestamp", alarm.getEventTimestamp());
        attackRoute.add(step1);
        
        // 数据传输阶段
        Map<String, Object> step2 = new HashMap<>();
        step2.put("step", 2);
        step2.put("action", "数据传输");
        step2.put("description", "通过隧道进行隐蔽数据传输");
        step2.put("protocol", getTunnelProtocol(alarm));
        attackRoute.add(step2);
        
        return attackRoute;
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99010";
    }
    
    @Override
    public boolean requiresSpecialHandling(Alarm alarm) {
        return true; // 隐蔽隧道需要特殊处理
    }
    
    /**
     * 获取隧道类型
     */
    private String getTunnelType(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object tunnelType = alarm.getExtendedProperties().get("tunnel_type");
            if (tunnelType != null) {
                return tunnelType.toString();
            }
        }
        return "未知隧道";
    }
    
    /**
     * 获取隧道协议
     */
    private String getTunnelProtocol(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object protocol = alarm.getExtendedProperties().get("tunnel_protocol");
            if (protocol != null) {
                return protocol.toString();
            }
        }
        return alarm.getProtocol() != null ? alarm.getProtocol() : "未知协议";
    }
    
    /**
     * 检查是否有协议异常
     */
    private boolean hasProtocolAnomaly(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object anomaly = alarm.getExtendedProperties().get("has_protocol_anomaly");
            if (anomaly instanceof Boolean) {
                return (Boolean) anomaly;
            }
        }
        return false;
    }
    
    /**
     * 获取协议异常描述
     */
    private String getProtocolAnomalyDescription(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object description = alarm.getExtendedProperties().get("protocol_anomaly_description");
            if (description != null) {
                return description.toString();
            }
        }
        return "协议使用异常";
    }
    
    /**
     * 检查是否有数据包异常
     */
    private boolean hasPacketAnomaly(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object anomaly = alarm.getExtendedProperties().get("has_packet_anomaly");
            if (anomaly instanceof Boolean) {
                return (Boolean) anomaly;
            }
        }
        return false;
    }
    
    /**
     * 获取数据包异常描述
     */
    private String getPacketAnomalyDescription(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object description = alarm.getExtendedProperties().get("packet_anomaly_description");
            if (description != null) {
                return description.toString();
            }
        }
        return "数据包特征异常";
    }
}
