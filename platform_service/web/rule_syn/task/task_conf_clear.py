# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : task_conf_clear.py
# 开发工具 : PyCharm
import sys,os,pymysql.cursors,xml.etree.ElementTree as ET ,json,time
# 写文件函数
def write_file(filename , context):
    os.system("echo \"\">"+filename)
    fnew = open(filename, "w")
    fnew.write(context)
    fnew.close()
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def get_dir(cmd,arr):
    p = os.popen(cmd)
    lines = p.readlines()
    for line in lines:
        arr.append(line.strip('\n'))
    return arr
param = sys.argv[1]
task_id  =  int(sys.argv[2])
batch_id =   int(sys.argv[3])
def task_mkdir(path):
    os.system("mkdir -p "+ path + "/db")
    os.system("mkdir -p "+ path + "/conf")
    os.system("mkdir -p "+ path + "/Config")
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
if task_id == 0  or task_id == 1:
    task_path =  "/opt/GeekSec/th/bin/conf/"+str(task_id)+ "/"
    task_mkdir(task_path)
else:
    task_path = "/opt/GeekSec/task/"+str(task_id)+"/"+str(batch_id)+"/conf/"
    task_mkdir(task_path)
    #os.system("mkdir -p "+ task_path)
# 获取参数
time_s = str(time.time()).split('.')[0]
isDelExists = os.path.exists("/data/del/")
if not isDelExists:
    os.makedirs("/data/del/")
base_json = {}
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
print(passwd)
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
#db = pymysql.connect(host='localhost',port=base_json["db_port"],user='root',password='root',db='statistical_information_base',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    if base_json["db_port"] != base_json["tidb_port"] :
         db2 = pymysql.connect(host=["tidb_host"],port=base_json["db_port"],user='root',password=passwd,db='push_database',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    else:
        db2 = db
cursor = db.cursor()
cursor2 = db2.cursor()
#if base_json['system'] == 'th':
#    os.system("python3 /opt/GeekSec/web/rule_syn/import_th_task.py")
# 10000 代表清理清理pcap文件
if param[0:1] == '1' or param[2:3] == '1':
    os.makedirs("/data/del/pcapfiles" + time_s + "/")
    dir_arr_pcap = get_dir("ls -l /data/pcapfiles/* | grep ftp |awk '{print $NF}'", [])
    if param[2:3] == '1':   
        if base_json["system"] == "an" :
           idu_mysql("truncate tb_rule;", cursor, db)
           idu_mysql("truncate tb_forensics_white_list;", cursor, db)
           idu_mysql("truncate tb_rule_lib_config;", cursor, db)
           idu_mysql("truncate tb_rule_statistic_info;", cursor, db)
           idu_mysql("delete from tb_rule_info where rule_id > 35000;", cursor, db)
           idu_mysql("delete from tb_alarm where tag_id > 35000;", cursor, db)
           #idu_mysql("truncate tb_alarm;", cursor, db)
           os.system("rm -f /opt/GeekSec/th/bin/LibConfig/* & rm -f /opt/GeekSec/th/bin/JsonRule/BasicRule/LibFolder/* & rm -f /opt/GeekSec/web/rule_syn/rule.json && touch /opt/GeekSec/web/rule_syn/rule.json & rm -f /opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/rule.json && touch /opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/rule.json")
           if len(dir_arr_pcap) > 0:
               dir_list = []
               for row in dir_arr_pcap:
                    dir_s = row.split("pcapfiles")[0]
                    if dir_s not in dir_list:
                        dir_e = dir_s + "del/pcapfiles" + time_s + str(row.split("pcapfiles")[0][8:])
                        os.makedirs(dir_e)
                        os.system("mv " + dir_s + "pcapfiles/* " + dir_e)
                        dir_list.append(dir_s)
                        os.system(
                             "mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ " + dir_s + "del/ &")
                        os.system(
                             "mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ " + dir_s + "pcapfiles_his/ &")
               os.system("rm -rf /data/pcapfiles/*")
        if base_json["system"] == "th" : 
           idu_mysql("update tb_rule set total_sum_bytes = 0 , last_size_time = 0  ,  rule_size = 0;", cursor, db)
        tree_fullflow = ET.parse(task_path +'/write_pcap.xml')
        root_fullflow = tree_fullflow.getroot()
        if root_fullflow.find('b_rule_save').text == "true":
                dir_e = "/data/"
                os.system("mkdir -p "+dir_e+"+pcapfiles_his/  && mv /data/pcapfiles/* " + dir_e+"pcapfiles_his/")
                os.system(
                       "mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ " + dir_s + "pcapfiles_his/ &")
              
    if param[0:1] == '1' and param[2:3] == '0':
        idu_mysql("truncate tb_rule_statistic_info;", cursor, db)
        #du_mysql("update tb_rule set total_sum_bytes = 0,last_size_time = NULL;", cursor, db)
        if len(dir_arr_pcap) > 0:
            for row in dir_arr_pcap:
                dir_e = row.split("pcapfiles")[0] + "del/pcapfiles" + row.split("pcapfiles")[1] + "/"
                os.makedirs(dir_e)
                os.system("mv " + row + "/* " + dir_e)
                os.system("rm -rf /data/pcapfiles" + row.split("pcapfiles")[1] + "/*")
                os.system(
                    "mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ " + row.split("pcapfiles")[0] + "del/ &")
                os.system(
                    "mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ " + row.split("pcapfiles")[0] + "pcapfiles_his/ &")
        else:
            os.system("mv /data/pcapfiles/* /data/del/pcapfiles" + time_s + "/")
    if param[0:1] == '1':
        os.system("mv /data/pcapfiles/* /data/del/pcapfiles" + time_s + "/")
        os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /data/del/pcapfiles  &")
    os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /data/pcapfiles_his/ &")
# 01000 代表清理日志文件
if param[1:2] == '1' or param[3:4] == '1':
    if param[3:4] == '1':
        idu_mysql("truncate cert_DN_info;", cursor, db)
        idu_mysql("truncate cert_DN_to_sha1;", cursor, db)
        idu_mysql("truncate tb_cert_info;", cursor, db)
        idu_mysql("truncate cert_sha1_to_own_id;", cursor, db)
        idu_mysql("truncate cert_subject_key_identifier;", cursor, db)
        idu_mysql("truncate tb_user_analysis_target;", cursor, db)
        idu_mysql("truncate tb_user_analysis_default_report;", cursor, db)
        idu_mysql("truncate tb_user_analysis_report;", cursor, db)
        idu_mysql("truncate tb_dns_server;", cursor, db)
        idu_mysql("truncate tb_model;", cursor, db)
        idu_mysql("truncate tb_system_fifter;", cursor2, db2)
        write_file("/opt/GeekSec/th/bin/conf/cert_config.json",
                   '{"whiteListFlow": 0, "blackListLog": 1, "whiteListLog": 1, "blackListFlow": 1}')
        idu_mysql("truncate tb_log_plug;", cursor, db)
        if param[1:2] == '1':
            os.system(
                "rm -f /opt/GeekSec/th/bin/plugin/msg_handle/user/* & rm -f /opt/GeekSec/web/rule_syn/certbwlist.json && touch /opt/GeekSec/web/rule_syn/certbwlist.json & rm -f /opt/GeekSec/th/bin/conf/certbwlist.json && touch /opt/GeekSec/th/bin/conf/certbwlist.json")
            dir_arr_pb = get_dir("ls -l /data/pbfiles/* | grep ftp |awk '{print $NF}'", [])
            if len(dir_arr_pb) > 0:
                for row in dir_arr_pb:
                    dir_e = row.split("pbfiles")[0] + "del/pbfiles" + row.split("pbfiles")[1] + "/"
                    os.makedirs(dir_e)
                    os.system("mv " + row + "/* " + dir_e)
                    os.system("rm -rf /data/pbfiles" + row.split("pbfiles")[1] + "/*")
                    os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ " + row.split("pbfiles")[0] + "del/ &")
    else:
        os.makedirs("/data/del/pbfiles" + time_s + "/")
        os.system("mv /data/pbfiles/* /data/del/pbfiles" + time_s + "/")
        dir_arr_cer = get_dir("ls -l /data/cerfiles/* | grep ftp |awk '{print $NF}'", [])
        if len(dir_arr_cer) > 0:
            for row in dir_arr_cer:
                dir_e = row.split("cerfiles")[0] + "del/cerfiles" + row.split("cerfiles")[1] + "/"
                os.makedirs(dir_e)
                os.system("mv " + row + "/* " + dir_e)
                os.system("rm -rf /data/cerfiles" + row.split("cerfiles")[1] + "/*")
                os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ " + row.split("cerfiles")[0] + "del/ &")
        else:
            os.makedirs("/data/del/cerfiles" + time_s + "/")
            os.system("mv /data/cerfiles/* /data/del/cerfiles" + time_s + "/")
        os.system("mkdir -p /tmp/empty; rm -rf /tmp/empty/*; rsync --delete-before -r /tmp/empty/ /data/del/ &")
# 00001 代表同步任务id 同步功能状态
if param[4:5] == '1':
    #results = s_mysql("select * from tb_task_batch where batch_id =  " + str(batch_id), cursor)
    results = s_mysql("select a.fullflow_state , a.flowlog_state , b.task_id , b.task_name , \
             a.begin_time  ,b.task_remark , a.avg_byte_pt_ps,a.max_byte_pt_ps ,a.topology_state \
             from tb_task_batch a , tb_task_analysis b where b.task_id = a.task_id and a.batch_id = " + str(batch_id) , cursor)
    print("********" , results)
    if len(results) > 0:
       # 全流量留存
       tree_fullflow = ET.parse(task_path + 'write_pcap.xml')
       root_fullflow = tree_fullflow.getroot()
       row = results[0]
       for elem in root_fullflow.iter('b_rule_save'):
          if results[0]['fullflow_state'] == 'ON':
             elem.text = 'false'
          if results[0]['fullflow_state'] == 'OFF':
             elem.text = 'true'
       for elem in root_fullflow.iter('avg_byte_pt_ps'):
           if row['avg_byte_pt_ps'] == 0:
                elem.text = str(51200000)
           else:
                elem.text = str(row['avg_byte_pt_ps'])
       for elem in root_fullflow.iter('max_byte_pt_ps'):
           if row['max_byte_pt_ps'] == 0:
              elem.text = str(51200000)
           else:
              elem.text = str(row['max_byte_pt_ps'])
       # ******
       tree_fullflow.write(task_path +'/write_pcap.xml')
       tree_all_session = ET.parse(task_path + 'all_session.xml')  

       all_session = tree_all_session.getroot()
       for elem in all_session.iter('b_mac_statistics'):
           if row['topology_state'] == 'ON':
               elem.text = 'true'
           if row['topology_state'] == 'OFF':
              elem.text = 'false'
       tree_all_session.write(task_path   + '/all_session.xml')
            # 流量日志留存、任务ID
       tree_flow_task = ET.parse(task_path + '/th_engine_conf.xml')
       root_flow_task = tree_flow_task.getroot()
       # 任务ID
       for elem in root_flow_task.iter('task_id'):
           elem.text = str(batch_id)
       # 流量日志留存
       for elem in root_flow_task.iter('b_save_pb_file'):
           if results[0]['flowlog_state'] == 'ON':
               elem.find('key').text = 'true'
           if results[0]['flowlog_state'] == 'OFF':
               elem.find('key').text = 'false'
       tree_flow_task.write(task_path + '/th_engine_conf.xml')
       # 同步任务json
       task_context = {}
       if True:
           task_context["MissionId"] = results[0]["task_id"]
           task_context["Mission"] = results[0]["task_name"]
           task_context["Addr"] = ""
           task_context["Time"] = time.strftime("%Y-%m-%d %H:%M:%S",time.localtime(results[0]["begin_time"]))
           task_context["Infor"] = results[0]["task_remark"]
       os.system("echo \"\">taskinfo.json")
       write_file("taskinfo.json",json.dumps(task_context))
       if  True:
            isExists = os.path.exists(task_path + "/Task/")
            if not isExists:
                os.makedirs(task_path + "/Task/")
                os.system("cp -rf taskinfo.json "+task_path+"/taskinfo.json")
            if True: 
                  isExists_Task = os.path.exists("/opt/GeekSec/STL/ExportData/bin/script/taskinfo_parse.py")
                  if isExists_Task:
                        os.system("python3 /opt/GeekSec/STL/ExportData/bin/script/taskinfo_parse.py /opt/GeekSec/th/bin/Task/taskinfo.json >/dev/null")
       #else:
# 000001 代表同步(DDOS防御、链路防御、过滤筛选)
if param[5:6] == '1':
    config_str = '{"Basic":{"DeviceNO":"123-456","Mission":"testn","OfflineFolder":"./OfflineFolder","FirstPro":12},"PFRing":{"Interface":["wlp3s0"],"ThreadNum":2,"RenewInterval_Sec":120,"CPU":[1,2,3,4],"BalanceCore":11},"Connect":{"IsIP":1,"IsUDP":1,"SYNSign":0},"ConnectInfor":{"ConnectNum_IPv4":10000,"ConnectNum_IPv6":1232,"TimeInterval":90},"LanProbe":{"FirstPro":[10,12,113,277],"SaveType_Pcap":1,"StartTime":"2018-01-19 16:51:00","LogFolder":"./FlowLog","RuleFolder":"./JsonRule/BasicRule","EngineFolder":"./JsonRule/BasicEngine","SaveFolder":"./PcapFolder","SaveFlow":0,"SaveFirstPacket":1,"RuleLog_Folder":"./FlowLog/RuleLog","RuleLog_MinLevel":20},"TileraFrame":{"PageNum":1,"rx":["eth0","eth1"],"GroupNumOfEachLink":8,"BucketNumOfEachLink":8,"RingNumOfEachLink":8,"BucketMode":1,"NodeOfEachRing":65536},"Extract_HTTP_Title":["^Sec-WebSocket","^Get","^Post","^HTTP","^Referer","^User-","^Server","^Date","^Cookie","^Host","^Last-Modified","^Expires","^Content","^Connect","^Accept","^Access","^Origin","^x-xss","^x-content","^x-frame","^strict-transport","^public-key","^Age","^ETag","^Location","^Proxy","^Retry","^Vary","^www-Auth","^upgrade"],"Extract":{"LogFolder":"./FlowLog/Extract","PcapFolder":"./PcapFolder/Extract_AbnormalPacket","CertFolder":"./FlowLog/Cert","BlockNum":1024,"IsConnect":1,"IsDNSQuery":1,"IsDNS":1,"IsHTTP":1,"IsTSL":1},"ProtocolAnalyse":{"SaveFolder":"./PcapFolder/ProAnalyse","TLS_HandShake":1,"AbnormalPro":1,"AbnormalIPTTL":5},"KeepOrder":{"PacketNum":10},"Port53":{"IPNum":50000,"Folder":"./FlowLog/Port53","TimeInterval":86400},"BytesDistribute":{"MaxBytes":2147483648,"MinBytes":2048,"Duration":60},"PlusIn_Json":{},"IPStatistic":{"VIP":[],"Network":[],"DMZ":[],"SaveFolder":"./PcapFolder","MacNum":1000,"IPv4Num":10000,"IPv6Num":5000,"TimeInterval_IPClear":1600,"TimeInterval_IPInfor":120,"SYNSample":1048575,"DDOS":{"LogFolder":"./FlowLog/DDOS_Log","TimeInterval_Judge":30,"Threshold_Packet":1000,"Times_SYN_Judge":4,"Times_Connect_Judge":4,"Hour_BasicLine_Judge":24,"Times_BasicLine_Judge":10},"IntraIPNum":0,"MacNum_Extra":0},"DDOS_Defense":{"Type":4,"RenewTimeInterval":30,"IntraIPv4":[{"IP":"***********","Mask":"***********"}],"IntraMac":[],"IPv4Num":200000,"IPv6Num":5000,"DDOS":{"TimeInterval_Judge":6,"Times_TCP":20,"Times_DNS":20,"Times_ICMP":20,"Times_UDP":20,"mbps_Basic":10,"PacketNum_Basic":1000}}}'
    config_json = json.loads(config_str)
    #config_json = {}
    # 链路防御
    results_link_str  = s_mysql("select network_param_json  from tb_network_state   where state = 1 and task_id = " + str(task_id), cursor)
    print( "results_link_str" , results_link_str )
    if len(results_link_str) > 0:
        if len(results_link_str)>0:
            link_defense_json = json.loads(results_link_str[0]["network_param_json"])
            results_link_defense_param = s_mysql("select network_json  from tb_network_config   where task_id = " + str(task_id), cursor)
            if results_link_defense_param is None:
                results_link_defense_param = {}
            link_defense_macinfo = []
            for istr in results_link_defense_param:
                link_defense_macinfo.append(json.loads(istr["network_json"]))
            link_defense_json['MacInfor'] = link_defense_macinfo
            print(link_defense_json)
            config_json['MacDefense'] = link_defense_json

    # ddos 配置 
    config_json['IPDefense'] =  {}
    ipInfor = []
    results_ddos_defense_param_str = s_mysql("select ddos_param_json from tb_ddos_state where state = 1 and  task_id = "+str(task_id),cursor)
    print("results_ddos_defense_param_str" ,results_ddos_defense_param_str )
    if len(results_ddos_defense_param_str) > 0:
        results_ddos_paramm_str = s_mysql(" SELECT  doss_json  FROM th_analysis.tb_ddos_config where state = 1 and   task_id = "+str(task_id),cursor)
        print( results_ddos_paramm_str)
        for iparam in results_ddos_paramm_str :
            resultsparamm = json.loads(iparam['doss_json'])
            print(resultsparamm)
            ipInfor.append( resultsparamm)
        results_ddos_defense_param = json.loads(results_ddos_defense_param_str[0]['ddos_param_json'])
        config_json['IPDefense'] =  results_ddos_defense_param
    sql_t  ="select inter_ip, ip_mask from th_analysis.tb_internal_net where inter_ip <> '' and task_id = " +str(task_id)
    print(sql_t)
    ip_net_list = s_mysql (sql_t,cursor)   
    print(ip_net_list)
    for row in  ip_net_list :
         ip_in = {"IP": row["inter_ip"] , "Mask":row["ip_mask"]  , "Type":["IntraIP"] }
         ipInfor.append(ip_in)
    config_json['IPDefense']["IPInfor"] = ipInfor
    print(config_json['IPDefense'])
    '''
    results_ddos_defense_state = s_mysql("select state from tb_defense_state where type_name = 'DDOS_DEFENSE';", cursor)[0]['state']
    results_ddos_defense_param = s_mysql("select * from tb_ddos_defense_param where type = 'DDOS_DEFENSE';",cursor)[0]
    results_ddos_defense = s_mysql("select * from tb_ddos_defense;", cursor)
    if results_ddos_defense_state == 1:
        ddos_defense_json = {}
        ddos_type = []
        if results_ddos_defense_param['intra_ip'] == 1:
            ddos_type.append("IntraIP")
        if results_ddos_defense_param['legal_ip'] == 1:
            ddos_type.append("LegalIP")
        if results_ddos_defense_param['all_ip'] == 1:
            ddos_type.append("All")
        ddos_ip_info = []
        if len(results_ddos_defense) > 0:
            for row in results_ddos_defense:
                ddos_ip_info_obj = {}
                ddos_ip_info_obj['IP'] = row['ip']
                ddos_ip_info_obj['Mask'] = row['mask']
                ddos_ip_info_obj_type = []
                if row['type'] == 3:
                    ddos_ip_info_obj_type.append("IntraIP")
                    ddos_ip_info_obj_type.append("LegalIP")
                if row['type'] == 2:
                    ddos_ip_info_obj_type.append("LegalIP")
                if row['type'] == 1:
                    ddos_ip_info_obj_type.append("IntraIP")
                ddos_ip_info_obj['Type'] = ddos_ip_info_obj_type
                ddos_ip_info.append(ddos_ip_info_obj)
        ddos_param = {}
        ddos_param['TimeInterval_Judge'] = results_ddos_defense_param['time_interval_judge']
        ddos_param['BasicLine_bps'] = results_ddos_defense_param['basic_line_bps']
        ddos_param['BasicLine_PacketNum'] = results_ddos_defense_param['basic_line_packet_num']
        ddos_param['CheckSum'] = []
        ddos_param['CheckSum'].append(results_ddos_defense_param['check_sum_ip'])
        ddos_param['CheckSum'].append(results_ddos_defense_param['check_sum_udp'])
        ddos_param['DDOS_CheckSum'] = []
        ddos_param['DDOS_CheckSum'].append(results_ddos_defense_param['ddos_check_sum_ip'])
        ddos_param['DDOS_CheckSum'].append(results_ddos_defense_param['ddos_check_sum_udp'])
        ddos_param['DDOS_SYN'] = results_ddos_defense_param['ddos_syn']
        ddos_param['DDOS_FIN'] = results_ddos_defense_param['ddos_fin']
        ddos_param['DDOS_DNS'] = results_ddos_defense_param['ddos_dns']
        ddos_param['DDOS_ICMP'] = results_ddos_defense_param['ddos_icmp']
        ddos_param['DDOS_IGMP'] = results_ddos_defense_param['ddos_igmp']
        ddos_param['DDOS_UDP'] = results_ddos_defense_param['ddos_udp']
        ddos_param['DDOS_Frag'] = results_ddos_defense_param['ddos_frag']
        ddos_param['DDOS_Multicast'] = results_ddos_defense_param['ddos_multicast']
        ddos_param['MaxOffset_IP'] = results_ddos_defense_param['max_offset_ip']
        ddos_defense_json['Type'] = ddos_type
        ddos_defense_json['IPv4Num'] = results_ddos_defense_param['ipv4_num']
        ddos_defense_json['IPv6Num'] = results_ddos_defense_param['ipv6_num']
        ddos_defense_json['RenewTime'] = results_ddos_defense_param['renew_time']
        ddos_defense_json['IPInfor'] = ddos_ip_info
        ddos_defense_json['Param'] = ddos_param
        '''
    # 过滤
    filter_json = {}
    results_filter_context = []
    f_state = ""
    results_filter = s_mysql("select b.id , b.ip ,a.state , b.filter_json from  tb_filter_state a , tb_filter_config b where b.status = 1 and  a.task_id = b.task_id and b.task_id = " + str(task_id),cursor)
    print(results_filter)
    if len(results_filter) == 0 :
        filter_state = s_mysql("select state from tb_filter_state where task_id = " + str(task_id),cursor)
        if filter_state[0]["state"] == 0:
            f_state = "pass"
        else :
            f_state = "drop"
        config_json['Filter']={"Respond":f_state,"Rule":[]}
    else :
        if results_filter[0]["state"] == 0:
            f_state = "pass"
        else :
            f_state = "drop"
        filter_json['Respond'] = f_state
        for i in  results_filter :
            filter_rule_json = json.loads(i['filter_json']) 
            filter_rule_json['ID'] = i['id']
            if "ip" in filter_rule_json  and  filter_rule_json["ip"] == "":
                del  filter_rule_json["ip"]
            results_filter_context.append(filter_rule_json)
        filter_json['Rule'] = results_filter_context
        config_json['Filter'] = filter_json
    #print(json.dumps(config_json))
    os.system("echo \"\">Config.txt")
    write_file("Config.txt", json.dumps(config_json))
    os.system("cp -rf Config.txt "+task_path+"/Config/Config.txt")
db.close()
