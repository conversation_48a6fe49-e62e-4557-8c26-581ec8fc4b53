{{- if .Values.services.cert-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: cert-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    # 证书服务配置

    # 服务器配置
    server:
      port: 8092
      servlet:
        context-path: /cert

    # 应用名称
    spring:
      application:
        name: cert-service

      # 主数据源：Doris - 存储证书只读数据
      datasource:
        primary:
          url: jdbc:mysql://{{ include "nta.dorisConfig" . | fromYaml | get "feNodes" | split "," | first | split ":" | first }}:9030/nta?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
          username: ${DORIS_USERNAME}
          password: ${DORIS_PASSWORD}
          driver-class-name: com.mysql.cj.jdbc.Driver
        # 辅助数据源：PostgreSQL - 存储证书标签数据
        secondary:
          url: jdbc:postgresql://{{ include "nta.postgresqlConfig" . | fromYaml | get "host" }}:{{ include "nta.postgresqlConfig" . | fromYaml | get "port" }}/{{ include "nta.postgresqlConfig" . | fromYaml | get "database" }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
          username: ${DB_USERNAME}
          password: ${DB_PASSWORD}
          driver-class-name: org.postgresql.Driver

      # 文件上传配置
      servlet:
        multipart:
          max-file-size: 10MB
          max-request-size: 50MB

    # MyBatis类型别名包配置
    mybatis-plus:
      type-aliases-package: com.geeksec.cert.entity

    # 数据存储配置
    storage:
      # Doris配置 - 存储证书只读数据
      doris:
        cert-table: dim_cert
        batch-size: 1000
        batch-timeout: 30000
      # PostgreSQL配置 - 存储证书标签关联数据
      postgresql:
        cert-labels-table: cert_labels  # 证书标签关联表
        labels-table: labels            # 统一标签表
        batch-size: 100

    # MinIO配置
    minio:
      endpoint: ${MINIO_ENDPOINT}
      access-key: ${MINIO_ACCESS_KEY}
      secret-key: ${MINIO_SECRET_KEY}
      bucket: ${MINIO_BUCKET}
      # 证书文件存储路径前缀
      path-prefix: certificates/

    # 证书服务特定配置
    certificate:
      # 支持的证书格式
      supported-formats:
        - pem
        - der
        - p12
        - pfx
        - crt
        - cer
      # 证书解析配置
      parsing:
        # 是否提取证书链
        extract-chain: true
        # 是否验证证书有效性
        validate-certificate: true
        # 是否解析扩展信息
        parse-extensions: true
      # 证书存储配置
      storage:
        # 证书元数据存储在Doris中（只读数据）
        metadata-storage: doris
        # 证书标签存储在PostgreSQL中（可变数据）
        labels-storage: postgresql
        # 文件存储在MinIO中
        file-storage: minio
        # 是否启用重复检测（基于哈希值）
        duplicate-detection: true
        # 批量插入大小
        batch-size: 1000
        # 批量插入间隔（毫秒）
        batch-interval: 5000
        # 文件保留天数
        retention-days: 365
      # 标签管理配置
      labels:
        # 是否启用标签功能
        enabled: true
        # 最大标签数量（每个证书）
        max-labels-per-cert: 50
        # 证书标签的目标类型
        target-type: "CERTIFICATE"
        # 默认标签来源
        default-source: "USER"
        # 标签查询配置
        query:
          # 是否只显示激活的标签
          active-only: true
          # 默认排序方式
          default-sort: "sort_order, display_name"

    # 生产环境特定配置
    knife4j:
      production: {{ eq .Values.global.springProfiles "prod" }}

    # 服务配置
    services:
      metadata-service:
        url: http://metadata-service:8093
{{- end -}}
