# Last Update:2021-08-03 16:24:13
##
# @file del_analysis_next_task.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-03-29

import pymysql,sys,json

if len(sys.argv) !=2:
    print("参数错误:")
    sys.exit(1)
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
sql = "delete from  tb_user_analysis_default_report where  target_id = "+sys.argv[1]
print(sql)
cursor.execute(sql)
db.commit()

