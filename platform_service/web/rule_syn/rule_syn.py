# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : rule_syn.py
# 开发工具 : PyCharm
import os,json,pymysql.cursors,sys,base64
from  tb_wihte_filter  import  read_white_list

from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
def write_file(filename , context):
    print(context)
    print(filename)
    os.system("rm -rf "+filename)
    fnew = open(filename, "w+")
    fnew.write(context)
    fnew.close()
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
if len(sys.argv) != 3:
    print("参数错误")
    sys.exit(1)
task_id = sys.argv[1]
batch_id = sys.argv[1]
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
def task_mkdir(path):
    os.system("mkdir -p "+ path + "/db")
    os.system("mkdir -p "+ path + "/conf")
    os.system("mkdir -p "+ path + "/Config")
if task_id == "0"  or task_id == "1": 
    task_path  = "/opt/GeekSec/th/bin/conf/"+ str(task_id)+"/"
    task_mkdir(task_path)
else:
    task_path = "/opt/GeekSec/task/conf/"+str(task_id)+"/"+str(batch_id)+"/conf/"
    task_mkdir(task_path)
def write_base64_file(filename , encode_str):
    context =  base64.decodebytes(bytes(encode_str, 'utf-8')) 
    fp = open(filename, "wb")
    fp.write(context)
    fp.close()
bFullFlow =  True
def batchPcapSave():
    global bFullFlow
    sql = "select fullflow_state  from th_analysis.tb_task_batch  where batch_id = "+str(batch_id)
    results  = s_mysql(sql,cursor)
    for row in  results:
        if row["fullflow_state"]  == "OFF":
            bFullFlow= False
context_json=""
def create_rule(sql,jfilename):
    global context_json

    context=""
    results = s_mysql(sql ,cursor)
    for row in results:
        print(row)
        fname_json= json.loads(row["rule_json"])
        fname_json["Name"] = str(row['rule_name'])
        if jfilename == "rule.json":
            if bFullFlow :
              fname_json["SaveBytes"]  = -1
        ruletagjson = {}
        ruletagjson["RuleId"] = row['rule_id']
        ruletagjson["rule_level"] = row['rule_level']
        ruletagjson["RuleName"] = str(row['rule_name'])
        ruletagjson["Tag"] = []
        ruletagjson["Infor"] = str(row['rule_desc'])
        ruletagjson["rule_size"] = row['rule_size']
        ruletagjson["capture_mode"] = row['capture_mode']
        ruletagjson["created_time"] = str(row['created_time'])
        ruletagjson["PbDrop"] = str(row['pb_drop'])
        ruletagjson["PcapDrop"] = str(row['pcap_drop'])
        ruletagjson["updated_time"] = str(row['updated_time'])
        if "rule_type" in row :
            rile_tyle_list   = row["rule_type"].split(",")
            print("rule_tyle_list ===== " , rile_tyle_list)
            if '6' in rile_tyle_list:   ### 动态库规则
                pathname = task_path + "JsonRule/BasicRule/LibFolder/" 
                soname = row["lib_respond_lib"]
  
                filename = pathname + soname
                os.system("mkdir -p "+ pathname)
                write_base64_file(filename , row["lib_data_so"]) 
                if  "lib_respund_config" in row:
                    conf_file = row["lib_respund_config"] 
                    if conf_file != "":
                        conf_path =  task_path + "JsonRule/BasicRule/LibConfig/"+str(ruletagjson["RuleId"] ) + "/"
                        os.system("mkdir -p "+conf_path)
                        write_base64_file(conf_path+ conf_file  , row["lib_data_conf"]) 
                fname_json["LibRespond"]["Lib"] = soname
            if '7' in rile_tyle_list:   ### 动态库规则
                confname = "/tmp/"+str(ruletagjson["RuleId"] ) +".json"
                json.dump(fname_json["DetailRespond"] , open(confname,"w+"))
                pathname = task_path + "JsonRule/BasicRule/LibFolder/" 
                osname =  str(ruletagjson["RuleId"] ) + ".so"
                filename = pathname + osname
                os.system("mkdir -p "+ pathname)
                print("/opt/GeekSec/th/bin/complex_dll_gen/src/_complex_so_gen.sh " + confname +" "+osname)
                os.system("/opt/GeekSec/th/bin/complex_dll_gen/src/_complex_so_gen.sh " + confname +" "+osname)
                os.system("cp -rf /tmp/"+osname + " "+filename)
                os.system("rm -rf "+confname)
                os.system("rm -rf /tmp/"+osname)
                fname_json["LibRespond"]["Lib"] = osname
                print(fname_json)
                
        if row["rule_state"]=="生效":
            print("work %d"%row["rule_id"])
            context_json = context_json + "\n\n" + json.dumps(ruletagjson)
            if "lib_data_so" in fname_json:
                del  fname_json["lib_data_so"]
            if "lib_data_conf" in fname_json:
                del  fname_json["lib_data_conf"]
            fname = json.dumps(fname_json).replace("\n","",100)
            context=context + "\n\n"+fname
    #os.system("echo \"\">"+filename)
    print("*************   write file ********************")
    print(context)
    write_file(jfilename,context)
    print("*************   write file end ********************")
batchPcapSave()
rulesql = "select rule_id,rule_level,rule_name,rule_desc,rule_size,capture_mode,rule_json,created_time,pb_drop,pcap_drop,updated_time,rule_state , lib_respond_open , lib_respond_lib  , lib_respond_config  , lib_respond_session_end , lib_data_so , lib_data_conf ,rule_type  from tb_rule where rule_state = '生效' and task_id = "+task_id+";"
whitesql = "select rule_id,rule_level,rule_name,rule_desc,rule_size,capture_mode,rule_json,created_time,pb_drop,pcap_drop,updated_time,rule_state from tb_forensics_white_list where task_id = "+task_id+ " and  rule_state = '生效';"
create_rule(rulesql ,"rule.json")
create_rule(whitesql ,"white.json")
os.system("echo \"\">ruletag.json")
write_file("ruletag.json",context_json)
def task_mkdir(path):
    os.system("mkdir -p "+ path + "/db")
    os.system("mkdir -p "+ path + "/conf")
    os.system("mkdir -p "+ path + "/Config")
    os.system("mkdir -p "+ path + "/JsonRule/BasicRule/UserRule/")
    os.system("mkdir -p "+ path + "/JsonRule/BasicRule/LibFolder/")
base_json = {}
# 数据同步
     
isExists = os.path.exists(task_path + "/JsonRule/BasicRule/UserRule/")
if not isExists:
    os.makedirs(task_path + "JsonRule/BasicRule/UserRule/")
os.system ("cp -rf rule.json  "+ task_path+  "/JsonRule/BasicRule/UserRule/rule.json")
os.system ("cp -rf white.json "+task_path+"/JsonRule/BasicRule/UserRule/white.json")
isExists_rule = os.path.exists("/opt/GeekSec/STL/ExportData/bin/script/rule_parse.py")
if isExists_rule:
    os.system("python3 /opt/GeekSec/STL/ExportData/bin/script/rule_parse.py /opt/GeekSec/web/rule_syn/rule.json >/dev/null")
#idu_mysql("delete from roule_id_total_statistic where rule_id not in (select rule_id from tb_rule);",cursor)
#idu_mysql("delete from tb_alarm where tag_id not in (select rule_id from tb_rule_info);",cursor)
db.close()
os.system("python3 /opt/GeekSec/web/rule_syn/task/jc_rule_sync.py")

