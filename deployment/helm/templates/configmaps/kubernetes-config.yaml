apiVersion: v1
kind: ConfigMap
metadata:
  name: kubernetes-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: config
data:
  application-kubernetes.yml: |
    # Spring Cloud Kubernetes 全局配置
    spring:
      cloud:
        # Kubernetes 服务发现配置
        kubernetes:
          # 服务发现配置
          discovery:
            # 启用服务发现
            enabled: true
            # 是否发现所有命名空间的服务
            all-namespaces: false
            # 等待服务发现就绪的超时时间
            wait-cache-ready: true
            # 缓存刷新间隔（秒）
            cache-loading-timeout-seconds: 60
            # 是否包含外部名称服务
            include-external-name-services: false
            # 服务标签过滤器
            service-labels:
              app.kubernetes.io/part-of: nta
            # 主要端口名称
            primary-port-name: http
            # 服务端口名称映射
            ports:
              http: 8080
              https: 8443
              management: 8081
          
          # 配置管理
          config:
            # 启用配置管理
            enabled: true
            # 配置源
            sources:
              # 从ConfigMap获取配置
              - name: kubernetes-config
                namespace: {{ .Values.global.namespace }}
              # 从Secret获取敏感配置
              - name: application-secrets
                namespace: {{ .Values.global.namespace }}
                optional: true
            # 配置重新加载
            reload:
              enabled: {{ eq .Values.global.springProfiles "dev" | ternary "true" "false" }}
              mode: event
              strategy: refresh
          
          # 负载均衡配置
          loadbalancer:
            # 启用Kubernetes原生负载均衡
            mode: SERVICE
            # 集群域名
            cluster-domain: cluster.local
            # 端口名称
            port-name: http
        
        # OpenFeign 配置
        openfeign:
          # 启用熔断器
          circuitbreaker:
            enabled: true
          # 客户端配置
          client:
            config:
              default:
                # 连接超时时间（毫秒）
                connect-timeout: 5000
                # 读取超时时间（毫秒）
                read-timeout: 10000
                # 日志级别
                logger-level: basic
          # 压缩配置
          compression:
            request:
              enabled: true
              mime-types: text/xml,application/xml,application/json
              min-request-size: 2048
            response:
              enabled: true
        
        # 负载均衡配置
        loadbalancer:
          # 启用重试
          retry:
            enabled: true
          # 健康检查
          health-check:
            initial-delay: 0
            interval: 25s
          # 缓存配置
          cache:
            enabled: true
            ttl: 35s
            capacity: 256

    # 管理端点配置
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus,refresh,discovery
      endpoint:
        health:
          show-details: always
          show-components: always
        discovery:
          enabled: true
      health:
        kubernetes:
          enabled: true
        refresh:
          enabled: true

    # 日志配置
    logging:
      level:
        org.springframework.cloud.kubernetes: {{ eq .Values.global.springProfiles "dev" | ternary "DEBUG" "INFO" }}
        org.springframework.cloud.openfeign: {{ eq .Values.global.springProfiles "dev" | ternary "DEBUG" "INFO" }}
        org.springframework.cloud.loadbalancer: {{ eq .Values.global.springProfiles "dev" | ternary "DEBUG" "INFO" }}
        feign: {{ eq .Values.global.springProfiles "dev" | ternary "DEBUG" "INFO" }}
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
