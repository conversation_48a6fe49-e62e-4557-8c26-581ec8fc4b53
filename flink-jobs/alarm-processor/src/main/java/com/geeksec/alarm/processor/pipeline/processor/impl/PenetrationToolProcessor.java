package com.geeksec.alarm.processor.pipeline.processor.impl;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.alarm.processor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 渗透工具指纹告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class PenetrationToolProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.PENETRATION_TOOL_FINGERPRINT;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        String toolName = getToolName(alarm);
        String toolFingerprint = getToolFingerprint(alarm);
        
        reasons.add(createDetectionReason(
                "渗透工具指纹检测",
                String.format("检测到%s渗透工具的网络指纹特征", toolName),
                "正常工具指纹",
                toolFingerprint,
                "合法应用指纹",
                8
        ));
        
        // 如果有具体的工具特征，添加详细分析
        if (toolFingerprint != null && !toolFingerprint.isEmpty()) {
            reasons.add(createDetectionReason(
                    "工具特征匹配",
                    "网络流量中包含已知渗透工具的特征字符串",
                    "正常应用流量",
                    String.format("特征: %s", toolFingerprint),
                    "标准协议流量",
                    7
            ));
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        String toolName = getToolName(alarm);
        
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        String.format("立即阻断来自%s的可疑IP访问", alarm.getSrcIp()),
                        String.format("检查目标系统是否存在%s可利用的漏洞", toolName),
                        "加强对该IP的监控"
                ))
                .investigationSteps(Arrays.asList(
                        String.format("分析%s工具的具体攻击目标", toolName),
                        "确认攻击者的真实意图和能力",
                        "检查是否有后续的攻击行为",
                        "分析攻击时间线和攻击路径"
                ))
                .preventionMeasures(Arrays.asList(
                        "更新入侵检测规则，加强对渗透工具的识别",
                        "加强网络边界访问控制",
                        "定期进行渗透测试，发现潜在漏洞",
                        "部署蜜罐系统，诱捕攻击者"
                ))
                .recoverySteps(Arrays.asList(
                        "修补被渗透工具发现的安全漏洞",
                        "重新配置受影响系统的安全设置",
                        "更新系统补丁和安全软件",
                        "恢复被修改的系统配置",
                        "加强访问控制和权限管理",
                        "监控系统确保没有后门或持久化威胁"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        String toolName = getToolName(alarm);
        return String.format("攻击者使用%s等渗透测试工具对目标系统进行漏洞扫描和攻击尝试，" +
                "这些工具具有特定的网络指纹特征，可以通过流量分析进行识别。", toolName);
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "通过分析网络流量中的特征字符串、请求模式、用户代理等信息，" +
                "识别已知渗透工具的网络指纹，从而检测潜在的攻击行为。";
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99064";
    }
    
    /**
     * 获取工具名称
     */
    private String getToolName(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object toolName = alarm.getExtendedProperties().get("tool_name");
            if (toolName != null) {
                return toolName.toString();
            }
        }
        return "未知渗透工具";
    }
    
    /**
     * 获取工具指纹
     */
    private String getToolFingerprint(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object fingerprint = alarm.getExtendedProperties().get("tool_fingerprint");
            if (fingerprint != null) {
                return fingerprint.toString();
            }
        }
        return "未知指纹";
    }
}
