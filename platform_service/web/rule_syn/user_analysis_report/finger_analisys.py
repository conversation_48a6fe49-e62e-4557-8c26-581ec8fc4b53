# Last Update:2020-04-23 17:20:35
##
# @file finger_analisys.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-03-17


from elasticsearch import Elasticsearch
import pymysql 
import json
import time 
import sys,os
import re
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.pagesizes import letter
import matplotlib as mpl
mpl.use('Agg')
import matplotlib.pyplot as plt
import numpy as np


from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak, Table, TableStyle
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.units import mm
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics
import ipaddress

from analisys_common_tools import list_to_pdftable,list_to_pdflist,drew_heatmap_png,drew_barplot_png,time_to_str,time_to_hour_str,time_to_all_str,isIP4or6
from ip_hot_calculation import  iplist_to_iphot

mpl.rcParams['font.sans-serif']=['SimHei']  #设置为黑体字

# 注册字体
pdfmetrics.registerFont(TTFont('msyh', '/opt/GeekSec/web/rule_syn/Fonts/msyh.ttc'))
pdfmetrics.registerFont(TTFont('msyhbd', '/opt/GeekSec/web/rule_syn/Fonts/msyhbd.ttc'))
pdfmetrics.registerFont(TTFont('msyhl', '/opt/GeekSec/web/rule_syn/Fonts/msyhl.ttc'))

max_time = int(time.time())
def get_max_time():
    global max_time
    max_time = int(time.time())
os.system("mkdir  -p png/")

argv_json = {}
base_json = {}

file_name = "" #+ "ip_analisys_" + str(task_id) + "_" + ip +"_"+str(time.time()) +".pdf"
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
cursors = cursor
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
finger_base_t =[]  #基本信息
finger_attr_t = []
aralm_info_t = [] # 告警列表
aralm_list =[]
task_info_t = []  # 任务信息
finger_corr_finger_t = []  # 指纹关联指纹
tcp_task_info_t =[]  # tcp 任务分布
tcp_ip_list_t = [] # tcp IP列表
tcp_ip_server_t = [] # tcp 服务器IP列表
tcp_ip_client_t = [] # tcp  ii.	访问应用
tcp_alarm_info_t = []  # tcp指纹告警
finger_info = {}
# 知识库部分
tag_info = {}
appid_appname = {}



# http 协议

http_task_info_t = [] # http 任务分布
http_ip_server_t = []  # http关联服务器
http_alarm_info_t = []
http_server_corr_domain_t = []
http_ip_list_t = []

# SSL
ssl_alarm_info_t = []
ssl_server_corr_domain_t = []
finger_corr_cert_t = []
finger_corr_certlist_t = []
ssl_task_info_t = []
ssl_ip_server_t = []
all_session_num  = 0
all_server_ip_list = []
all_client_ip_list = []
ssl_ip_list_t = []
def ip2int(ip):
    return abs(int(ipaddress.ip_address(ip)))


should = [] # 条件
def finger_base_info(finger_sha1):
    finger_info['sha1'] = ""
    finger_info['type'] = ""
    black_list  = 0
    remark = ""
    sql = "select a.finger ,a.remark,a.finger_sha1 ,a.type ,a.device ,a.os,a.application,a.renew_time ,a.black_list , a.white_list  from tb_finger_infor a  where a.finger_sha1 = '"+str(finger_sha1)+"'"
    reslut = s_mysql(sql,cursor)
    if len(reslut) > 0:
        row = reslut[0]
        # '//1 TCP  2 HTTP  3 SSL',
        if row['type']==1:
             finger_info['type']= "TCP"
        elif row['type']==2:
            finger_info['type']= "HTTP"
        else :
            finger_info['type']= "SSL"
        finger_info['finger'] = row['finger']
        finger =  finger_info['finger']
        finger_info['sha1']  = row['finger_sha1']
        finger_info['os']  = row['os']
        black_list = row['black_list']
        white_list =  row['white_list']
        remark = row['remark']
        print(finger_info)
        finger_base_t.append(("指纹",finger_sha1,"指纹类型",finger_info['type']))
        finger_attr_t.append(("指纹类型",finger_info['type'] ,"硬件",row['device']))
        finger_attr_t.append(("系统",finger_info['os'] ,"应用",row['application']))
        #finger_base_t.append(("指纹",finger,"指纹类型",finger_info['type']))
    else:
        print("*******  指纹未找到  ******* ")
        return  
    sql = "select max(a.last_time) as last_time  ,min(a.first_time) as first_time  from ((select max(last_time) as last_time ,min(first_time) as  \
     first_time,finger  from tb_client_finger  where finger = "+str(finger)+" group by finger) \
     UNION(select max(last_time) as last_time ,min(first_time) as first_time ,finger from tb_server_finger where finger = "+str(finger)+" group by finger)) as a"
    reslut = s_mysql(sql,cursor)
    if len(reslut)>0:
        row  = reslut[0]
        finger_base_t.append(("首次发现时间", time_to_str(row['first_time']), "末次发现时间", time_to_str(row['first_time'])))

    # tag 
    tag_list = []
    sql = "select  distinct b.tag_text  from  tb_finger_tag a ,tb_tag_info b  where a.finger = "+str(finger)+" and a.tag_id = b.tag_id "
    reslut = s_mysql(sql,cursor)
    for row in reslut :
        tag_list.append(row['tag_text'])
    aralm_info(finger)
    finger_base_t.append(("标签",list_to_pdftable(tag_list)))
    finger_base_t.append(("告警",list_to_pdftable(aralm_list)))
    finger_base_t.append(("备注",remark))
def aralm_info(finger):
    sql = "select b.tag_text , a.tag_id ,b.tag_family , b.black_list , c.task_id , a.defense_info ,max(time) as last_time ,min(time) as first_time from tb_alarm a , \
((SELECT post.tag_text as tag_text, post.black_list as black_list,post.tag_family AS tag_family,post.tag_id AS tag_id FROM `tb_tag_info` AS `post`) \
    UNION(SELECT reply.rule_name as tag_text,reply.rule_level as black_list,reply.rule_family AS tag_family,reply.rule_id AS tag_id FROM `tb_rule_info` AS `reply`)) as b  ,tb_task_batch c  \
     where a.tag_id = b.tag_id   and a.target_type = 7 and  a.target_name = "+str(finger)+"   and a.batch_id  = c.batch_id group by a.tag_id,c.task_id "


    reslut = s_mysql(sql,cursor)
    aralm_info_t.append(("序号","告警","告警类型","任务","威胁权重","说明","首次告警时间","末次告警时间"))
    tid =  0
    for row  in reslut:
        if  row['tag_text']== None:
            continue
        tid = tid + 1
        aralm_list.append(row['tag_text'])
        aralm_info_t.append((tid,row['tag_text'],row['tag_family'],row['task_id'],row['black_list'],row['defense_info'],row['first_time'],row['last_time']))


    if finger_info['type'] == "TCP":
        should.append({"match": {"tcp_c_finger": str(finger)}})
        should.append({"match": {"tcp_s_finger": str(finger)}})
    elif finger_info['type'] == "HTTP":
        should.append({"match": {"http_c_finger": str(finger)}})
        should.append({"match": {"http_s_finger": str(finger)}})
    elif finger_info['type'] == "SSL":
        should.append({"match": {"ssl_c_finger": str(finger)}})
        should.append({"match": {"ssl_s_finger": str(finger)}})
def task_info_info(finger):
    c_should = []
    if finger_info['type'] == "TCP":
        c_should.append({"match": {"tcp_c_finger": str(finger)}})
        c_should.append({"match": {"tcp_s_finger": str(finger)}})
    elif finger_info['type'] == "HTTP":
        c_should.append({"match": {"http_c_finger": str(finger)}})
        c_should.append({"match": {"http_s_finger": str(finger)}})
    elif finger_info['type'] == "SSL":
        c_should.append({"match": {"ssl_c_finger": str(finger)}})
        c_should.append({"match": {"ssl_s_finger": str(finger)}})
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    body_ar = {"query": {"bool": {"should": c_should }}, "aggs": {"taskif": {"terms": {"field": "TaskId"}, "aggs": {
        "sIp": {"terms": {"field": "sIp.keyword"}, "aggs":{"dIp": {"terms": {"field": "dIp.keyword"},
                                                    "aggs": {"max_price": {"max": {"field": "StartTime"}},
                                                             "aggs": {"min": {"field": "StartTime"}}}}}}}}}}
    result = es.search(index=index_name, body=body_ar)
    if "aggregations" not in result:
        return
    c_finger_list  = {}
    s_finger_list = {}
    task_info_t.append(("序号","任务","所有者热度","访问服务器热度","会话数量","首次出现时间","末次出现时间"))
    print(json.dumps(body_ar))
    print(result)
    time.sleep(10)
    tid = 0
# 关联分析
    for row in result["aggregations"]['taskif']['buckets']:
        task_id = row['key']
        s_ip_list = []
        d_ip_list = []
        first_time  = 9999999999
        last_time =  0
        nrow = row['doc_count']
        all_session_num = all_session_num +  nrow

        for sip_row in row['sIp']['buckets']:
            s_ip_list.append(sip_row['key'])
            all_client_ip_list.append(sip_row['key'])
            for dip_row in sip_row['dIp']['buckets']:
                d_ip_list.append(dip_row['key'])
                all_server_ip_list.append(dip_row['key'])
                if dip_row['max_price']['value'] > last_time:
                    last_time = dip_row['max_price']['value']
                if  dip_row['aggs']['value'] < first_time:
                    first_time = dip_row['aggs']['value']
        tid =  tid + 1
        if first_time != 999999999:
            task_info_t.append((tid,task_id,iplist_to_iphot(s_ip_list),iplist_to_iphot(d_ip_list),nrow,time_to_str(first_time),time_to_str(last_time)))
# 关联分析
def get_finger_info(sql ):
    f = []
    relust = s_mysql(sql,cursor)
    if len(relust) > 0:
        for row in relust:
            f.append(row['tcpfinger'])
    return f
# 关联指纹
finger_type_info=["TCP","HTTP","SSL"]
def  tag_info_nuk():
    sql = "select * from ((SELECT  post.tag_text as tag_text, post.black_list as black_list,post.tag_family AS tag_family,post.tag_id AS tag_id FROM `tb_tag_info` AS `post`) \
    UNION(SELECT   reply.rule_name as tag_text,reply.rule_level as black_list,reply.rule_family AS tag_family,reply.rule_id AS tag_id FROM `tb_rule_info` AS `reply`)) as c"
    relust = s_mysql(sql,cursor)
    for row in relust:
        tag_info[row['tag_id']] = {"tag_text":row['tag_text'],"black_list":row['black_list'],"tag_family":row['tag_family']}


    sql = "select pro_id ,pro_name from app_pro_value"
    relust = s_mysql(sql, cursor)
    for row in relust:
        appid_appname[row['pro_id']] = row['pro_name']
    
def corr_finger_finger(finger):

    finger_list = []
    finger_list.append(finger)

    if  finger_info['type'] == "HTTP":
        sql = "select  a.tcpfinger  as tcpfinger from   tb_ssl_tcp_finger a where a.sslfinger =  " + str(finger)
        finger_list =  finger_list + get_finger_info(sql)
    elif finger_info['type'] == "SSL":
        sql = "select  a.tcpfinger as tcpfinger  from   tb_http_tcp_finger a where a.tcpfinger = " + str(finger)
        finger_list = finger_list +get_finger_info(sql)
    # 查询处理
    sql = "select a.finger ,a.finger_sha1 ,a.type ,c.tag_id ,b.times ,a.device , b.first_time ,b.last_time  from tb_finger_infor as a ,  (select * from  \
    ((select a.sslfinger as finger  ,max(a.last_time)  last_time  , min(a.first_time) as first_time ,sum(a.times) as times  from  tb_ssl_tcp_finger a  \
        where a.tcpfinger in "+json.dumps(finger_list).replace("[","(",1).replace("]",")",1)+"  group by a.sslfinger ) \
        UNION (select a.useragent as finger ,max(a.last_time)  last_time  , min(a.first_time) as first_time  ,sum(a.times) as times from  tb_http_tcp_finger a \
            where a.tcpfinger in "+json.dumps(finger_list).replace("[","(",1).replace("]",")",1)+"   )) as a group by finger  ) ) as b  ,tb_finger_tag c   where a.finger   = b.finger  order by  b.finger desc "
    sql = "select a.finger ,a.finger_sha1 ,a.type ,c.tag_id ,b.times ,a.device , b.first_time ,b.last_time  from tb_finger_infor as a ,  \
              (select * from      ((select a.sslfinger as finger  ,max(a.last_time)  last_time  , \
               min(a.first_time) as first_time ,sum(a.times) as times  from  tb_ssl_tcp_finger a     \
                where a.tcpfinger in "+json.dumps(finger_list).replace("[","(",1).replace("]",")",1)+"  group by a.sslfinger )         UNION (select a.useragent as finger ,max(a.last_time)  last_time  , \
                 min(a.first_time) as first_time  ,sum(a.times) as times from  tb_http_tcp_finger a   \
                  where a.tcpfinger in ("+json.dumps(finger_list).replace("[","(",1).replace("]",")",1)+")   )) as a group by finger ) as b  ,tb_finger_tag c   where a.finger   = b.finger  order by  b.finger desc "
    #finger_corr_finger_t.append()
    result = s_mysql(sql,cursor)
    finger_corr_finger_t.append(("序号","指纹","指纹类型","威胁等级","标签","关联IP热度","属性","会话数量","首次发现时间","末次发现时间"))
    tid = 0

    if len(result) >0:
        finger_info_sl = {}
        finger_ts = ""
        ip_t = []
        tag_t = []
        first_time = 0
        last_time = 0
        tag_backlist = 0
        for row in result:
            if finger_ts  == "" or finger_ts != row['finger']:
                if finger_ts  != "":
                    finger_corr_finger_t.append((tid, finger_info_sl['finger'], finger_type_info[finger_info_sl['type']],finger_info_sl['tag_backlist'],list_to_pdftable(tag_in),
                                                 clientdis_consrt(ip_t),finger_info_sl['device'],time_to_str(finger_info_sl['first_time']),time_to_str(finger_info_sl['last_time'])))
                    ip_t = []
                    tag_t = []
                    tid = tid + 1
                    tag_in = []
                    finger_info_sl = {}
                finger_info_sl['finger'] = row['finger']
                finger_info_sl['type'] = row['type']
                finger_info_sl['times'] = row['times']
                finger_info_sl['device'] = row['device']
                ip_t.append(row['ip'])
                finger_info_sl['tag_in'] = tag_info[row['tag_id']]
                tag_in = tag_info[row['tag_id']]
                tag_t.append(tag_in['tag_text'])
                finger_info_sl['tag_backlist'] = tag_in['black_list']
                finger_info_sl['first_time'] = row['first_time']
                finger_info_sl['last_time'] = row['last_time']

            if  finger_ts == row['finger']:
                tag_in = tag_info[row['tag_id']]
                ip_t.append(row['ip'])
                if finger_info_sl['tag_backlist'] < tag_in['black_list']:
                    finger_info_sl['tag_backlist'] = tag_in['black_list']
                if finger_info_sl['first_time']  > row['first_time']:
                    finger_info_sl['first_time'] = row['first_time']
                if finger_info_sl['last_time']  < row['last_time']:
                    finger_info_sl['last_time'] = row['last_time']
                finger_info_sl['times'] = row['times'] + finger_info['times']

# 四、	会话分析   / i.	任务分布

def tcp_task_info(finger):
    tcp_task_info_t .append(("序号","任务","客户端热度","客户端IP数量","会话数量","首次发现时间","末次发现时间"))
    if finger_info['type'] != 1:
        return
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    body_ar = {"query": {
        "bool": {"should": [{"match": {"tcp_s_finger": str(finger)}}], "must": [{"match": {"IPPro": 17}}]}},
     "aggs": {"task": {"terms": {"field": "TaskId"}, "aggs": {"sIp": {"terms": {"field": "sIp.keyword"}, "aggs": {
         "max_price": {"max": {"field": "StartTime"}}, "aggs": {"min": {"field": "StartTime"}}}}}}}}
    result = es.search(index=index_name, body=body_ar)
    if "aggregations" not in  result :
        return
    tid=  0
    for row in result["aggregations"]['task']['buckets']:
        ip_t  = []
        task_id  = row['key']
        connect_num = row['doc_count']
        first_time = 99999999999
        last_time = 0
        for sIp_row in row['sIp']['buckets']:
            ip_t.append(sIp_row['key'])
            if first_time >  sIp_row['aggs']['value']:
                first_time = sIp_row['aggs']['value']
            if last_time > sIp_row['max_price']['value']:
                last_time = sIp_row['max_price']['value']

        #ip_t = list(set(ip_t))
        t_id = t_id + 1
        tcp_task_info_t.append((tid,task_id,clientdis_consrt(ip_t),len(ip_t),connect_num,time_to_str(first_time),time_to_str(last_time)))


# ii.	时序分布
def tcp_finger_shixu(finger):
    min_time = max_time - 3600 * 24
    body_ar ={"query":{"bool":{"should":should,"must":[{"match":{"IPPro":17}}]}},"aggs":{"prices":{"histogram":{"field":"StartTime","interval":3600,"order":{"_key":"desc"}}}}}

    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ar)
    if "aggregations" not in result:
        return
    tid = 0
    for row in result["aggregations"]['prices']['buckets']:
        if row['key'] < min_time :
            continue

# 获取IP 的
def get_ip_info(ip):
    ip_t = {}
    sql  = "select max(a.white_list) as white_list  ,min(a.black_list) as black_list  , a.ip  from tb_ip_info a  " \
           "where ip = \'"+ip + "\' group by ip "
    relust = s_mysql(sql,cursor)
    ip_t['black_list']  = 0 
    ip_t['white_list']  = 100
    if len(relust) > 0 :
        row  = relust[0]
        ip_t['white_list'] = row['white_list']
        ip_t['black_list'] =  row['black_list']
    ip_tag_t = []
    sql = "select tag_id from tb_ip_tag where ip = \'"+ ip + "\'"
    relust = s_mysql(sql, cursor)
    for row in relust:
        ip_tag_t.append(tag_info[row['tag_id']])
    ip_t['tag_info'] = ip_tag_t
    sql = "select distinct  finger from tb_client_finger where ip = '"+ip+"'"
    ip_client_finger_t = []
    relust = s_mysql(sql, cursor)
    for row in relust:
        ip_client_finger_t.append(row['finger'])
    sql =  "select distinct  finger from tb_server_finger where ip = '"+ip+"'"
    relust = s_mysql(sql, cursor)
    ip_server_finger_t = []
    for row in relust:
        ip_server_finger_t.append(row['finger'])
    ip_t['finger_client_info'] = ip_client_finger_t
    ip_t['finger_server_info'] = ip_server_finger_t
    return  ip_t


def get_ip_domain_and_cert(ip):
    sql = "select n_domain ,cert_sha1 from tb_passive_cert where ip = \'"+ip+"\'"
    relust = s_mysql(sql , cursor)
    ip_domain_t = []
    ip_cert_t = []
    for row in  relust:
        ip_domain_t.append(row['n_domain'])
        ip_cert_t.append(row['cert_sha1'])
    ip_dc = {"domain_list":list(set(ip_domain_t)),"cert_list":list(set(ip_cert_t))}
    return ip_dc
# iii.	IP列表
def tcp_ip_list(finger):
    c_should = []
    if finger_info['type'] == "TCP":
        c_should.append({"match": {"tcp_c_finger": str(finger)}})
        c_should.append({"match": {"tcp_s_finger": str(finger)}})
    elif finger_info['type'] == "HTTP":
        c_should.append({"match": {"http_c_finger": str(finger)}})
        c_should.append({"match": {"http_s_finger": str(finger)}})
    elif finger_info['type'] == "SSL":
        c_should.append({"match": {"ssl_c_finger": str(finger)}})
        c_should.append({"match": {"ssl_s_finger": str(finger)}})
    
    body_ac = {"query": {
        "bool": {"should": c_should, "must": [{"match": {"IPPro": 17}}]}},
        "aggs": {"sIp": {"terms": {"field": "sIp.keyword"}, "aggs": {"sPort": {"terms": {"field": "sPort"},"aggs": {"Appid": {"terms": {"field": "AppId"}, "aggs": {
         "max_price": {"max": {"field": "StartTime"}}, "aggs": {"min": {"field": "StartTime"}}}}}}}}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    tcp_ip_list_t.append(("序号","类型","IP","威胁权重","标签","访问端口","拥有指纹数量","会话数量"))
    if "aggregations" not in result:
        return
    tid = 0

    for row in result["aggregations"]['sIp']['buckets']:
        sIp = row['key']
        first_time = 9999999999
        last_time = 0
        port_app_list=[]

        for sPort_row in row['sPort']['buckets']:
            sPort = sPort_row['key']
            connect_num = sPort_row['doc_count']
            for appid_row in sPort_row['Appid']['buckets']:
                if appid_row['key'] == 10000:
                    continue
                app_name = str(appid_row['key'])
                if  appid_row['key'] in appid_appname :
                    app_name = appid_appname[appid_row['key']]
                port_app_list.append(str(sPort)+"-"+app_name)

                if first_time > appid_row['aggs']['value']:
                    first_time = appid_row['aggs']['value']
                if last_time < appid_row['max_price']['value']:
                    last_time = appid_row['max_price']['value']

        ip_t = get_ip_info(sIp)
        tid=tid + 1
        if 'black_list' not in  ip_t:
            continue 
        print(ip_t)
        tcp_ip_list_t.append((tid,isIP4or6(sIp),sIp,ip_t['black_list'],list_to_pdflist(ip_t['tag_info']),list_to_pdflist(port_app_list),len(ip_t['finger_client_info'] ) + len(ip_t['finger_server_info']),connect_num))

# tcp  服务器 ：
def tcp_finger_to_serverip(finger):
    body_ac = {"query":{"bool":{"should":[{"match":{"tcp_s_finger":str(finger)}}],"must":[{"match":{"IPPro":17}}]}},"aggs":{"dIp":{"terms":{"field":"dIp.keyword"},"aggs":{"AppId":{"terms":{"field":"AppId"},"aggs":{"dPort":{"terms":{"field":"dPort"},"aggs":{"sIp":{"terms":{"field":"sIp.keyword"},"aggs":{"max_price":{"max":{"field":"StartTime"}},"aggs":{"min":{"field":"StartTime"}}}}}}}}}}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    tcp_ip_server_t.append(("序号","服务器","威胁等级","标签","开放端口","会话数量","客户端指纹数量","关联域名","关联证书","首次发现时间","末次发现时间"))
    if "aggregations" not in result:
        return
    tid = 0

    for row in result["aggregations"]['dIp']['buckets']:
        dip =  row['key']
        fisrt_time = 999999999
        last_time = 0
        port_app_list = []
        conect_num = row['doc_count']
        for AppId_row in row['AppId']['buckets']:
            app_name = str(AppId_row['key'] ) 
            if AppId_row['key'] in appid_appname :
                app_name = appid_appname[AppId_row['key']]
            for dPort_row in AppId_row['dPort']['buckets']:
                dport = dPort_row['key']
                port_app_list.append(str(dport)+"-"+app_name)
                for sIp_row  in dPort_row['sIp']['buckets']:
                    sip  = sIp_row['key']
                    if fisrt_time > sIp_row['aggs']['value']:
                        fisrt_time = sIp_row['aggs']['value']
                        if last_time < sIp_row['max_price']['value']:
                            last_time = sIp_row['max_price']['value']

        ip_dc=get_ip_domain_and_cert(dip)
        tid = tid + 1
        ip_t = get_ip_info(dip)
        print(ip_dc)
        tcp_ip_server_t.append((tid,dip,ip_t['black_list'],list_to_pdflist(ip_t['tag_info']),list_to_pdflist(port_app_list)
                                ,conect_num,len(ip_t['finger_client_info']),list_to_pdflist(ip_dc['domain_list']),list_to_pdflist(ip_dc['cert_list']),time_to_str(fisrt_time),time_to_str(last_time)))

   # ii.访问应用
def tcp_finger_to_appid(finger):
    body_ac = {}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    tcp_ip_client_t.append(
            ("序号", "端口", "应用协议", "会话数量", "客户端热度", "客户端指纹数量", "首次发现时间", "末次发现时间"))
    if "aggregations" not in result:
        return
    tid = 0
    for row in result["aggregations"]['dPort']['buckets']:
        dPort  = row['dPort']
        connect_num = row['doc_count']
        first_time = 999999999
        last_time = 0
        app_list = []
        ip_list = []
        tid = 0
        client_times = 0
        for appid_row in row['AppId']['buckets']:
            app_name  = str(appid_row['key'])
            if appid_row['key'] in appid_appname:
                app_list.append(appid_appname[appid_row['key']])
            else :
                 app_list.append(app_name)
            for sip_row in appid_row['sIp']['buckets']:
                sip = sip_row['key']
                sql = "select count(finger) as  num   from  tb_client_finger where ip  = \'"+str(sip)+"\'"
                client_times = client_times +  s_mysql(sql,cursor)[0]['num']

                ip_list.append(sip)
                if first_time  > sip_row['aggs']['value']:
                    first_time = sip_row['aggs']['value']
                if last_time < sip_row['max_price']['value']:
                    last_time = sip_row['max_price']['value']
        tid = tid + 1
        tcp_ip_client_t.append((tid,dPort,list_to_pdftable(app_list),connect_num,clientdis_consrt(ip_list),client_times,first_time,last_time))


# c)	告警会话
def tcp_alarm_session_info(finger):
    body_ac = {"query":{"bool":{"should":[{"match":{"tcp_c_finger":str(finger)}},{"match":{"tcp_s_finger":str(finger)}}],"must":[{"match":{"IPPro":17}}]}}}
    index_name = "connectinfo_*"
    result = es.search(index=index_name, body=body_ac)
    finger_session_info={}
    for row in result['hits']['hits']:
        print(row)
        finger_session_info[row['_source']['SessionId']] = row['_source']

    tcp_alarm_info_t.append(("序号","会话ID","源IP","源端口","目的IP","目的端口","协议号","威胁等级","标签","上行数据量","下行数据量","起始时间","结束时间"))
    sql = "select target_name as session_id ,tag_id as tag_id from tb_alarm where target_type = 6  "
    relust = s_mysql(sql , cursor)
    #tcp_alarm_info_t.append(())
    n_tid = 0;
    for row in relust:
        sesson_id =  row['session_id']
        tag_id  = row['tag_id']
        if sesson_id in finger_session_info:
            n_tid = n_tid + 1
            w=finger_session_info[sesson_id]
            tcp_alarm_info_t.append((n_tid,sesson_id ,w['sIp'],w['sPort'],w['dIp'],w['dPort'],
                                     w['IPPro'],tag_info[tag_id]['black_list'],tag_info[tag_id]['tag_text'],w['pkt']['pkt_dbytes'],w['pkt']['pkt_sbytes'],time_to_str(w['StartTime']),time_to_str(w['EndTime'])))


# http


# 四、	会话分析  /HTTP 协议  / i.	任务分布

def http_task_info(finger):
    http_task_info_t .append(("序号","任务","客户端热度","客户端IP数量","会话数量","首次发现时间","末次发现时间"))
    if finger_info['type'] != 1:
        return
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    body_ar = {"query": {
        "bool": {"should": [{"match": {"http_s_finger": str(finger)}}], "must": [{"match": {"IPPro": 17}}]}},
     "aggs": {"task": {"terms": {"field": "TaskId"}, "aggs": {"sIp": {"terms": {"field": "sIp.keyword"}, "aggs": {
         "max_price": {"max": {"field": "StartTime"}}, "aggs": {"min": {"field": "StartTime"}}}}}}}}
    result = es.search(index=index_name, body=body_ar)
    if "aggregations" not in  result :
        return
    tid=  0
    for row in result["aggregations"]['task']['buckets']:
        ip_t  = []
        task_id  = row['key']
        connect_num = row['doc_count']
        first_time = 99999999999
        last_time = 0
        for sIp_row in row['sIp']['buckets']:
            ip_t.append(sIp_row['key'])
            if first_time >  sIp_row['aggs']['value']:
                first_time = sIp_row['aggs']['value']
            if last_time > sIp_row['max_price']['value']:
                last_time = sIp_row['max_price']['value']

        #ip_t = list(set(ip_t))
        t_id = t_id + 1
        http_task_info_t.append((tid,task_id,clientdis_consrt(ip_t),len(ip_t),connect_num,time_to_str(first_time),time_to_str(last_time)))


# ii.	时序分布
def http_finger_shixu(finger):
    min_time = max_time - 3600 * 24
    body_ar ={"query":{"bool":{"should":should,"must":[{"match":{"IPPro":17}}]}},"aggs":{"prices":{"histogram":{"field":"StartTime","interval":3600,"order":{"_key":"desc"}}}}}

    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ar)
    if "aggregations" not in result:
        return
    tid = 0
    for row in result["aggregations"]['prices']['buckets']:
        if row['key'] < min_time :
            continue
# http  服务器 ：
def http_finger_to_serverip(finger):
    body_ac = {"query":{"bool":{"should":[{"match":{"http_s_finger":str(finger)}}],"must":[{"match":{"IPPro":17}}]}},"aggs":{"dIp":{"terms":{"field":"dIp.keyword"},"aggs":{"AppId":{"terms":{"field":"AppId"},"aggs":{"dPort":{"terms":{"field":"dPort"},"aggs":{"sIp":{"terms":{"field":"sIp.keyword"},"aggs":{"max_price":{"max":{"field":"StartTime"}},"aggs":{"min":{"field":"StartTime"}}}}}}}}}}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    http_ip_server_t.append(("序号","服务器","威胁等级","标签","开放端口","会话数量","客户端指纹数量","关联域名","关联证书","首次发现时间","末次发现时间"))
    if "aggregations" not in result:
        return
    tid = 0

    for row in result["aggregations"]['dIp']['buckets']:
        dip =  row['key']
        fisrt_time = 99999999999
        last_time = 0
        port_app_list = []
        conect_num = row['doc_count']
        for AppId_row in row['AppId']['buckets']:
            app_name  = str(AppId_row['key'])
            if AppId_row['key'] in appid_appname :
                app_name = appid_appname[AppId_row['key']]
            for dPort_row in AppId_row['dPort']['buckets']:
                dport = dPort_row['key']
                port_app_list.append(str(dport)+"-"+app_name)
                for sIp_row  in dPort_row['sIp']['buckets']:
                    sip  = sIp_row['key']
                    if fisrt_time > sIp_row['aggs']['value']:
                        fisrt_time = sIp_row['aggs']['value']
                        if last_time < sIp_row['max_price']['value']:
                            last_time = sIp_row['max_price']['value']

        ip_dc=get_ip_domain_and_cert(dip)
        tid = tid + 1
        ip_t = get_ip_info(dip)
        http_ip_server_t.append((tid,dip,ip_t['black_list'],list_to_pdflist(ip_t['tag_info']),list_to_pdflist(port_app_list)
                                ,conect_num,len(ip_t['finger_client_info']),list_to_pdflist(ip_dc['domain_list']),list_to_pdflist(ip_dc['cert_list']),time_to_str(fisrt_time),time_to_str(last_time)))


# iii.	IP列表
def http_ip_list(finger):
    body_ac = {"query": {
        "bool": {"should": [{"match": {"tcp_s_finger": str(finger)}}], "must": [{"match": {"IPPro": 17}}]}},
     "aggs": {"sIp": {"terms": {"field": "sIp.keyword"}, "aggs": {"sPort": {"terms": {"field": "sPort"}, "aggs": {
         "max_price": {"max": {"field": "StartTime"}}, "aggs": {"min": {"field": "StartTime"}}}}}}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    http_ip_list_t.append(("序号","类型","IP","威胁权重","标签","访问端口","拥有指纹数量","会话数量"))
    if "aggregations" not in result:
        return
    tid = 0

    for row in result["aggregations"]['sIp']['buckets']:
        sIp = row['key']
        first_time = 9999999999
        last_time = 0
        port_app_list=[]

        for sPort_row in row['sPort']['buckets']:
            sPort = sPort_row['key']
            connect_num = sPort_row['doc_count']
            for appid_row in sPort_row['sPort']['buckets']:
                app_name = str( appid_row['key'] )
                if  appid_row['key'] in appid_appname:
                    app_name = appid_appname[appid_row['key']]
                port_app_list.append(str(sPort)+"-"+app_name)
                if first_time > appid_row['aggs']['value']:
                    first_time = appid_row['aggs']['value']
                if last_time < appid_row['max_price']['value']:
                    last_time = appid_row['max_price']['value']

        ip_t = get_ip_info(sIp)
        tid=tid + 1
        http_ip_list_t.append((tid,isIP4or6(sIp),sIp,ip_t['black_list'],list_to_pdflist(ip_t['tag_info']),list_to_pdflist(port_app_list),len(ip_t['finger_client_info'] ) + len(ip_t['finger_server_info']),connect_num))

def get_domain_info(domain):
    domain_t = {}
    sql = "select a.block_list from tb_domain_info a  where a.n_domain =  \'"+domain+"\'"
    relust = s_mysql(sql ,cursor)
    if len(relust) > 0:
        domain_t['block_list'] =relust[0]['block_list']

    domain_t['tag_info'] = []
    sql_w = "select a.tag_id from tb_domain_tag a where a.n_domain = \'"+domain+"\'"
    relust = s_mysql(sql_w, cursor)
    for row in relust:
        domain_t['tag_info'].append(tag_info(row['tag_id']))


#   关联域名
def http_server_corr_domain(finger):
    body_ac ={"query":{"bool":{"should":[{"match":{"http_s_finger":str(finger)}}]}},"aggs":{"domain":{"terms":{"field":"Host"},"aggs":{"dPort":{"terms":{"field":"dPort"},"aggs":{"c_finger":{"terms":{"field":"http_c_finger.keyword"},"aggs":{"dIp":{"terms":{"field":"dIp.keyword"},"aggs":{"sIp":{"terms":{"field":"sIp.keyword"},"aggs":{"max_price":{"max":{"field":"StartTime"}},"aggs":{"min":{"field":"StartTime"}}}}}}}}}}}}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    http_server_corr_domain_t.append(("序号","域名","威胁等级","标签","开放端口","会话数量","客户端指纹数量","服务器IP","首次发现时间","末次发现时间"))
    if "aggregations" not in result:
        return
    for row in result["aggregations"]['domain']['buckets']:
        domain = row['key']
        connect_num = row['doc_count']
        port_app_list = []
        c_finger_list = []
        dIp_list = []
        sIp_list = []
        first_time = 9999999999
        last_time = 0
        n_tid  =  0
        for dPort_row in row['dPort']['buckets']:
            port_app_list.append(str(dPort_row['key'])+"-HTTP")
            for c_finger_row in  dPort_row['c_finger']['buckets']:
                c_finger_list.append(c_finger_row['key'])
                for dIp_row in c_finger_row['dIp']['buckets']:
                    dIp_list.append(dIp_row['key'])
                    for sIp_row in dIp_row['dIp']['buckets']:
                        sIp_list.append(sIp_row['key'])
                        if first_time > sIp_row['aggs']['value']:
                            first_time = sIp_row['aggs']['value']
                        if last_time < sIp_row['max_price']['value']:
                            last_time = sIp_row['max_price']['value']


        #
        doamin_t = get_domain_info(domain)
        n_tid = n_tid +1
        http_server_corr_domain_t.append((n_tid,domain,doamin_t['block_list'],list_to_pdflist(doamin_t['tag_info']),list_to_pdflist(c_finger_list),len(sIp_list),list_to_pdflist(dIp_list),first_time,last_time ))


def http_ip_list(finger):
    body_ac = {"query": {
        "bool": {"should": [{"match": {"tcp_s_finger": str(finger)}}], "must": [{"match": {"IPPro": 17}}]}},
        "aggs": {"sIp": {"terms": {"field": "sIp.keyword"}, "aggs": {"sPort": {"terms": {"field": "sPort"},"aggs": {"appId": {"terms": {"field": "AppId"}, "aggs": {
         "max_price": {"max": {"field": "StartTime"}}, "aggs": {"min": {"field": "StartTime"}}}}}}}}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    http_ip_list_t.append(("序号","类型","IP","威胁权重","标签","访问端口","拥有指纹数量","会话数量"))
    if "aggregations" not in result:
        return
    tid = 0

    for row in result["aggregations"]['sIp']['buckets']:
        sIp = row['key']
        first_time = 9999999999
        last_time = 0
        port_app_list=[]

        for sPort_row in row['sPort']['buckets']:
            sPort = sPort_row['key']
            connect_num = sPort_row['doc_count']
            for appid_row in sPort_row['appId']['buckets']:
                app_name  = str(appid_row['key'])
                if appid_row['key'] in appid_appname:
                    app_name = appid_appname[appid_row['key']]
                port_app_list.append(str(sPort)+"-"+app_name)
                if first_time > appid_row['aggs']['value']:
                    first_time = appid_row['aggs']['value']
                if last_time < appid_row['max_price']['value']:
                    last_time = appid_row['max_price']['value']

        ip_t = get_ip_info(sIp)
        tid=tid + 1
        http_ip_list_t.append((tid,isIP4or6(sIp),sIp,ip_t['black_list'],list_to_pdflist(ip_t['tag_info']),list_to_pdflist(port_app_list),len(ip_t['finger_client_info'] ) + len(ip_t['finger_server_info']),connect_num))





# c)	告警会话
def http_alarm_session_info(finger):
    body_ac = {"query":{"bool":{"should":[{"match":{"http_c_finger":str(finger)}},{"match":{"http_s_finger":str(finger)}}],"must":[{"match":{"IPPro":17}}]}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    finger_session_info={}
    for row in result['hits']['hits']:
        finger_session_info[row['_source']['SessionId']] = row['_source']

    http_alarm_info_t.append(("序号","会话ID","源IP","源端口","目的IP","目的端口","协议号","威胁等级","标签","上行数据量","下行数据量","起始时间","结束时间"))
    sql = "select target_name as session_id ,tag_id  from tb_alarm where target_type = 6  "
    relust = s_mysql(sql , cursor)
    #tcp_alarm_info_t.append(())
    n_tid = 0;
    for row in relust:
        sesson_id =  row['session_id']
        tag_id  = row['tag_id']
        if sesson_id in finger_session_info:
            n_tid = n_tid + 1
            w=finger_session_info[sesson_id]
            http_alarm_info_t.append((n_tid,sesson_id ,w['sIp'],w['sPort'],w['dIp'],w['dPort'],
                                     w['IPPro'],tag_info[tag_id]['black_list'],tag_info[tag_id]['tag_text'],w['pkt']['pkt_dbytes'],w['pkt']['pkt_sbytes'],time_to_str(w['StartTime']),time_to_str(w['EndTime'])))





# SSL
# 任务分布

def ssl_task_info(finger):
    ssl_task_info_t.append(("序号","任务","客户端热度","客户端IP数量","会话数量","首次发现时间","末次发现时间"))
    if finger_info['type'] != 1:
        return
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    body_ar = {"query": {
        "bool": {"should": [{"match": {"ssl_s_finger": str(finger)}}], "must": [{"match": {"IPPro": 17}}]}},
     "aggs": {"task": {"terms": {"field": "TaskId"}, "aggs": {"sIp": {"terms": {"field": "sIp.keyword"}, "aggs": {
         "max_price": {"max": {"field": "StartTime"}}, "aggs": {"min": {"field": "StartTime"}}}}}}}}
    result = es.search(index=index_name, body=body_ar)
    if "aggregations" not in  result :
        return
    tid=  0
    for row in result["aggregations"]['task']['buckets']:
        ip_t  = []
        task_id  = row['key']
        connect_num = row['doc_count']
        first_time = 999999999
        last_time = 0
        for sIp_row in row['sIp']['buckets']:
            ip_t.append(sIp_row['key'])
            if first_time >  sIp_row['aggs']['value']:
                first_time = sIp_row['aggs']['value']
            if last_time > sIp_row['max_price']['value']:
                last_time = sIp_row['max_price']['value']

        #ip_t = list(set(ip_t))
        t_id = t_id + 1
        ssl_task_info_t.append((tid,task_id,clientdis_consrt(ip_t),len(ip_t),connect_num,time_to_str(first_time),time_to_str(last_time)))


# ii.	时序分布
def ssl_finger_shixu(finger):
    min_time = max_time - 3600 * 24
    body_ar ={"query":{"bool":{"should":should,"must":[{"match":{"IPPro":17}}]}},"aggs":{"prices":{"histogram":{"field":"StartTime","interval":3600,"order":{"_key":"desc"}}}}}

    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ar)
    if "aggregations" not in result:
        return
    tid = 0
    for row in result["aggregations"]['prices']['buckets']:
        if row['key'] < min_time :
            continue


# iii.	IP列表
def ssl_ip_list(finger):
    body_ac = {"query": {
        "bool": {"should": [{"match": {"tcp_s_finger": str(finger)}}], "must": [{"match": {"IPPro": 17}}]}},
        "aggs": {"sIp": {"terms": {"field": "sIp.keyword"}, "aggs": {"sPort": {"terms": {"field": "sPort"},"aggs": {"appId": {"terms": {"field": "AppId"}, "aggs": {
         "max_price": {"max": {"field": "StartTime"}}, "aggs": {"min": {"field": "StartTime"}}}}}}}}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    ssl_ip_list_t.append(("序号","类型","IP","威胁权重","标签","访问端口","拥有指纹数量","会话数量"))
    if "aggregations" not in result:
        return
    tid = 0

    for row in result["aggregations"]['sIp']['buckets']:
        sIp = row['key']
        first_time = 9999999999
        last_time = 0
        port_app_list=[]

        for sPort_row in row['sPort']['buckets']:
            sPort = sPort_row['key']
            connect_num = sPort_row['doc_count']
            for appid_row in sPort_row['appId']['buckets']:
                app_name = str(appid_row['key'])
                if  appid_row['key'] in appid_appname:
                    app_name = appid_appname[appid_row['key']]
                port_app_list.append(str(sPort)+"-"+app_name)
                if first_time > appid_row['aggs']['value']:
                    first_time = appid_row['aggs']['value']
                if last_time < appid_row['max_price']['value']:
                    last_time = appid_row['max_price']['value']

        ip_t = get_ip_info(sIp)
        tid=tid + 1
        ssl_ip_list_t.append((tid,isIP4or6(sIp),sIp,ip_t['black_list'],list_to_pdflist(ip_t['tag_info']),list_to_pdflist(port_app_list),len(ip_t['finger_client_info'] ) + len(ip_t['finger_server_info']),connect_num))

# ssl  服务器 ：
def ssl_finger_to_serverip(finger):
    body_ac = {"query":{"bool":{"should":[{"match":{"tcp_s_finger":str(finger)}}],"must":[{"match":{"IPPro":17}}]}},"aggs":{"dIp":{"terms":{"field":"dIp.keyword"},"aggs":{"AppId":{"terms":{"field":"AppId"},"aggs":{"dPort":{"terms":{"field":"dPort"},"aggs":{"sIp":{"terms":{"field":"sIpi.keyword"},"aggs":{"max_price":{"max":{"field":"StartTime"}},"aggs":{"min":{"field":"StartTime"}}}}}}}}}}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    ssl_ip_server_t.append(("序号","服务器","威胁等级","标签","开放端口","会话数量","客户端指纹数量","关联域名","关联证书","首次发现时间","末次发现时间"))
    if "aggregations" not in result:
        return
    tid = 0

    for row in result["aggregations"]['dIp']['buckets']:
        dip =  row['key']
        fisrt_time = 999999999
        last_time = 0
        port_app_list = []
        conect_num = row['doc_count']
        for AppId_row in row['AppId']['buckets']:
            app_name  = str(AppId_row['key'])
            if  AppId_row['key'] in appid_appname:
                app_name = appid_appname[AppId_row['key']]
            for dPort_row in AppId_row['dPort']['buckets']:
                dport = dPort_row['key']
                port_app_list.append(str(dport)+"-"+app_name)
                for sIp_row  in dPort_row['sIp']['buckets']:
                    sip  = sIp_row['key']
                    if fisrt_time > sIp_row['aggs']['value']:
                        fisrt_time = sIp_row['aggs']['value']
                        if last_time < sIp_row['max_price']['value']:
                            last_time = sIp_row['max_price']['value']

        ip_dc=get_ip_domain_and_cert(dip)
        tid = tid + 1
        ip_t = get_ip_info(dip)
        ssl_ip_server_t.append((tid,dip,ip_t['black_list'],list_to_pdflist(ip_t['tag_info']),list_to_pdflist(port_app_list)
                                ,conect_num,len(ip_t['finger_client_info']),list_to_pdflist(ip_dc['domain_list'])))

def get_cert_sha1_info(cert_sha1):
    cert_info = {}
    sql = "select  a.cert_json,a.black_list ,a.not_before ,a.not_after from tb_cert_info a   where  a.cert_sha1 == \'"+cert_sha1+"\'"
    reslut  = s_mysql(sql,cursor)
    if len(reslut) == 0:
        return cert_info
    else :
        cert_info['block_list'] =reslut[0]['block_list']
        CertJson = json.loads(reslut[0]['cert_json'])
        row = reslut[0]
        cert_info["cert_sha1"] = cert_sha1
        cert_info['before'] = row['not_before']  # 签发时间
        cert_info['after'] = row['not_after']  # 失效时间
        cert_info['own'] = ""
        if 'Subject' in CertJson:
            if 'CN' in CertJson['Subject']:
                cert_info['own'] = CertJson['Subject']['CN']  # 拥有者
        # 签发机构
        cert_info['issuer'] = ""
        #
        if 'Issuer' in CertJson:
            if 'CN' in CertJson['Issuer']:
                cert_info['issuer'] = CertJson['Issuer']['CN']

        # 算法
        cert_info['sufa'] = ""
        if 'SignatureAlgorithm' in CertJson:
            cert_info['sufa'] = CertJson['SignatureAlgorithm']
            # 序号
        cert_info['seq'] = ""
        if 'SerialNumber' in CertJson:
            cert_info['seq'] = CertJson['SerialNumber']

            # key
        cert_info['KeyUsage'] = ""
        if 'Extension' in CertJson:
            if 'extendedKeyUsage' in CertJson['Extension']:
                cert_info['KeyUsage'] = CertJson['Extension']['extendedKeyUsage']
            # domain
            if 'subjectAltName' in CertJson['Extension']:
                cert_info['domain'] = CertJson['Extension']['subjectAltName'].replace("Dns:", "", 1000)

    #
    tag_t = []
    sql = "select tag_id from tb_cert_tag where  cert_sha1 = \'"+cert_sha1 +"\'"
    reslut  = s_mysql(sql,cursor)
    for row in reslut:
        tag_t.append(tag_info(row['tag_id']))
    cert_info["tag"] = tag_t
    return cert_info

# 关联证书  # c)	关联证书链
def ssl_finger_corr_cert(finger):
    body_ac = {"query":{"bool":{"should":[{"match":{"ssl_s_finger":str(finger)}}]}},"aggs":{"cert_list":{"terms":{"field":"Cert_s_Hash"},"aggs":{"dPort":{"terms":{"field":"dPort"},"aggs":{"dIp":{"terms":{"field":"dIp.keyword"},"aggs":{"sIp":{"terms":{"field":"sIp.keyword"},"aggs":{"c_finger":{"terms":{"field":"ssl_c_finger.keyword"},"aggs":{"max_price":{"max":{"field":"StartTime"}},"aggs":{"min":{"field":"StartTime"}}}}}}}}}}}}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    finger_corr_certlist_t.append(("序号","证书链","叶子证书","威胁等级","标签","开放端口","会话数量","客户端指纹数量","服务器IP","首次发现时间","末次发现时间"))
    finger_corr_cert_t.append(
        ("序号", "证书", "威胁等级", "标签", "证书指纹", "所有者", "签发机构", "授权域名", "签发时间", "失效时间"))
    if "aggregations" not in result:
        return
    tid = 0
    cert_tid = 0

    for row in result["aggregations"]['cert_list']['buckets']:
        cert_list = row['key']
        dIp_list = []
        dPort_list = []
        sIp_list = []
        finger_list = []
        connect_num = row['doc_count']
        first_time = 999999999
        last_time = 0
        for dIp_row in row['dIp']['buckets']:
            dIp_list.append(dIp_row['key'])

            for dPort_row in row['dPort']['buckets']:
                dPort_list.append(dPort_row['key'])
                for sIp_row in row['sIp']['buckets']:
                    sIp_list.append(sIp_row['key'])
                    for finger_row in row['c_finger']['buckets']:
                        finger_list.append(finger_row['key'])
                        if first_time > finger_row['aggs']['value']:
                            first_time = finger_row['aggs']['value']
                        if last_time < finger_row['max_price']['value']:
                            last_time = finger_row['max_price']['value']
        cert_json = json.load(cert_list)
        cert_sha1 =cert_json[0]
        cert_info = get_cert_sha1_info(cert_sha1)
        tid =  tid + 1

        finger_corr_certlist_t.append((tid,cert_list,cert_sha1,cert_info['block_list'],list_to_pdftable(cert_info['tag']),list_to_pdftable(dPort_list),
                                connect_num,len(finger_list),list_to_pdftable(sIp_list),time_to_str(first_time),time_to_str(last_time)))
        cert_tid = cert_tid + 1
        finger_corr_cert_t.append((cert_tid,cert_sha1,cert_info['block_list'],list_to_pdftable(cert_info['tag']),cert_info['own'],cert_info['issuer'],list_to_pdftable(cert_info['domain']),time_to_str(cert_info['before']),time_to_str(cert_info['not_after'])))

#     关联域名
        #   关联域名
def ssl_server_corr_domain(finger):
    body_ac = {"query": {"bool": {"should": [{"match": {"http_s_finger": str(finger)}}]}}, "aggs": {
                "domain": {"terms": {"field": "Host"}, "aggs": {"dPort": {"terms": {"field": "dPort"}, "aggs": {
                "c_finger": {"terms": {"field": "http_c_finger.keyword"}, "aggs": {"dIp": {"terms": {"field": "dIp.keyword"},
                "aggs": {"sIp": {"terms": {"field": "sIpi.keyword"},"aggs": {"max_price": {"max": {"field": "StartTime"}},"aggs": {"min": {
                "field": "StartTime"}}}}}}}}}}}}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    ssl_server_corr_domain_t.append(
                ("序号", "域名", "威胁等级", "标签", "开放端口", "会话数量", "客户端指纹数量", "服务器IP", "首次发现时间", "末次发现时间"))
    if "aggregations" not in result:
        return
    for row in result["aggregations"]['domain']['buckets']:
        domain = row['key']
        connect_num = row['doc_count']
        port_app_list = []
        c_finger_list = []
        dIp_list = []
        sIp_list = []
        first_time = 9999999999
        last_time = 0
        n_tid = 0
        for dPort_row in row['dPort']['buckets']:
            port_app_list.append(str(dPort_row['key']) + "-HTTP")
            for c_finger_row in dPort_row['c_finger']['buckets']:
                c_finger_list.append(c_finger_row['key'])
                for dIp_row in c_finger_row['dIp']['buckets']:
                    dIp_list.append(dIp_row['key'])
                    for sIp_row in dIp_row['dIp']['buckets']:
                        sIp_list.append(sIp_row['key'])
                        if first_time > sIp_row['aggs']['value']:
                            first_time = sIp_row['aggs']['value']
                        if last_time < sIp_row['max_price']['value']:
                            last_time = sIp_row['max_price']['value']

                #
            doamin_t = get_domain_info(domain)
            n_tid = n_tid + 1
            ssl_server_corr_domain_t.append((n_tid, domain, doamin_t['block_list'],
                                                  list_to_pdflist(doamin_t['tag_info']), list_to_pdflist(c_finger_list),
                                                  len(sIp_list), list_to_pdflist(dIp_list), first_time, last_time))


# c)	告警会话
def ssl_alarm_session_info(finger):
    body_ac = {"query":{"bool":{"should":[{"match":{"http_c_finger":str(finger)}},{"match":{"http_s_finger":str(finger)}}],"must":[{"match":{"IPPro":17}}]}}}
    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0 :
        index_name = "connectinfo_"+str(argv_json['task_id'])+"_*"  
    result = es.search(index=index_name, body=body_ac)
    finger_session_info={}
    for row in result['hits']['hits']:
        finger_session_info[row['SessionId']] = row

    ssl_alarm_info_t.append(("序号","会话ID","源IP","源端口","目的IP","目的端口","协议号","威胁等级","标签","上行数据量","下行数据量","起始时间","结束时间"))
    sql = "select target_name as session_id ,tag_id  from tb_alarm where target_type = 6  "
    relust = s_mysql(sql , cursor)
    #tcp_alarm_info_t.append(())
    n_tid = 0;
    for row in relust:
        sesson_id =  row['session_id']
        tag_id  = finger_session_info['tag_id']
        if sesson_id in finger_session_info:
            n_tid = n_tid + 1
            w=finger_session_info[sesson_id]
            ssl_alarm_info_t.append((n_tid,sesson_id ,w['sIp'],w['sPort'],w['dIp'],w['dPort'],
                                     w['IPPro'],tag_info[tag_id]['black_list'],tag_info[tag_id]['tag_text'],w['pkt']['pkt_dbytes'],w['pkt']['pkt_sbytes'],time_to_str(w['StartTime']),time_to_str(w['EndTime'])))

class Graphs:
    def __init__(self):
        pass
    # 绘制标题
    @staticmethod
    def draw_title(name_title, fontName, fontSize, leading, alignment):
        style = getSampleStyleSheet()
        ct = style['Normal']
        ct.fontName = fontName
        ct.fontSize = fontSize
        #  设置行距
        ct.leading = leading
        #  颜色
        ct.textColor = colors.black
        #  居中
        ct.alignment = alignment
        #  添加标题并居中
        title = Paragraph(name_title, ct)
        return title

def to_pdf(finger):
    global finger_base_t
    global finger_attr_t
    global aralm_info_t
    global aralm_list
    global task_info_t
    global finger_corr_finger_t
    global tcp_task_info_t
    global tcp_ip_list_t
    global tcp_ip_server_t
    global tcp_ip_client_t
    global tcp_alarm_info_t
    global finger_info


    global  http_task_info_t  # http 任务分布
    global  http_ip_server_t   # http关联服务器
    global  http_alarm_info_t
    global  http_server_corr_domain_t

    # SSL
    global ssl_alarm_info_t
    global ssl_server_corr_domain_t
    global finger_corr_cert_t
    global finger_corr_certlist_t
    global ssl_task_info_t
    global ssl_ip_server_t
    global all_session_num 
    global all_server_ip_list
    global all_client_ip_list
    content = list()
    # %添加标题
    content.append(Graphs.draw_title("指纹 ["+str(finger) +"]", 'msyhbd', 18, 50, 1))
    content.append(Graphs.draw_title("分析报告", 'msyhbd', 18, 50, 1))

    content.append(Graphs.draw_title("一 基本信息", 'msyh', 14, 30, 0))
    content.append(Graphs.draw_title(" 1 分析结果", 'msyh', 12, 30, 0))
    content.append(Table(finger_base_t, colWidths=125,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title(" 2 目标属性", 'msyh', 12, 30, 0))
    finger_attr_t.append(("关联指纹数量",len(finger_corr_finger_t),"会话数量",all_session_num))
    finger_attr_t.append(("服务器热度",iplist_to_iphot(all_server_ip_list),"会话数量",iplist_to_iphot(all_client_ip_list)))
    finger_attr_t.append(("访问域名数量",len(http_server_corr_domain_t) + len(ssl_server_corr_domain_t),"访问证书数量",len(finger_corr_cert_t)))
    if len(finger_attr_t) > 0:
        content.append(Table(finger_attr_t, colWidths=125,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title(" 3 告警信息", 'msyh', 12, 30, 0))
    content.append(Table(aralm_info_t, colWidths=[30,50,50,50,50,50,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("二 任务归属", 'msyh', 14, 30, 0))
    content.append(Graphs.draw_title(" 1 数据统计", 'msyh', 12, 30, 0))
    content.append(Table(task_info_t, colWidths=[30,40,60,80,70,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("三 关联分析", 'msyh', 14, 30, 0))
    content.append(Graphs.draw_title(" 1 关联指纹", 'msyh', 12, 30, 0))
    content.append(Table(finger_corr_finger_t, colWidths=[30,85,45,45,45,75,40,45,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("三 会话分析", 'msyh', 14, 30, 0))
    content.append(Graphs.draw_title(" a) 指纹所有者", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("  i 任务分布", 'msyh', 12, 30, 0))
    content.append(Table(tcp_task_info_t, colWidths=[30,40,55,55,45,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("  ii 时序分布", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("  iii ip列表", 'msyh', 12, 30, 0))
    content.append(Table(tcp_ip_list_t, colWidths=[30,30,75,45,30,105,75,45],
                        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                               ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                               ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("b) 访问服务器", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("  i 服务器IP列表", 'msyh', 12, 30, 0))
    content.append(Table(tcp_ip_server_t, colWidths=[30,95,45,30,75,45,55,55,70,50,50],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("  ii 访问应用", 'msyh', 12, 30, 0))
    content.append(Table(tcp_ip_client_t, colWidths=[30,45,45,45,65,75,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    content.append(Graphs.draw_title("c) 告警会话", 'msyh', 12, 30, 0))
    content.append(Table(tcp_alarm_info_t, colWidths=[30,45,75,30,75,45,40,45,30,55,55,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    # HTTP
    content.append(Graphs.draw_title("2 HTTP协议", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title(" a) 指纹所有者", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("  i 任务分布", 'msyh', 12, 30, 0))
    content.append(Table(http_task_info_t, colWidths=[30,40,55,55,85,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("  ii 时序分布", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("  iii ip列表", 'msyh', 12, 30, 0))
    content.append(Table(http_ip_list_t, colWidths=[30,30,75,45,30,105,75,45],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title(" a) 访问服务器", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("  i 服务器IP列表", 'msyh', 12, 30, 0))
    content.append(Table(http_ip_server_t, colWidths=[30,95,45,30,75,45,55,55,70,50,50],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("  c) 关联域名", 'msyh', 12, 30, 0))
    content.append(Table(http_server_corr_domain_t, colWidths=55,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title("  d) 告警会话", 'msyh', 12, 30, 0))
    content.append(Table(http_alarm_info_t, colWidths=[30,45,75,30,75,45,40,45,30,55,55,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # SSL/TLS 协议
    content.append(Graphs.draw_title("2 SSL/TLS协议", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title(" a) 指纹所有者", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("  i 任务分布", 'msyh', 12, 30, 0))

    content.append(Graphs.draw_title("c) 告警会话", 'msyh', 12, 30, 0))
    content.append(Table(ssl_task_info_t, colWidths=[30,40,55,55,105,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    content.append(Graphs.draw_title("  ii 时序分布", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("  iii ip列表", 'msyh', 12, 30, 0))
    content.append(Table(ssl_ip_list_t, colWidths=[30,30,75,45,30,105,75,45],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title(" a) 访问服务器", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("  i 服务器IP列表", 'msyh', 12, 30, 0))
    content.append(Table(ssl_ip_server_t, colWidths=[30,95,45,30,75,45,55,55,70,50,50],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    #
    content.append(Graphs.draw_title(" c) 关联证书链", 'msyh', 12, 30, 0))
    content.append(Table(finger_corr_certlist_t, colWidths=[30,75,45,45,45,45,45,65,65,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title(" c) 关联证书", 'msyh', 12, 30, 0))
    content.append(Table(finger_corr_cert_t, colWidths=[30,75,45,45,45,75,75,65, 65,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title(" e) 关联域名", 'msyh', 12, 30, 0))
    content.append(Table(ssl_server_corr_domain_t, colWidths=[30,65,45,30,45,45,75,75,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    content.append(Graphs.draw_title(" d) 会话告警", 'msyh', 12, 30, 0))
    if len(ssl_alarm_info_t) > 0:
        content.append(Table(ssl_alarm_info_t, colWidths=[30,45,75,30,75,45,40,45,30,55,55,85,85],
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    doc = SimpleDocTemplate(file_name, pagesize=letter)
    doc.build(content)

def PasreToPdf(finger_sha1):
    global finger_base_t
    global finger_attr_t
    global aralm_info_t
    global aralm_list
    global task_info_t
    global finger_corr_finger_t
    global tcp_task_info_t
    global tcp_ip_list_t
    global tcp_ip_server_t
    global tcp_ip_client_t
    global tcp_alarm_info_t
    global finger_info

    global http_task_info_t  # http 任务分布
    global http_ip_server_t  # http关联服务器
    global http_alarm_info_t
    global http_server_corr_domain_t

    # SSL
    global ssl_alarm_info_t
    global ssl_server_corr_domain_t
    global finger_corr_cert_t
    global finger_corr_certlist_t
    global ssl_task_info_t
    global ssl_ip_server_t
    global all_session_num 
    global all_server_ip_list
    global all_client_ip_list
    aralm_info_t = []
    all_session_num = 0
    all_server_ip_list = []
    all_client_ip_list = []

    finger_base_info(finger_sha1)
    if  'finger' in finger_info:
        finger = finger_info['finger']
    else:
        return 
    #aralm_info(finger)
    task_info_info(finger)
    corr_finger_finger(finger)
    tcp_task_info(finger)
    tcp_finger_shixu(finger)
    tcp_ip_list(finger)
    tcp_finger_to_serverip(finger)
    tcp_finger_to_appid(finger)
    tcp_alarm_session_info(finger)

    http_task_info(finger)
    http_finger_shixu(finger)
    http_finger_to_serverip(finger)
    http_ip_list(finger)
    http_server_corr_domain(finger)
    http_ip_list(finger)
    http_alarm_session_info(finger)
    ssl_task_info(finger)
    ssl_finger_shixu(finger)
    ssl_ip_list(finger)
    ssl_finger_to_serverip(finger)
    ssl_finger_corr_cert(finger)
    ssl_server_corr_domain(finger)
    ssl_server_corr_domain(finger)
    to_pdf(finger)

    # ---
def argv_parse(argv) :
    global file_name
    global argv_json
    print(len(argv))
    if len(argv) !=3:
        print("参数错误")
        sys.exit(1)
    str1 = argv[1]
    file_name = argv[2]
    print(str1)
    argv_json= json.loads(str(str1))
    value = argv_json['target_value']
    argv_json['value_type'] = 1 # 单IP
    vaulelist = value.split(",")
    if len(vaulelist) > 1 :
        argv_json['value_type'] =  2  #  列举
    else :
        vaulelist = value.split("--")
        if len(vaulelist) == 1 :
            argv_json['value_type'] = 3  # IP 区域
    if 'task_id' not in  argv_json:
        argv_parse['task_id'] = 0

def init():
    tag_info_nuk()
    #print("***")
init()
argv_parse(sys.argv)
print(argv_json)
PasreToPdf(argv_json['target_value'] )


