# Last Update:2020-03-02 11:42:46
##
# @file analisys_common_tools.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-02-27


import os,sys
import time
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.pagesizes import letter
import matplotlib as mpl
mpl.use('Agg')
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas  as pd


from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak, Table, TableStyle
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.units import mm
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from netaddr.ip import IPAddress
import re 
mpl.rcParams['font.sans-serif']=['SimHei']  #设置为黑体字
# 注册字体
pdfmetrics.registerFont(TTFont('msyh', '/opt/GeekSec/web/rule_syn/Fonts/msyh.ttc'))
pdfmetrics.registerFont(TTFont('msyhbd', '/opt/GeekSec/web/rule_syn/Fonts/msyhbd.ttc'))
pdfmetrics.registerFont(TTFont('msyhl', '/opt/GeekSec/web/rule_syn/Fonts/msyhl.ttc'))

def list_to_pdftable(slist):
    c_str = ""
    num = 1
    for row in slist:
        if c_str =="":
            if type(row) != type(""):
                c_str  = str(row)
            else:
                c_str =  row
        else:
            if type(row) != type(""):
                c_str =c_str +"\n"+ str(row)
            else:
                c_str =c_str +"\n"+ row
        num = num +1 
        if num > 5:
            return c_str

    return c_str



def list_to_pdflist(slist):
    c_str = ""
    num = 1
    print(slist)
    for row in slist:
        if c_str =="":
            if type(row) != type(""):
                c_str = str(row)
            else:
                c_str =  row
        else:
            if type(row) != type(""):
                c_str =c_str +"\n"+ str(row)
            else:
                c_str =c_str +"\n"+ row
        num = num +1 
        if num > 5:
            return c_str
    return c_str
# 热力图
def drew_heatmap_png(data ,xLabel , yLabel ,title_name,png_file_name):
    print(data)
    print(xLabel)
    print(yLabel)
    df = pd.DataFrame(data = data ,index = xLabel ,columns = yLabel)
    print(df)
    ax= sns.heatmap(df,   center=0, annot=True)
    # Decorations
    plt.title(title_name, fontsize=12)
    plt.xticks(fontsize=6)
    plt.yticks(fontsize=6)
    plt.rcParams['savefig.dpi'] = 100  # 图片像素
    plt.rcParams['figure.dpi'] = 20  # 分辨率
    plt.rcParams['figure.figsize'] = (15.0, 15.0)  # 尺寸
    #ax.set_xticks(len(xLabel))
    #ax.set_yticks(len(yLabel))
    plt.savefig(png_file_name)
    plt.show()
    plt.clf()


def drew_countplot_png( data ,xLabel , yLabel ,title_name,png_file_name):
    df = pd.DataFrame(data = data ,index = xLabel ,columns = yLabel)
    sns.countplot(x="class", hue="who", data=df)
    # Decorations
    plt.title(title_name, fontsize=22)
    plt.xticks(fontsize=12)
    plt.yticks(fontsize=12)
    plt.rcParams['savefig.dpi'] = 100  # 图片像素
    plt.rcParams['figure.dpi'] = 20  # 分辨率
    plt.rcParams['figure.figsize'] = (15.0, 15.0)  # 尺寸
    #ax.set_xticks(len(xLabel))
    #ax.set_yticks(len(yLabel))
    plt.savefig(png_file_name)
    plt.show()
    plt.clf()
# 
def drew_barplot_png( xLabel,data ,title_name,png_file_name):
    sns.barplot(xLabel,data)
    # Decorations
    plt.title(title_name, fontsize=9)
    plt.xticks(fontsize=6)
    plt.yticks(fontsize=6)
    plt.rcParams['savefig.dpi'] = 100  # 图片像素
    plt.rcParams['figure.dpi'] = 20  # 分辨率
    #plt.rcParams['figure.figsize'] = (15.0, 15.0)  # 尺寸
    #ax.set_xticks(len(xLabel))
    #ax.set_yticks(len(yLabel))
    plt.savefig(png_file_name)
    plt.show()
    plt.clf()


def time_to_all_str(ltime):
    #转换成localtime
    time_local = time.localtime(ltime)
    #转换成新的时间格式(2016-05-05 20:28:54)
    dt = time.strftime("%Y-%m-%d %H:%M:%S",time_local)
    #print (dt)
    return dt
def time_to_str(ltime):
    #转换成localtime
    time_local = time.localtime(ltime)
    #转换成新的时间格式(2016-05-05 20:28:54)
    dt = time.strftime("%Y:%m:%d\n%H:%M:%S",time_local)
    #print (dt)
    return dt
def time_to_hour_str(ltime):
    #转换成localtime
    time_local = time.localtime(ltime)
    #转换成新的时间格式(2016-05-05 20:28:54)
    dt = time.strftime("%H",time_local)
    #print (dt)
    return dt





def isIP4or6(cfgstr):


    if '/' in cfgstr:
        text = cfgstr[:cfgstr.rfind('/')]
    else:
        text = cfgstr
    addr = IPAddress(text)
    if addr.version == 4 :
        return "IPV4"
    elif addr.version == 6:
        return "IPV6"
    else:
        return ""

def longstr_to_pdf_t(src_str,leng):
   for aa in src_str: 
      b=re.findall(r'.{leng}',aa) 
      c='\n'.join(b) 
   return c 

def longstr_to_pdf(src_str):
   for aa in src_str: 
      b=re.findall(r'.{10}',aa) 
      c='\n'.join(b) 
   return c 

