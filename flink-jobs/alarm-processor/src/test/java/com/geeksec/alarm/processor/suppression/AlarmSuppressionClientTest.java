package com.geeksec.alarm.processor.suppression;

import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 告警抑制规则客户端测试
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
class AlarmSuppressionClientTest {
    
    private MockWebServer mockWebServer;
    private AlarmSuppressionClient suppressionClient;
    private AlarmProcessorConfig config;
    
    @BeforeEach
    void setUp() throws IOException {
        mockWebServer = new MockWebServer();
        mockWebServer.start();
        
        config = new AlarmProcessorConfig();
        config.setAlarmServiceBaseUrl(mockWebServer.url("/").toString().replaceAll("/$", ""));
        
        suppressionClient = new AlarmSuppressionClient(config);
    }
    
    @AfterEach
    void tearDown() throws IOException {
        if (suppressionClient != null) {
            suppressionClient.shutdown();
        }
        mockWebServer.shutdown();
    }
    

    
    @Test
    void testIsAlarmInWhitelist_WhenAlarmInWhitelist_ShouldReturnTrue() throws InterruptedException {
        // Given
        String responseBody = """
                {
                    "success": true,
                    "data": {
                        "inWhitelist": true,
                        "matchedType": "ALARM",
                        "matchedValue": "********->*************:malware_detected"
                    }
                }
                """;
        mockWebServer.enqueue(new MockResponse()
                .setBody(responseBody)
                .addHeader("Content-Type", "application/json"));
        
        // When
        boolean result = suppressionClient.shouldSuppress(createTestAlarm());
        
        // Then
        assertThat(result).isTrue();
        
        RecordedRequest request = mockWebServer.takeRequest();
        assertThat(request.getPath()).isEqualTo("/alarm-suppression/check");
        String requestBody = request.getBody().readUtf8();
        assertThat(requestBody).contains("*************");
        assertThat(requestBody).contains("********");
        assertThat(requestBody).contains("malware_detected");
    }
    
    @Test
    void testIsAttackChainInWhitelist_WhenChainInWhitelist_ShouldReturnTrue() throws InterruptedException {
        // Given
        String responseBody = """
                {
                    "success": true,
                    "data": {
                        "inWhitelist": true,
                        "matchedType": "ATTACK_CHAIN"
                    }
                }
                """;
        mockWebServer.enqueue(new MockResponse()
                .setBody(responseBody)
                .addHeader("Content-Type", "application/json"));
        
        List<String> attackChain = Arrays.asList("*************_********_malware_detected");
        
        // When
        boolean result = suppressionClient.shouldSuppressAttackChain(attackChain);
        
        // Then
        assertThat(result).isTrue();
        
        RecordedRequest request = mockWebServer.takeRequest();
        assertThat(request.getPath()).isEqualTo("/alarm-suppression/check/attack-chain");
    }
    
    @Test
    void testShouldSuppress_WhenAlarmShouldBeSupressed_ShouldReturnTrue() throws InterruptedException {
        // Given
        String responseBody = """
                {
                    "success": true,
                    "data": {
                        "shouldSuppress": true,
                        "matchedType": "ALARM"
                    }
                }
                """;
        mockWebServer.enqueue(new MockResponse()
                .setBody(responseBody)
                .addHeader("Content-Type", "application/json"));
        
        Alarm alarm = createTestAlarm();
        
        // When
        boolean result = suppressionClient.shouldSuppress(alarm);
        
        // Then
        assertThat(result).isTrue();
        
        RecordedRequest request = mockWebServer.takeRequest();
        assertThat(request.getPath()).isEqualTo("/alarm-suppression/check");
        String requestBody = request.getBody().readUtf8();
        assertThat(requestBody).contains("victim");
        assertThat(requestBody).contains("attacker");
        assertThat(requestBody).contains("label");
    }
    

    
    @Test
    void testShouldSuppress_WhenAlarmShouldNotBeSupressed_ShouldReturnFalse() throws InterruptedException {
        // Given
        String responseBody = """
                {
                    "success": true,
                    "data": {
                        "shouldSuppress": false,
                        "matchedType": "ALARM"
                    }
                }
                """;
        mockWebServer.enqueue(new MockResponse()
                .setBody(responseBody)
                .addHeader("Content-Type", "application/json"));
        
        Alarm alarm = createTestAlarm();
        
        // When
        boolean result = suppressionClient.shouldSuppress(alarm);
        
        // Then
        assertThat(result).isFalse();
        
        RecordedRequest request = mockWebServer.takeRequest();
        assertThat(request.getPath()).isEqualTo("/alarm-suppression/check");
    }
    
    @Test
    void testHttpError_ShouldReturnFalse() {
        // Given
        mockWebServer.enqueue(new MockResponse().setResponseCode(500));
        
        // When
        boolean result = suppressionClient.shouldSuppress(createTestAlarm());
        
        // Then
        assertThat(result).isFalse();
    }
    
    @Test
    void testInvalidJson_ShouldReturnFalse() {
        // Given
        mockWebServer.enqueue(new MockResponse()
                .setBody("invalid json")
                .addHeader("Content-Type", "application/json"));
        
        // When
        boolean result = suppressionClient.shouldSuppress(createTestAlarm());
        
        // Then
        assertThat(result).isFalse();
    }
    
    /**
     * 创建测试告警对象
     */
    private Alarm createTestAlarm() {
        Alarm alarm = new Alarm();
        alarm.setSrcIp("********");
        alarm.setDstIp("*************");
        alarm.setAlarmType("malware_detected");
        return alarm;
    }
}
