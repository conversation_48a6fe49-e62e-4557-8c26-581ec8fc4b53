# Last Update:2021-09-01 14:17:44
##
# @file analysis_mac_situation.py
# @brief: 分析平台 网络态势 处理 
# <AUTHOR>
# @version 0.1.00
# @date 2020-05-19

import sys ,os 
import json,pymysql
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
# 初始化设备名称 和  设备产商 
batch_id = ""
base_json ={} 
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
    db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    cursor = db.cursor()
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
device_name = {}
device_organization = {}
mac_info = {}
def init():
    sql = "select distinct ifnull(device_name ,\"\") as device_name,  ifnull(state,0) as state  ,mac  from tb_network_config  "
    relust = s_mysql(sql,cursor)
    for row in relust :
        device_name['mac'] = row['device_name']
    sql = " select organization_name , assignment from tb_mac_info_value  "
    relust = s_mysql(sql,cursor)
    for row in  relust:
        device_organization[row['assignment']] = row['organization_name']
def int_task_mac_info(task_id):
    sql =  "select a.batch_id from tb_task_batch  a where a.task_id = "+task_id +" order by a.batch_id desc limit 0 , 1"
    relust = s_mysql(sql , cursor)
    batch_id = relust[0]['batch_id']
    global mac_info
    mac_info = {}
    mac_info_file = "/data/File/"+str(task_id)+"/"+str(batch_id)+"/mac_ip_bind_t"
    if os.path.exists(mac_info_file) :
        mac_info  = json.load(open(mac_info_file))
mac_situation = { "mac_list":{}}
def mac_ip_list_func(mac):
    mac_ip_list = []
    mac_port_list = []
    if mac in mac_info:
        for k in mac_info[mac] :
            mac_ip_list.append(k)
            for port in mac_info[mac][k]:
                mac_port_list.append(port)
    return (list(set(mac_ip_list)),list(set(mac_port_list)))
def  get_device_name(mac):
    if mac in device_name:
        return device_name[mac]
    else:
        return  ""
def get_device_organization(mac):
    macstr = mac.replace(":","",10)[0:9].upper()
    print(macstr)
    #print(dev_mac_organization_name)
    if macstr in device_organization:
        return device_organization[macstr]
    else :
        macstr = macstr[0:6]
    if  macstr in device_organization:
        return device_organization[macstr]
    return ""
def get_alarm_num(mac):
    sql = "select count(distinct alarm_id) as num from  tb_alarm_extend_target a where a.target_type = 5  and a.target_name = '"+mac +"'"
    relust = s_mysql(sql,cursor)
    return relust[0]['num']
def add_mac_list(mac):
    if mac not in mac_situation['mac_list']:
        t = {}
        t["mac"] = mac
        t_list  = mac_ip_list_func(mac)
        t["ip_list"] = t_list[0]
        t["port_list"] = t_list[1]
        t["device_name"] =get_device_name(mac)
        t["device_organization"] =get_device_organization(mac)
        t['alarm_num'] = get_alarm_num(mac)
        mac_situation['mac_list'][mac] = t

def task_mac_situation(task_id):
    global mac_situation 
    mac_to_mac_file = "/data/File/"+str(task_id)+"/batch_mac_mac_t_all"
    if os.path.exists(mac_to_mac_file):
        mac_to_mac = json.load(open(mac_to_mac_file))
        for k in mac_to_mac:
            t = mac_to_mac[k]
            add_mac_list(t['mac1'])
            add_mac_list(t['mac2'])
        mac_situation['mac_mac'] = mac_to_mac
        print(mac_situation)
        json.dump(mac_situation,open("/data/File/"+str(task_id)+"/mac_situation",'w'))


if __name__ == '__main__':
    if len(sys.argv)  == 2 :
        init()
        task_id = sys.argv[1]
        int_task_mac_info(task_id)
        task_mac_situation(task_id)
    else:
        print("参数错误")

