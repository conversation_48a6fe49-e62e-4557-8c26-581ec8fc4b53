import sys
sys.path.append("./")

from PyGksec.GkHelper.GkAnayTaskHelper import G_AnayModel
from PyGksec.GkHelper.KnowledgeHelper import Benign_DNS_Servers
from PyGksec.GkUtils import common

import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.AlarmHelper as alarm_helper
import PyGksec.GkHelper.ESHelper as eh

from Detect.Domain.DNS_Tunnnel.load_tunnel_feature import load_feature_data_of_detail
from Detect.Domain.DNS_Tunnnel.train_model import predict_df

TUNNEL_SHOLD = 0.9

def check_servers(df):
    if df is None:
        return
    df = predict_df(df)
    df = df[df.y_pred>TUNNEL_SHOLD]
    return df

def detect_dns_tunnel(task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="dns",task_id=task_id)
    benign_servers = list(Benign_DNS_Servers)
    detail["query"]["bool"]["must_not"].append({"terms": {"sIp":benign_servers}})  
    detail["query"]["bool"]["must_not"].append({"terms": {"dIp":benign_servers}})      
    df = load_feature_data_of_detail(table,detail)
    black_df = check_servers(df)
    if black_df is None:
        return
    black_df.key.map(lambda server : \
        tag_helper.add_tag_for_target_toNebula(tag_text="DNS隧道服务器",target=server,task_id=task_id))


class DNS_TunnelDetectTask(G_AnayModel):
     def run(self,task_id,batch_id):
        detect_dns_tunnel(task_id,batch_id)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("dns*")
    detect_dns_tunnel(task_id,batch_id)    




