{{- if and .Values.services.knowledge-base.enabled .Values.services.knowledge-base.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: knowledge-base-hpa
  namespace: {{ .Values.global.namespace | default "nta" }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: knowledge-base
  minReplicas: {{ .Values.services.knowledge-base.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.services.knowledge-base.autoscaling.maxReplicas }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.services.knowledge-base.autoscaling.targetCPUUtilizationPercentage }}
{{- end }}
