# Last Update:2020-12-31 17:16:16
##
# @file rule_syn.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-06-11

import os,sys,json
hostlist = []
hostpath="/opt/GeekSec/pubconfig/hostlist.json"
if os.path.exists(hostpath):
    hostlist_t = json.load(open(hostpath))
    hostlist = hostlist_t["host"]
print(hostlist)
if len(hostlist) == 0:
    sys.exit(0)
th_mode = {"th":"online"}
if os.path.exists("/opt/GeekSec/STL/mode/.th_mode.json"):
    th_mode = json.load(open("/opt/GeekSec/STL/mode/.th_mode.json"))
filelist = ["/opt/GeekSec/th/bin/conf/all_session.xml","/opt/GeekSec/th/bin/conf/th_engine_conf.xml","/opt/GeekSec/th/bin/conf/write_pcap.xml","/opt/GeekSec/th/bin/Config/Config.txt","/opt/GeekSec/th/bin/JsonRule/BasicRule/*"]
def syn_conf(filepath,host):
    target_file = filepath[0:filepath.rfind("/")]
    os.system("scp -r "+filepath + " root@"+host+":"+target_file)
def stop_thd(host):
    os.system("ssh root@"+host+ " \"/etc/init.d/thdd stop\"")
def start_thd(host):
    os.system("ssh root@"+host+ " \"/etc/init.d/thdd restart\"")
if len(sys.argv) == 1:
    for host in hostlist:
        # @ 杀死thdd
        stop_thd(host)
        # 同步文件
        for filepath in filelist:
            syn_conf(filepath , host)
        # 启动thdd
        if th_mode["th"]  == "online":
            start_thd(host)
if sys.argv == "stop":
    for host in hostlist:
        # @ 杀死thdd
        stop_thd(host)
elif sys.argv == "start": 
    for host in hostlist:
       # 启动thdd
       start_thd(host)


