{{- if .Values.infrastructure.postgresql.enabled }}
# PostgreSQL 大文件数据初始化作业
# 将大型 CSV 文件复制到 PVC 中
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-postgresql-large-data-init
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: postgresql-data-init
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "5"  # 在 PVC 创建之后执行
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  backoffLimit: 3
  activeDeadlineSeconds: 3600  # 1小时超时
  template:
    metadata:
      labels:
        app: postgresql-large-data-init-job
    spec:
      restartPolicy: Never
      containers:
        - name: data-init
          image: {{ .Values.images.alpine.repository }}:{{ .Values.images.alpine.tag }}
          imagePullPolicy: {{ .Values.images.alpine.pullPolicy | default "IfNotPresent" }}
          command:
            - /bin/sh
            - -c
            - |
              set -e
              echo "开始初始化大文件数据到 PVC..."
              
              # 创建目录结构
              mkdir -p /data/csv/large
              mkdir -p /data/csv/optimized
              
              # 复制大文件（这里需要根据实际情况调整）
              # 在实际部署时，这些文件应该通过其他方式（如 initContainer、sidecar 等）提供
              echo "创建示例大文件结构..."
              
              # 创建占位符文件，实际部署时替换为真实数据
              cat > /data/csv/large/domain_knowledge.csv << 'EOF'
              # 大型域名知识库数据文件
              # 实际部署时应替换为真实数据
              domain,category,threat_level,description
              example.com,benign,0,示例域名
              EOF
              
              cat > /data/csv/large/tb_domain_alexa.csv << 'EOF'
              # Alexa 域名排名数据文件
              # 实际部署时应替换为真实数据
              rank,domain,category
              1,google.com,search
              EOF
              
              cat > /data/csv/large/tb_threat_info.csv << 'EOF'
              # 威胁情报数据文件
              # 实际部署时应替换为真实数据
              threat_id,threat_type,severity,description
              1,malware,high,示例威胁
              EOF
              
              # 设置权限
              chmod -R 755 /data
              
              echo "大文件数据初始化完成"
              echo "数据目录结构:"
              find /data -type f -name "*.csv" -exec ls -lh {} \;
              
          volumeMounts:
            - name: large-data-volume
              mountPath: /data
      volumes:
        - name: large-data-volume
          persistentVolumeClaim:
            claimName: {{ .Release.Name }}-postgresql-large-data
{{- end }}
