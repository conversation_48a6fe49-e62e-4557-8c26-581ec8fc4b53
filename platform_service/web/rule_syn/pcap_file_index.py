import os
import sys
import json
fileList = []
err_fileList = []
begin_time = 0 
end_time =  0 
def scanPath(filePath,suffix):
    print("开始扫描[{0}]".format(filePath))
    if not os.path.isdir(filePath): ##判断是否是目录
        print("{0} 不是目录".format(filePath))
        exit(-1)
    for filename in os.listdir(filePath):   ##遍历子目录
        if os.path.isdir((filePath + "/" + filename)):
            scanPath(filePath + "/" + filename,suffix)
        else:
            if filename.endswith(".pcap") or filename.endswith(".cap") or filename.endswith(".pcapng"):
                if  begin_time == 0 or filename.isdigit()  == False :
                    fileList.append(os.path.join(filePath ,filename))
                else :
                    file_name_time = int(int(filename.split(".")[0])/60)
                    if begin_time == 0:
                        fileList.append(os.path.join(filePath ,filename))
                    else:
                        if file_name_time > begin_time and  file_name_time < end_time :
                            fileList.append(os.path.join(filePath ,filename))
            else:
                err_fileList.append(os.path.join(filePath ,filename))
####warning 下面这行代码会删除目录下的文件，请慎用！！！！！！

def listWirteFile(flist,filename):
    #f = open('file_index.txt', 'w')
    f = open(filename, 'w')
    for i in flist:
        f.write(i+"\n")
    f.close()
if len(sys.argv) == 2 or len(sys.argv) == 4 :
    pass 
else :
    print("参数错误:")
    print("正确命令格式为: python3 pcap_file_index.py pcap文件目录" )
    sys.exit(1)
os.system("rm -rf  file_index.txt")
path=sys.argv[1]
if len(sys.argv) == 4:
    if  sys.argv[2] != '0' :
        begin_time = int(int(sys.argv[2])/3600)
        end_time = int(int(sys.argv[3])/3600) +24
if os.path.isfile(path):
    os.system("\cp  -rf " +path + "    file_index.txt" )
    os.system("sed -i 's/\r$//g'  file_index.txt")
else:
   scanPath(path,"")
   fileList1 = sorted(fileList)
   print(fileList1)
   listWirteFile(fileList1,"file_index.txt")
#listWirteFile(err_fileList,"/opt/GeekSec/task/STL/err_fileList.json")
if os.path.exists("/opt/GeekSec/task/STL/"):
    json.dump(err_fileList,open("/opt/GeekSec/task/STL/err_fileList.json","w+"))
os.system("cp -rf file_index.txt /opt/GeekSec/th/bin/")
