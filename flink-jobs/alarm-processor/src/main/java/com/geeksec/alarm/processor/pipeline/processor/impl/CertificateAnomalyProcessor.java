package com.geeksec.alarm.processor.pipeline.processor.impl;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.alarm.processor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 证书异常告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class CertificateAnomalyProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.CERTIFICATE_ANOMALY;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        String anomalyType = getCertificateAnomalyType(alarm);
        String certSubject = getCertificateSubject(alarm);
        
        // 证书异常检测
        reasons.add(createDetectionReason(
                "证书异常",
                String.format("检测到%s类型的证书异常", anomalyType),
                "正常有效证书",
                String.format("证书主体: %s", certSubject),
                "合法CA签发的证书",
                6
        ));
        
        // 证书有效性检查
        if (isCertificateExpired(alarm)) {
            reasons.add(createDetectionReason(
                    "证书过期",
                    "检测到使用已过期的SSL/TLS证书",
                    "有效期内证书",
                    String.format("过期时间: %s", getCertificateExpireTime(alarm)),
                    "有效证书",
                    5
            ));
        }
        
        // 自签名证书检测
        if (isSelfSignedCertificate(alarm)) {
            reasons.add(createDetectionReason(
                    "自签名证书",
                    "检测到使用自签名证书",
                    "CA签发证书",
                    "自签名证书",
                    "权威CA证书",
                    4
            ));
        }
        
        // 证书链验证失败
        if (hasCertificateChainError(alarm)) {
            reasons.add(createDetectionReason(
                    "证书链验证失败",
                    "证书链验证过程中发现错误",
                    "完整证书链",
                    getCertificateChainError(alarm),
                    "有效证书链",
                    7
            ));
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        String anomalyType = getCertificateAnomalyType(alarm);
        
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        String.format("验证%s证书异常的具体原因", anomalyType),
                        "检查证书的颁发机构和有效性",
                        "确认是否为中间人攻击或钓鱼网站",
                        "暂时阻断可疑的SSL/TLS连接"
                ))
                .investigationSteps(Arrays.asList(
                        "分析证书的详细信息和签名",
                        "确认证书异常的影响范围",
                        "检查是否有其他相关的安全事件",
                        "验证服务器的真实身份",
                        "分析证书使用的时间和频率"
                ))
                .preventionMeasures(Arrays.asList(
                        "加强SSL/TLS证书的监控和验证",
                        "实施证书透明度日志监控",
                        "更新证书验证规则和策略",
                        "部署证书固定(Certificate Pinning)",
                        "定期审查和更新受信任的CA列表"
                ))
                .recoverySteps(Arrays.asList(
                        "更换或重新颁发有效的SSL/TLS证书",
                        "清理浏览器和系统中的异常证书缓存",
                        "恢复正常的SSL/TLS连接配置",
                        "更新应用程序的证书验证逻辑",
                        "重新建立安全的通信通道",
                        "验证所有SSL/TLS连接的安全性"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        String anomalyType = getCertificateAnomalyType(alarm);
        return String.format("检测到%s等证书异常情况，可能表明存在中间人攻击、" +
                "钓鱼网站、配置错误或恶意服务器等安全风险。", anomalyType);
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "通过分析SSL/TLS握手过程中的证书信息，验证证书的有效性、" +
                "签名、证书链等，识别异常或可疑的证书使用情况。";
    }
    
    @Override
    public List<Map<String, Object>> getTargets(Alarm alarm) {
        List<Map<String, Object>> targets = new ArrayList<>();
        
        if (alarm.getDstIp() != null) {
            Map<String, Object> target = new HashMap<>();
            target.put("ip", alarm.getDstIp());
            target.put("port", alarm.getDstPort());
            target.put("certificate_subject", getCertificateSubject(alarm));
            target.put("anomaly_type", getCertificateAnomalyType(alarm));
            targets.add(target);
        }
        
        return targets;
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99999"; // 证书异常通用模型ID
    }
    
    /**
     * 获取证书异常类型
     */
    private String getCertificateAnomalyType(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object anomalyType = alarm.getExtendedProperties().get("certificate_anomaly_type");
            if (anomalyType != null) {
                return anomalyType.toString();
            }
        }
        return "未知证书异常";
    }
    
    /**
     * 获取证书主体
     */
    private String getCertificateSubject(Alarm alarm) {
        if (alarm.getCertificateInfo() != null) {
            return alarm.getCertificateInfo().getSubjectCn();
        }
        
        if (alarm.getExtendedProperties() != null) {
            Object subject = alarm.getExtendedProperties().get("certificate_subject");
            if (subject != null) {
                return subject.toString();
            }
        }
        
        return "未知证书主体";
    }
    
    /**
     * 检查证书是否过期
     */
    private boolean isCertificateExpired(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object expired = alarm.getExtendedProperties().get("is_certificate_expired");
            if (expired instanceof Boolean) {
                return (Boolean) expired;
            }
        }
        return false;
    }
    
    /**
     * 获取证书过期时间
     */
    private String getCertificateExpireTime(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object expireTime = alarm.getExtendedProperties().get("certificate_expire_time");
            if (expireTime != null) {
                return expireTime.toString();
            }
        }
        return "未知";
    }
    
    /**
     * 检查是否为自签名证书
     */
    private boolean isSelfSignedCertificate(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object selfSigned = alarm.getExtendedProperties().get("is_self_signed");
            if (selfSigned instanceof Boolean) {
                return (Boolean) selfSigned;
            }
        }
        return false;
    }
    
    /**
     * 检查是否有证书链错误
     */
    private boolean hasCertificateChainError(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object chainError = alarm.getExtendedProperties().get("has_certificate_chain_error");
            if (chainError instanceof Boolean) {
                return (Boolean) chainError;
            }
        }
        return false;
    }
    
    /**
     * 获取证书链错误信息
     */
    private String getCertificateChainError(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object chainError = alarm.getExtendedProperties().get("certificate_chain_error");
            if (chainError != null) {
                return chainError.toString();
            }
        }
        return "证书链验证失败";
    }
}
