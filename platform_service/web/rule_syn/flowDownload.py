##
# @file flowDownload.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2021-12-15

import json , os ,sys
def flowToThreadId(flowName):
#    return ["0"]
    tlist = []
    path = "/opt/GeekSec/th/bin/if_info.json"
    if os.path.exists(path) == True:
        js = json.load(open(path))
        for i in js:
            if i["pos"] == flowName:
                for k in i["thread"]:
                    tlist.append(str(k))
    return tlist

ret_msg = {"status":0,"msg":""}
def get_dir(cmd):
    arr = []
    p = os.popen(cmd)
    lines = p.readlines()
    for line in lines:
        arr.append(line.strip('\n'))
    return arr
def pathSize(itlist):
    sum_bytes = 0
    for i in  itlist :
        cmd = "du -sb /data/pcapfiles/" + i  + " | awk '{print $1}'"
        sb = get_dir(cmd)
        #print(sb)
        sum_bytes += int(sb[0])
        if sum_bytes > 10*1024*1024*1024:
            return sum_bytes
    return sum_bytes


if __name__=="__main__":
    if len(sys.argv) != 2:
        ret_msg["msg"] = "脚本参数错误"
        #print(json.dumps(ret_msg))
        sys.exit(1)
    flow = sys.argv[1]
    thread_list = flowToThreadId(flow)
    if len(thread_list) == 0:
        ret_msg["msg"] = "文件过大,文件不能超过10G"
    else:
        sb = pathSize(thread_list)
        if sb > 10*1024*1024*1024:
            ret_msg["msg"] = "文件过大,文件不能超过10G"
        else:
            path = flow+".tar.gz"
            cmd =  "cd /data/pcapfiles && tar -zcf "+flow+".tar.gz"
            for i in thread_list:
                cmd += " "+i;
            os.system(cmd)
            os.system("mv /data/pcapfiles/"+path +  "  /var/ftp/cert_files/")
            ret_msg["msg"] = path
    print(json.dumps(ret_msg,ensure_ascii=False))




