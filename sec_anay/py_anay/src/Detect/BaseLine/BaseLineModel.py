import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.GkAnayTaskHelper import G_AnayModel
from Detect.BaseLine.MacCommuBaseline import check_abnormal_commu
from Detect.BaseLine.DomainBaseline import check_domain_base_line
from Detect.BaseLine.FingerBaseline import check_finger_baseline
from Detect.BaseLine.ServiceBaseline import check_service_baseline
from Detect.BaseLine.NetRangeBaseline import check_net_range_baseline



class MacBaseLineModel(G_AnayModel):
     def run(self,task_id,batch_id):
        check_abnormal_commu(task_id,batch_id)

class DomainBaseLineModel(G_AnayModel):
     def run(self,task_id,batch_id):
        check_domain_base_line(task_id,batch_id)

class FingerBaseLineModel(G_AnayModel):
     def run(self,task_id,batch_id):
        check_finger_baseline(task_id,batch_id)

class ServiceBaseLineModel(G_AnayModel):
     def run(self,task_id,batch_id):
        check_service_baseline(task_id,batch_id)

class NetRangeBaseLineModel(G_AnayModel):
     def run(self,task_id,batch_id):
        check_net_range_baseline(task_id,batch_id)



if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    MacBaseLineModel("mac_baseline_model").run(task_id,batch_id)
    DomainBaseLineModel("domain_baseline_model").run(task_id,batch_id)
    FingerBaseLineModel("finger_baseline_model").run(task_id,batch_id)
    ServiceBaseLineModel("service_baseline_model").run(task_id,batch_id)
    NetRangeBaseLineModel("net_baseline_model").run(task_id,batch_id)
