 #Last Update:2022-10-31 15:40:24
##
# @file import_task_loop.py
# @brief: 循环导入任务 ====== 
# <AUTHOR>
# @version 0.1.00
# @date 2022-10-31

import pymysql,json,time
import threading
from threading import Lock
import  math,os,sys
from elasticsearch import Elasticsearch
lock = Lock()
total = 5
#####  

from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
from loadMysqlPasswd import  mysql_passwd
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
#########################
####
def import_pb_data(task_id , batch_id , path):
     os.system(" cd  /opt/GeekSec/web/rule_syn/PbToKafka/ && python  ReadPbFile.py "+str(task_id)+" "+str(batch_id) + " "+path)
     os.system(" cd  /opt/GeekSec/web/rule_syn/ &&  python3 task_end.py "+str(task_id)+" "+str(batch_id)+ " "+path)
def import_task_pcap(task_id , batch_id ,path,task_type):
    #：//导入的数据类型，1（pcap），2（pb），3（探针数据）
    if  task_type  == 1:
        os.system("cd /opt/GeekSec/web/rule_syn/ &&  ./import_task.sh "+ str(task_id) + " "+str(batch_id) + " "+ path)
    elif task_type == 2 : ##### pb 数据
        import_pb_data(task_id , batch_id , path)
    elif task_type ==3 : #暂时不实现
        pass
def do_task(task_id,batch_id , path , task_type):
    global total
    global lock
    lock.acquire()
    total = total -1
    lock.release()
    import_task_pcap(task_id , batch_id ,path,task_type)
    lock.acquire()
    total = total +1
def update_done(tid):
    db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    cursor = db.cursor()
    sql = "update tb_task_batch  set state=1  where batch_id = "+str(tid)
    idu_mysql(sql,cursor,db) 
    db.close()
def read_mysql(path_list , start_time , end_time ):
    if (total  > 0 ):
       db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
       cursor = db.cursor()
       sql = " select task_id , batch_id  , batch_dir  , data_type from tb_task_batch where state  = 2  and batch_id > 1 limit \G " + str(total) ;
       relset = s_mysql(sql , cursor)
       for row in relset :
           batch_id =  row["batch_id"]
           update_done(d_task_id)
           task_id =  row["task_id"]
           path = row["batch_dir"]
           task_type = row["data_type"]
           #do_task(d_task_id ,json.dumps(task_json) , type)
           t = threading.Thread(target=do_task  , args=(task_id , batch_id , path , task_type ))
           t.start()
           if total < 0:
               total = 0
       db.close()
if __name__ == '__main__':
    while True:
        start_task()
        time.sleep(1)
