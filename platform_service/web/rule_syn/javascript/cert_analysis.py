# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/8/13 19:19
# 文件名称 : cert_analysis.py
# 开发工具 : PyCharm
import json,pymysql.cursors,sys,hashlib
# 将ASC转String
def ascToStr(str):
    strObj = ''
    strArr = str.split(",")
    for row in strArr:
        strObj = strObj + chr(int(row))
    return strObj
# 将String计算Hash
def strToHash(str):
    hhaasshh = hashlib.sha1()
    hhaasshh.update(str.encode("utf8"))
    strHash = hhaasshh.hexdigest()
    return strHash
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
# 获取参数
param = sys.argv[1]
paramStr = ascToStr(param)
#paramStr = '{"type":"Cert_BasicInfo","certSha1":"014763c5af661162129e6f494e8c9a08bbe7e82a"}'
paramHash = strToHash(paramStr)
paramValue = json.loads(paramStr)["certSha1"]
tidb = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='statistical_information_base',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
#passive = {}
data = {}
res_ip = s_mysql("select cinfo.CertSHA1,cinfo.CertJson,cinfo.blackList,cinfo.whiteList,cinfo.remarks from CertInfo cinfo LEFT JOIN CertSha1ToOwnID ownid ON ownid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertOwnInfo oinfo ON oinfo.CertOWNID = ownid.CertOwnID LEFT JOIN CertDNToSha1 dnid ON dnid.CertSha1 = cinfo.CertSHA1 LEFT JOIN CertDNInfo dinfo ON dinfo.DNID = dnid.DNID where cinfo.CertSHA1 = '" + paramValue + "' group by cinfo.CertSHA1",tidb_cursor)
if len(res_ip) > 0:
    res_tag = s_mysql("select t.Tag_Text as tag_name,t.Black_List as black_list,t.White_List as white_list from Cert_TAG c,TAG_INFO t where t.Tag_Id = c.tagId and c.CertSHA1 = '" + paramValue + "'",tidb_cursor)
    data["count"] = 1
    r_s = []
    r_obj = {}
    r_obj["CertSHA1"] = res_ip[0]["CertSHA1"]
    cert_json_s = res_ip[0]["CertJson"].replace("'", '"').replace("\n", "")
    r_obj["CertJson"] = json.loads(cert_json_s)
    r_obj["blackList"] = res_ip[0]["blackList"]
    r_obj["whiteList"] = res_ip[0]["whiteList"]
    r_obj["remarks"] = res_ip[0]["remarks"]
    r_obj["tagList"] = res_tag
    r_s.append(r_obj)
    data["list"] = r_s
#passive["data"] = data
else:
    data["count"] = 0
    data["list"] = []
    #passive["data"] = data
#print(json.dumps(data,ensure_ascii=False))
flag = s_mysql("select count(*) as num from tb_cache where hash = '" + paramHash + "';", tidb_cursor)
if flag[0]['num'] > 0:
    idu_mysql("update tb_cache set text = '" + json.dumps(data,ensure_ascii=False) + "',param = '" + paramStr + "' where hash = '" + paramHash + "';", tidb_cursor, tidb)
else:
    idu_mysql("insert into tb_cache (text,param,hash) values ('" + json.dumps(data,ensure_ascii=False) + "','" + paramStr + "','" + paramHash + "');", tidb_cursor, tidb)
tidb.close()