
import sys
sys.path.append("./")
import time

from PyGksec.GkHelper.TagHelper import send_WS_data
import json

import os
from PyGksec.GkConfig import base_dir
import pandas as pd
import PyGksec.GkHelper.ESHelper as eh
from Detect.Finger.GetFingerFromES import get_finger_data_of_esdata
from Detect.Finger.update_Finger import update_finger_csv
import csv
from GkUtils import common


GKFingerFile = os.path.join(base_dir,"GkData/Knowledge/gk_fingers.csv")
GKFinger_DF = pd.read_csv(GKFingerFile)
geek_ja3_dic = GKFinger_DF[["finger_es", "ja3_hash"]].set_index("finger_es").to_dict(orient='dict')["ja3_hash"]
geek_id_type_dic = GKFinger_DF[["finger_es", "finger_type"]].set_index("finger_es").to_dict(orient='dict')["finger_type"]


# 根据用户给的CSV文件，先根据这个配置文件去增加会话的指纹标签，再给会话打上对应的标签。  
def find_finger_test(task_id,batch_id,filepath):
    Test_Finger_File = os.path.join(base_dir,filepath)
    Test_Finger_DF = pd.read_csv(Test_Finger_File)
    write_finger_tag(Test_Finger_DF)
    session_write={}
    for finger_type in Test_Finger_DF["finger_tag"].tolist():
        ja3_list_es,ja3s_list_es = tran_finger_tag_es_list(finger_type,Test_Finger_DF)
        if ja3_list_es!=[] and ja3s_list_es!=[]:
            table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="connectinfo")
            detail["query"]["bool"]["must"].append({"terms": {"sSSLFinger":ja3_list_es}})
            detail["query"]["bool"]["must"].append({"terms": {"dSSLFinger":ja3s_list_es}})
            detail["query"]["bool"]["must"].append({"term": {"AppName":"APP_SSL"}})
            eh.describe(table,detail)
            df = eh.load_es_data_to_DF(table,detail,ignore_file=True)
            if df is None:
                print("会话中未出现该指纹相关会话")
            else:
                for i in range(len(df["SessionId"].tolist())):
                    session_ID = df["SessionId"].tolist()[i]
                    if session_ID in session_write.keys():
                        session_write[session_ID]["Label_add"].append(finger_type)
                    else:
                        session_write[session_ID]={}
                        session_write[session_ID]["session"]={}
                        session_write[session_ID]["append_data"]={}
                        session_write[session_ID]["session"]["TaskId"] = task_id
                        session_write[session_ID]["session"]["SessionId"] = session_ID
                        session_write[session_ID]["Label_add"]=[]
                        session_write[session_ID]["Label_add"].append(finger_type)

        else:
            print("知识库中未发现该指纹的ES查询值")
    for sessionID in session_write.keys():
        add_tag_for_session(session_write[sessionID]["Label_add"],session_write[sessionID]["session"],Test_Finger_DF)

def tran_finger_tag_es_list(finger_type,finger_df):
    ja3_list=[]
    ja3s_list=[]
    df_finger = finger_df.loc[finger_df["finger_tag"]==finger_type]
    ja3_list = df_finger["ja3"].tolist()[0].split(',')
    ja3s_list = df_finger["ja3s"].tolist()[0].split(',')

    ja3_list_es=[]
    ja3s_list_es=[]
    for ja3 in ja3_list:
        for key in geek_ja3_dic:
            if geek_ja3_dic[key] == ja3:
                ja3_list_es.append(key)
    for ja3s in ja3s_list:
        for key in geek_ja3_dic:
            if geek_ja3_dic[key] == ja3s:
                ja3s_list_es.append(key)    

    return ja3_list_es,ja3s_list_es

def write_finger_tag(finger_df):
    Tag_List = json.loads(finger_df.to_json(orient='index'))
    Tag_ID_Map = dict((x['tag_id'],x) for x in Tag_List.values())
    for info in Tag_ID_Map.values():
        data = {}
        data["Black_List"] = '80'
        data["White_List"] = '0'
        data["Target"] = "session"
        data["Tag_Id"] = str(info["tag_id"])
        data["Property"] = "威胁"
        data["Tag_Text"] = info["finger_tag"]
        data["Tag_Remark"] = info["finger_tag"]
        data['Created_Time']  = str(common.get_now_time())
        data['Last_Created_Time'] = str(common.get_now_time())
        data['type'] = 'TAG_DIC_INSERT'
        data['Tag_Type'] = '1'
        data['Tag_Num'] = '0'
        data["Default_Black_List"] = data["Black_List"]
        data["Default_White_List"] = data["White_List"]
        data["Tag_Target_Type"] = 6
        r = send_WS_data(data)
        data['type'] = 'TAG_DIC_UPDATE'
        send_WS_data(data)


def add_tag_for_session(tag_text_list,session,finger_df):
    """
        会话添加标签的接口
        :session: session_data
    """
    label = []
    Tag_List = json.loads(finger_df.to_json(orient='index'))
    Tag_Text_Map = dict((x['finger_type'],x) for x in Tag_List.values())
    task_id=session["TaskId"]
    target = session["SessionId"]
    for tag_text in tag_text_list:
        label.append(int(Tag_Text_Map[tag_text]["tag_id"]))
    label = str(label).replace("[",'').replace("]",'')
    data = {"type": "DAN_TAG_ES_SESSION_INSERT", 
            "TargetName": str(target), 
            "Tag_Id": label, 
            "Time":str(common.get_now_time()), 
            "TaskId":str(task_id)}
    send_WS_data(data)


if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    update_finger_csv(task_id,batch_id)
    find_finger_test(task_id,batch_id,"GkData/ja3_finger_test.csv")

