# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/8/13 19:19
# 文件名称 : domain_analysis.py
# 开发工具 : PyCharm
import json,pymysql.cursors,sys,hashlib
# 将ASC转String
def ascToStr(str):
    strObj = ''
    strArr = str.split(",")
    for row in strArr:
        strObj = strObj + chr(int(row))
    return strObj
# 将String计算Hash
def strToHash(str):
    hhaasshh = hashlib.sha1()
    hhaasshh.update(str.encode("utf8"))
    strHash = hhaasshh.hexdigest()
    return strHash
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
# 获取参数
param = sys.argv[1]
paramStr = ascToStr(param)
#paramStr = '{"type":"Domain_Analysis","domainName":"hr.xiaomi.com"}'
paramHash = strToHash(paramStr)
paramValue = json.loads(paramStr)["domainName"]
tidb = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
data = {}
res_ip = s_mysql("SELECT dif.Domain_Name,dif.blackList,dif.whiteList,(SELECT COUNT(DISTINCT durl.URL) FROM DNS_URL durl WHERE durl.Domain_Name = dif.Domain_Name) AS linkURL,(SELECT COUNT(DISTINCT Cert_Sha1) FROM DATA_PASSICECERT dcert WHERE dcert.Domain = dif.Domain_Name) AS linkCert,(SELECT SUM(pdns.num) FROM PassiveDNS pdns WHERE pdns.DANAME = dif.Domain_Name) AS numDNS,(SELECT drm.remarks FROM DOMAIN_REMARK drm WHERE drm.Domain_Name = dif.Domain_Name) AS listRemark FROM DOMAIN_INFO dif WHERE dif.Domain_Name = '" + paramValue + "'",tidb_cursor)
if len(res_ip) > 0:
    res_tag = s_mysql("select t.Tag_Text as tag_name,t.Black_List as black_list,t.White_List as white_list from DOMAIN_TAG d,TAG_INFO t where t.Tag_Id = d.Tag_Id and d.Domain_Name = '" + paramValue + "'",tidb_cursor)
    data["count"] = 1
    res_ip[0]["tagList"] = res_tag
    if not res_ip[0]["numDNS"] is None:
        res_ip[0]["numDNS"] = str(res_ip[0]["numDNS"])
    else:
        res_ip[0]["numDNS"] = 0
    data["list"] = res_ip
else:
    data["count"] = 0
    data["list"] = []
#print(json.dumps(data,ensure_ascii=False))
flag = s_mysql("select count(*) as num from tb_cache where hash = '" + paramHash + "';", tidb_cursor)
if flag[0]['num'] > 0:
    idu_mysql("update tb_cache set text = '" + json.dumps(data,ensure_ascii=False) + "',param = '" + paramStr + "' where hash = '" + paramHash + "';", tidb_cursor, tidb)
else:
    idu_mysql("insert into tb_cache (text,param,hash) values ('" + json.dumps(data,ensure_ascii=False) + "','" + paramStr + "','" + paramHash + "');", tidb_cursor, tidb)
tidb.close()
