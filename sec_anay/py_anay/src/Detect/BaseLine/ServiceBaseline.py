import sys
sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common 
from PyGksec.GkConfig import env
from PyGksec.GkHelper.ES_Pattern import get_direction_ip_list

import Detect.BaseLine.BaseLineHelper as bh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.AlarmHelper as alarm_helper

import copy
import numpy as np
import pandas as pd


def load_service_data(table,base_detail):
    detail = copy.deepcopy(base_detail)
    detail["aggs"] = eh.make_term_agg("AppName.keyword","app_name")
    detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE+["APP_DNS"]}})
    data_gen = eh.load_agg_data_of_split_time(table,detail,data_key="app_name")
    result = []
    for data in data_gen:
        result+=data
    return result    


def get_shold(data_list):
    ymax = np.max(data_list)
    ystd = np.std(data_list)
    threadshold = 2*ymax+3*ystd
    return threadshold

def write_service_alarm(df):
    if df is None or df.shape[0]==0:
        return
    df.groupby(["sIp","dIp"],as_index=False).apply(lambda df:
        alarm_helper.add_alarm_of_sessions("服务器基线异常",df,["sIp","dIp"]))

def check_access_service_data(base_list,check_list,match_dict,batch_id):
    if len(base_list)==0 or len(check_list)==0:
        return
    base_df = pd.DataFrame(base_list)[["key","doc_count"]].\
        groupby("key",as_index=False).agg(list)
    base_df["shold"] = base_df.doc_count.map(get_shold)
    check_df = pd.DataFrame(check_list)
    df = pd.merge(check_df,base_df[["key","shold"]],how="left",on="key")
    rdf = df[df.doc_count>df.shold]
    if rdf.shape[0]>0:
        for app_name in rdf.key.drop_duplicates():
            new_match_dict = copy.deepcopy(match_dict)
            new_match_dict.update({"AppName.keyword":app_name})
            table,detail = bh.get_detect_query(batch_id,match_dict=new_match_dict)
            df = eh.load_es_data_to_DF(table,detail)
            write_service_alarm(df)

def check_access_abnormal_service(batch_id,match_dict):
    base_table,base_detail = bh.get_build_query(batch_id,match_dict)
    base_list = load_service_data(base_table,base_detail)
    check_table,check_detail = bh.get_detect_query(batch_id,match_dict)
    check_list = load_service_data(check_table,check_detail)
    check_access_service_data(base_list,check_list,match_dict,batch_id)
    


def check_service_baseline(task_id,batch_id,direction="sIp"):
    table,detail = bh.get_detect_query(batch_id,match_dict={})
    detail["query"]["bool"]["must_not"].append({"terms": {"AppName.keyword":common.ERR_DIRECT_SERVICE+["APP_DNS"]}})
    ips = get_direction_ip_list(table,detail,ip_net=common.InnerNet,direction=direction)
    for ip in ips:
        check_access_abnormal_service(batch_id,match_dict={direction:ip})  


    

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    check_service_baseline(task_id,batch_id)
    