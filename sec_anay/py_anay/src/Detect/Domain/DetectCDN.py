import sys
sys.path.append('./')

import copy
import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper

from PyGksec.GkHelper.ModelHelper.DomainHelper import domain_helper
from PyGksec.GkHelper.ModelHelper.IpHelper import ip_helper

CDN_DOMAIN_TAG = lambda domain:tag_helper.add_tag_for_target_toNebula(tag_text="CDN域名",target=domain)
CDN_IP_TAG = lambda ip:tag_helper.add_tag_for_target_toNebula(tag_text='CDN地址', target=ip)
CLOUD_IP_TAG = lambda ip:tag_helper.add_tag_for_target_toNebula(tag_text='云主机地址', target=ip)

def detect_cdn_domain(batch_id):
    # detect cdn domain
    table, detail = eh.get_basic_query('dns', batch_id=batch_id)
    detail['aggs'] = eh.make_term_agg(query_key='Domain.keyword', result_key='domain')

    for domain_data in eh.load_agg_data_of_split_time(table, detail,data_key='domain'):
        domain_list = [data['key'] for data in domain_data if domain_helper.is_cdn_domain(data['key'])]
        for domain in domain_list:
            CDN_DOMAIN_TAG(domain)
    
def detect_cdn_ip(batch_id):
    # detect cdn ip 
    table, detail = eh.get_basic_query('connectinfo', batch_id=batch_id)
    detail['aggs'] = eh.make_term_agg(query_key='dIp', result_key='dip')

    for dip_data in eh.load_agg_data_of_split_time(table, detail,data_key='dip'):
        ip_list = [data['key'] for data in dip_data]
        for ip in ip_list:
            if not (tag:=ip_helper.check_ip(ip)):
                continue
            CDN_IP_TAG(ip) if tag=='CDN' else CLOUD_IP_TAG(ip)
            
if __name__ == '__main__':
    task_id, batch_id = eh.get_run_arg('dns*')
    detect_cdn_ip(batch_id)
    detect_cdn_domain(batch_id)