"model_id","model_name","model_algorithm","model_type","state","priority","tags","created_by","updated_by"
"99001","智能内网网段检测","协议识别","内网检测","1","100","内网,网段,检测","system","system"
"99002","检测异常证书","特征识别","证书检测","1","100","证书,异常,检测","system","system"
"99003","检测APT29组织发起的攻击","行为识别","APT检测","1","100","APT,APT29,攻击检测","system","system"
"99004","检测APT28组织发起的攻击","行为识别","APT检测","1","100","APT,APT28,攻击检测","system","system"
"99005","检测挖矿行为","特征识别","挖矿检测","1","100","挖矿,检测","system","system"
"99006","检测远程访问木马","行为识别","RAT检测","1","100","RAT,木马,检测","system","system"
"99007","检测Tor匿名网络","特征识别","Tor检测","1","100","Tor,匿名,检测","system","system"
"99008","SSL指纹检测","特征识别","指纹检测","1","100","SSL,指纹,检测","system","system"
"99009","DNS隧道检测","行为识别","DNS检测","1","100","DNS,隧道,检测","system","system"
"99010","Web登录暴力破解检测","行为识别","暴力破解检测","1","100","Web,暴力破解,检测","system","system"
"99011","指纹类型随机森林检测模型","机器学习","指纹检测","1","90","指纹,随机森林,机器学习","system","system"
"99012","指纹随机化登录检测模型","特征识别","指纹检测","1","90","指纹,登录,APP_SCAN","system","system"
"99013","域名检测模型","特征识别","域名检测","1","90","域名,检测","system","system"
"99014","端口扫描检测模型","行为识别","网络属性检测","1","90","端口,扫描,检测","system","system"
"99015","Neoregeo检测模型","特征识别","恶意软件检测","1","90","Neoregeo,恶意软件","system","system"
"99016","RDP登录爆破检测模型","行为识别","暴力破解检测","1","90","RDP,登录,爆破","system","system"
"99017","Oracle登录爆破检测模型","行为识别","暴力破解检测","1","90","Oracle,登录,爆破","system","system"
"99018","MYSQL登录爆破检测模型","行为识别","暴力破解检测","1","90","MYSQL,登录,爆破","system","system"
"99019","SMB登录爆破检测模型","行为识别","暴力破解检测","1","90","SMB,登录,爆破","system","system"
"99020","xRay检测模型","特征识别","恶意软件检测","1","90","xRay,恶意软件","system","system"
"99021","suo5检测模型","特征识别","恶意软件检测","1","90","suo5,恶意软件","system","system"
"99022","BeHinder检测模型","特征识别","恶意软件检测","1","90","BeHinder,恶意软件","system","system"
"99023","antSword检测模型","特征识别","恶意软件检测","1","90","antSword,恶意软件","system","system"
