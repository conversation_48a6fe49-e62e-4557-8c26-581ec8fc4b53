<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Awesome-pyecharts</title>
            <script type="text/javascript" src="https://assets.pyecharts.org/assets/echarts.min.js"></script>
        <script type="text/javascript" src="https://assets.pyecharts.org/assets/themes/macarons.js"></script>

    
</head>
<body>
    <style>.box {  }; </style>
    <div class="box">
                <div id="********************************" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_******************************** = echarts.init(
            document.getElementById('********************************'), 'white', {renderer: 'canvas'});
        var option_******************************** = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                139,
                77,
                50,
                48,
                121,
                138,
                27
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                40,
                108,
                120,
                52,
                26,
                45,
                136
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u54c8\u58eb\u5947",
                "\u8428\u6469\u8036",
                "\u6cf0\u8fea",
                "\u91d1\u6bdb",
                "\u7267\u7f8a\u72ac",
                "\u5409\u5a03\u5a03",
                "\u67ef\u57fa"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u57fa\u672c\u793a\u4f8b",
            "subtext": "\u6211\u662f\u526f\u6807\u9898",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_********************************.setOption(option_********************************);
    </script>
<br/>                <div id="c4bd3f09cba84522ab8204f8844a5047" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_c4bd3f09cba84522ab8204f8844a5047 = echarts.init(
            document.getElementById('c4bd3f09cba84522ab8204f8844a5047'), 'white', {renderer: 'canvas'});
        var option_c4bd3f09cba84522ab8204f8844a5047 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                139,
                40,
                78,
                62,
                27,
                127,
                81
            ],
            "barCategoryGap": "60%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "itemStyle": {
                "normal": {
                    "color": new echarts.graphic.LinearGradient(0, 0, 0, 1, [{                    offset: 0,                    color: 'rgba(0, 244, 255, 1)'                }, {                    offset: 1,                    color: 'rgba(0, 77, 167, 1)'                }], false),
                    "barBorderRadius": [
                        30,
                        30,
                        30,
                        30
                    ],
                    "shadowColor": "rgb(0, 160, 221)"
                }
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A"
            ],
            "selected": {
                "\u5546\u5bb6A": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u5468\u4e00",
                "\u5468\u4e8c",
                "\u5468\u4e09",
                "\u5468\u56db",
                "\u5468\u4e94",
                "\u5468\u516d",
                "\u5468\u65e5"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u6e10\u53d8\u5706\u67f1",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_c4bd3f09cba84522ab8204f8844a5047.setOption(option_c4bd3f09cba84522ab8204f8844a5047);
    </script>
<br/>                <div id="642cc949eb9a43339c25c1a6c17016cd" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_642cc949eb9a43339c25c1a6c17016cd = echarts.init(
            document.getElementById('642cc949eb9a43339c25c1a6c17016cd'), 'white', {renderer: 'canvas'});
        var option_642cc949eb9a43339c25c1a6c17016cd = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "elasticOut",
    "animationDelay": 1000,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                114,
                32,
                145,
                75,
                132,
                149,
                144
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                50,
                129,
                103,
                82,
                72,
                82,
                23
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u5468\u4e00",
                "\u5468\u4e8c",
                "\u5468\u4e09",
                "\u5468\u56db",
                "\u5468\u4e94",
                "\u5468\u516d",
                "\u5468\u65e5"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u52a8\u753b\u914d\u7f6e\u57fa\u672c\u793a\u4f8b",
            "subtext": "\u6211\u662f\u526f\u6807\u9898",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_642cc949eb9a43339c25c1a6c17016cd.setOption(option_642cc949eb9a43339c25c1a6c17016cd);
    </script>
<br/>                <div id="6ecce179a1fe4f5ab6196c582aaa50a5" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_6ecce179a1fe4f5ab6196c582aaa50a5 = echarts.init(
            document.getElementById('6ecce179a1fe4f5ab6196c582aaa50a5'), 'white', {renderer: 'canvas'});
            
        var img = new Image(); img.src = 'https://s2.ax1x.com/2019/07/08/ZsS0fK.jpg';
        
        var option_6ecce179a1fe4f5ab6196c582aaa50a5 = {
    "backgroundColor": {
        "type": "pattern",
        "image": img,
        "repeat": "no-repeat"
    },
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                139,
                148,
                63,
                138,
                28,
                34,
                57
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                144,
                66,
                26,
                54,
                73,
                114,
                62
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u54c8\u58eb\u5947",
                "\u8428\u6469\u8036",
                "\u6cf0\u8fea",
                "\u91d1\u6bdb",
                "\u7267\u7f8a\u72ac",
                "\u5409\u5a03\u5a03",
                "\u67ef\u57fa"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u80cc\u666f\u56fe\u57fa\u672c\u793a\u4f8b",
            "subtext": "\u6211\u662f\u526f\u6807\u9898",
            "padding": 5,
            "itemGap": 10,
            "textStyle": {
                "color": "white"
            }
        }
    ]
};
        chart_6ecce179a1fe4f5ab6196c582aaa50a5.setOption(option_6ecce179a1fe4f5ab6196c582aaa50a5);
    </script>
<br/>                <div id="156e19271d89486293b00deaf50342bd" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_156e19271d89486293b00deaf50342bd = echarts.init(
            document.getElementById('156e19271d89486293b00deaf50342bd'), 'macarons', {renderer: 'canvas'});
        var option_156e19271d89486293b00deaf50342bd = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                68,
                95,
                137,
                49,
                122,
                90,
                23
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                64,
                116,
                24,
                36,
                138,
                109,
                65
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u53ef\u4e50",
                "\u96ea\u78a7",
                "\u6a59\u6c41",
                "\u7eff\u8336",
                "\u5976\u8336",
                "\u767e\u5a01",
                "\u9752\u5c9b"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": {
        "text": "Bar-\u901a\u8fc7 dict \u8fdb\u884c\u914d\u7f6e",
        "subtext": "\u6211\u4e5f\u662f\u901a\u8fc7 dict \u8fdb\u884c\u914d\u7f6e\u7684"
    }
};
        chart_156e19271d89486293b00deaf50342bd.setOption(option_156e19271d89486293b00deaf50342bd);
    </script>
<br/>                <div id="f2d31cb9e4a543c48e6e88db56e71927" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_f2d31cb9e4a543c48e6e88db56e71927 = echarts.init(
            document.getElementById('f2d31cb9e4a543c48e6e88db56e71927'), 'white', {renderer: 'canvas'});
        var option_f2d31cb9e4a543c48e6e88db56e71927 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                31,
                52,
                48,
                67,
                65,
                121,
                56
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                117,
                82,
                44,
                95,
                20,
                97,
                123
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": false
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u53ef\u4e50",
                "\u96ea\u78a7",
                "\u6a59\u6c41",
                "\u7eff\u8336",
                "\u5976\u8336",
                "\u767e\u5a01",
                "\u9752\u5c9b"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u9ed8\u8ba4\u53d6\u6d88\u663e\u793a\u67d0 Series",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_f2d31cb9e4a543c48e6e88db56e71927.setOption(option_f2d31cb9e4a543c48e6e88db56e71927);
    </script>
<br/>                <div id="13691085271142079d7b56fe9b8adbf4" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_13691085271142079d7b56fe9b8adbf4 = echarts.init(
            document.getElementById('13691085271142079d7b56fe9b8adbf4'), 'white', {renderer: 'canvas'});
        var option_13691085271142079d7b56fe9b8adbf4 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                117,
                147,
                34,
                60,
                21,
                51,
                83
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                65,
                100,
                74,
                73,
                20,
                109,
                26
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": false,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u6cb3\u9a6c",
                "\u87d2\u86c7",
                "\u8001\u864e",
                "\u5927\u8c61",
                "\u5154\u5b50",
                "\u718a\u732b",
                "\u72ee\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u663e\u793a ToolBox",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "toolbox": {
        "show": true,
        "orient": "horizontal",
        "itemSize": 15,
        "itemGap": 10,
        "left": "80%",
        "feature": {
            "saveAsImage": {
                "show": true,
                "title": "save as image",
                "type": "png"
            },
            "restore": {
                "show": true,
                "title": "restore"
            },
            "dataView": {
                "show": true,
                "title": "data view",
                "readOnly": false
            },
            "dataZoom": {
                "show": true,
                "title": {
                    "zoom": "data zoom",
                    "back": "data zoom restore"
                }
            }
        }
    }
};
        chart_13691085271142079d7b56fe9b8adbf4.setOption(option_13691085271142079d7b56fe9b8adbf4);
    </script>
<br/>                <div id="3cd76d08ef374021a1f6ad527c6602b2" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_3cd76d08ef374021a1f6ad527c6602b2 = echarts.init(
            document.getElementById('3cd76d08ef374021a1f6ad527c6602b2'), 'white', {renderer: 'canvas'});
        var option_3cd76d08ef374021a1f6ad527c6602b2 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                54,
                40,
                118,
                132,
                71,
                140,
                51
            ],
            "barCategoryGap": "80%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A"
            ],
            "selected": {
                "\u5546\u5bb6A": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u54c8\u58eb\u5947",
                "\u8428\u6469\u8036",
                "\u6cf0\u8fea",
                "\u91d1\u6bdb",
                "\u7267\u7f8a\u72ac",
                "\u5409\u5a03\u5a03",
                "\u67ef\u57fa"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u5355\u7cfb\u5217\u67f1\u95f4\u8ddd\u79bb",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_3cd76d08ef374021a1f6ad527c6602b2.setOption(option_3cd76d08ef374021a1f6ad527c6602b2);
    </script>
<br/>                <div id="34d82e7a7ad846e09d71345f01914103" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_34d82e7a7ad846e09d71345f01914103 = echarts.init(
            document.getElementById('34d82e7a7ad846e09d71345f01914103'), 'white', {renderer: 'canvas'});
        var option_34d82e7a7ad846e09d71345f01914103 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                61,
                59,
                93,
                106,
                113,
                85,
                127
            ],
            "barCategoryGap": "20%",
            "barGap": "0%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                137,
                119,
                103,
                125,
                86,
                118,
                67
            ],
            "barCategoryGap": "20%",
            "barGap": "0%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u8349\u8393",
                "\u8292\u679c",
                "\u8461\u8404",
                "\u96ea\u68a8",
                "\u897f\u74dc",
                "\u67e0\u6aac",
                "\u8f66\u5398\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u4e0d\u540c\u7cfb\u5217\u67f1\u95f4\u8ddd\u79bb",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_34d82e7a7ad846e09d71345f01914103.setOption(option_34d82e7a7ad846e09d71345f01914103);
    </script>
<br/>                <div id="ef2e05b1be8a40bab7cbac4c2df2d38c" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_ef2e05b1be8a40bab7cbac4c2df2d38c = echarts.init(
            document.getElementById('ef2e05b1be8a40bab7cbac4c2df2d38c'), 'white', {renderer: 'canvas'});
        var option_ef2e05b1be8a40bab7cbac4c2df2d38c = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                79,
                132,
                105,
                116,
                149,
                123,
                50
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                55,
                138,
                150,
                129,
                61,
                147,
                92
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u886c\u886b",
                "\u6bdb\u8863",
                "\u9886\u5e26",
                "\u88e4\u5b50",
                "\u98ce\u8863",
                "\u9ad8\u8ddf\u978b",
                "\u889c\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "position": "top",
                "margin": 8,
                "formatter": "{value} /\u6708"
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-Y \u8f74 formatter",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_ef2e05b1be8a40bab7cbac4c2df2d38c.setOption(option_ef2e05b1be8a40bab7cbac4c2df2d38c);
    </script>
<br/>                <div id="123e1faf185342f5a19dc804fcd130e8" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_123e1faf185342f5a19dc804fcd130e8 = echarts.init(
            document.getElementById('123e1faf185342f5a19dc804fcd130e8'), 'white', {renderer: 'canvas'});
        var option_123e1faf185342f5a19dc804fcd130e8 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                132,
                131,
                107,
                120,
                71,
                56,
                37
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                149,
                144,
                115,
                51,
                63,
                42,
                135
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "name": "\u6211\u662f X \u8f74",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u6cb3\u9a6c",
                "\u87d2\u86c7",
                "\u8001\u864e",
                "\u5927\u8c61",
                "\u5154\u5b50",
                "\u718a\u732b",
                "\u72ee\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "name": "\u6211\u662f Y \u8f74",
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-XY \u8f74\u540d\u79f0",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_123e1faf185342f5a19dc804fcd130e8.setOption(option_123e1faf185342f5a19dc804fcd130e8);
    </script>
<br/>                <div id="397f200a62064351be30c946ed484f5e" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_397f200a62064351be30c946ed484f5e = echarts.init(
            document.getElementById('397f200a62064351be30c946ed484f5e'), 'white', {renderer: 'canvas'});
        var option_397f200a62064351be30c946ed484f5e = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                85,
                22,
                40,
                81,
                34,
                128,
                142
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "right",
                "margin": 8
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                122,
                77,
                130,
                88,
                38,
                28,
                32
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "right",
                "margin": 8
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u8349\u8393",
                "\u8292\u679c",
                "\u8461\u8404",
                "\u96ea\u68a8",
                "\u897f\u74dc",
                "\u67e0\u6aac",
                "\u8f66\u5398\u5b50"
            ]
        }
    ],
    "title": [
        {
            "text": "Bar-\u7ffb\u8f6c XY \u8f74",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_397f200a62064351be30c946ed484f5e.setOption(option_397f200a62064351be30c946ed484f5e);
    </script>
<br/>                <div id="d586ac6cfef54a789f2dc2f415765869" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_d586ac6cfef54a789f2dc2f415765869 = echarts.init(
            document.getElementById('d586ac6cfef54a789f2dc2f415765869'), 'white', {renderer: 'canvas'});
        var option_d586ac6cfef54a789f2dc2f415765869 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                122,
                150,
                26,
                83,
                62,
                93,
                78
            ],
            "stack": "stack1",
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                139,
                129,
                94,
                24,
                71,
                52,
                34
            ],
            "stack": "stack1",
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u6cb3\u9a6c",
                "\u87d2\u86c7",
                "\u8001\u864e",
                "\u5927\u8c61",
                "\u5154\u5b50",
                "\u718a\u732b",
                "\u72ee\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u5806\u53e0\u6570\u636e\uff08\u5168\u90e8\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_d586ac6cfef54a789f2dc2f415765869.setOption(option_d586ac6cfef54a789f2dc2f415765869);
    </script>
<br/>                <div id="a9bd5b73bea647a892955e1859b3d9e3" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_a9bd5b73bea647a892955e1859b3d9e3 = echarts.init(
            document.getElementById('a9bd5b73bea647a892955e1859b3d9e3'), 'white', {renderer: 'canvas'});
        var option_a9bd5b73bea647a892955e1859b3d9e3 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                137,
                77,
                58,
                114,
                131,
                78,
                76
            ],
            "stack": "stack1",
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                62,
                20,
                95,
                92,
                30,
                135,
                143
            ],
            "stack": "stack1",
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6C",
            "data": [
                144,
                72,
                84,
                28,
                90,
                68,
                34
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B",
                "\u5546\u5bb6C"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true,
                "\u5546\u5bb6C": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u6cb3\u9a6c",
                "\u87d2\u86c7",
                "\u8001\u864e",
                "\u5927\u8c61",
                "\u5154\u5b50",
                "\u718a\u732b",
                "\u72ee\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u5806\u53e0\u6570\u636e\uff08\u90e8\u5206\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_a9bd5b73bea647a892955e1859b3d9e3.setOption(option_a9bd5b73bea647a892955e1859b3d9e3);
    </script>
<br/>                <div id="203431963e2541edba5217e6ea306856" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_203431963e2541edba5217e6ea306856 = echarts.init(
            document.getElementById('203431963e2541edba5217e6ea306856'), 'white', {renderer: 'canvas'});
        var option_203431963e2541edba5217e6ea306856 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                88,
                46,
                52,
                86,
                128,
                128,
                141
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "markPoint": {
                "label": {
                    "show": true,
                    "position": "inside",
                    "color": "#fff",
                    "margin": 8
                },
                "data": [
                    {
                        "name": "\u6700\u5927\u503c",
                        "type": "max"
                    },
                    {
                        "name": "\u6700\u5c0f\u503c",
                        "type": "min"
                    },
                    {
                        "name": "\u5e73\u5747\u503c",
                        "type": "average"
                    }
                ]
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                136,
                141,
                139,
                109,
                23,
                24,
                62
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "markPoint": {
                "label": {
                    "show": true,
                    "position": "inside",
                    "color": "#fff",
                    "margin": 8
                },
                "data": [
                    {
                        "name": "\u6700\u5927\u503c",
                        "type": "max"
                    },
                    {
                        "name": "\u6700\u5c0f\u503c",
                        "type": "min"
                    },
                    {
                        "name": "\u5e73\u5747\u503c",
                        "type": "average"
                    }
                ]
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u886c\u886b",
                "\u6bdb\u8863",
                "\u9886\u5e26",
                "\u88e4\u5b50",
                "\u98ce\u8863",
                "\u9ad8\u8ddf\u978b",
                "\u889c\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-MarkPoint\uff08\u6307\u5b9a\u7c7b\u578b\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_203431963e2541edba5217e6ea306856.setOption(option_203431963e2541edba5217e6ea306856);
    </script>
<br/>                <div id="962631ae42db45bfb861239e99047415" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_962631ae42db45bfb861239e99047415 = echarts.init(
            document.getElementById('962631ae42db45bfb861239e99047415'), 'white', {renderer: 'canvas'});
        var option_962631ae42db45bfb861239e99047415 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                112,
                57,
                92,
                68,
                61,
                28,
                73
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "markPoint": {
                "label": {
                    "show": true,
                    "position": "inside",
                    "color": "#fff",
                    "margin": 8
                },
                "data": [
                    {
                        "name": "\u81ea\u5b9a\u4e49\u6807\u8bb0\u70b9",
                        "coord": [
                            "\u9886\u5e26",
                            92
                        ],
                        "value": 92
                    }
                ]
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                55,
                28,
                46,
                95,
                66,
                66,
                50
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u886c\u886b",
                "\u6bdb\u8863",
                "\u9886\u5e26",
                "\u88e4\u5b50",
                "\u98ce\u8863",
                "\u9ad8\u8ddf\u978b",
                "\u889c\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-MarkPoint\uff08\u81ea\u5b9a\u4e49\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_962631ae42db45bfb861239e99047415.setOption(option_962631ae42db45bfb861239e99047415);
    </script>
<br/>                <div id="e558a4837c7e4ff1b3a71a8a50ae6f11" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_e558a4837c7e4ff1b3a71a8a50ae6f11 = echarts.init(
            document.getElementById('e558a4837c7e4ff1b3a71a8a50ae6f11'), 'white', {renderer: 'canvas'});
        var option_e558a4837c7e4ff1b3a71a8a50ae6f11 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                91,
                60,
                79,
                82,
                150,
                127,
                136
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "markLine": {
                "silent": false,
                "precision": 2,
                "label": {
                    "show": true,
                    "position": "top",
                    "margin": 8
                },
                "data": [
                    {
                        "name": "\u6700\u5c0f\u503c",
                        "type": "min"
                    },
                    {
                        "name": "\u6700\u5927\u503c",
                        "type": "max"
                    },
                    {
                        "name": "\u5e73\u5747\u503c",
                        "type": "average"
                    }
                ]
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                57,
                59,
                132,
                102,
                61,
                126,
                141
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "markLine": {
                "silent": false,
                "precision": 2,
                "label": {
                    "show": true,
                    "position": "top",
                    "margin": 8
                },
                "data": [
                    {
                        "name": "\u6700\u5c0f\u503c",
                        "type": "min"
                    },
                    {
                        "name": "\u6700\u5927\u503c",
                        "type": "max"
                    },
                    {
                        "name": "\u5e73\u5747\u503c",
                        "type": "average"
                    }
                ]
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u53ef\u4e50",
                "\u96ea\u78a7",
                "\u6a59\u6c41",
                "\u7eff\u8336",
                "\u5976\u8336",
                "\u767e\u5a01",
                "\u9752\u5c9b"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-MarkLine\uff08\u6307\u5b9a\u7c7b\u578b\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_e558a4837c7e4ff1b3a71a8a50ae6f11.setOption(option_e558a4837c7e4ff1b3a71a8a50ae6f11);
    </script>
<br/>                <div id="6b745522a22241958dceedc63b20de22" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_6b745522a22241958dceedc63b20de22 = echarts.init(
            document.getElementById('6b745522a22241958dceedc63b20de22'), 'white', {renderer: 'canvas'});
        var option_6b745522a22241958dceedc63b20de22 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                103,
                93,
                91,
                92,
                91,
                129,
                80
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "markLine": {
                "silent": false,
                "precision": 2,
                "label": {
                    "show": true,
                    "position": "top",
                    "margin": 8
                },
                "data": [
                    {
                        "name": "yAxis=50",
                        "yAxis": 50
                    }
                ]
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                75,
                67,
                150,
                89,
                28,
                87,
                137
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": false,
                "position": "top",
                "margin": 8
            },
            "markLine": {
                "silent": false,
                "precision": 2,
                "label": {
                    "show": true,
                    "position": "top",
                    "margin": 8
                },
                "data": [
                    {
                        "name": "yAxis=50",
                        "yAxis": 50
                    }
                ]
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u6cb3\u9a6c",
                "\u87d2\u86c7",
                "\u8001\u864e",
                "\u5927\u8c61",
                "\u5154\u5b50",
                "\u718a\u732b",
                "\u72ee\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-MarkLine\uff08\u81ea\u5b9a\u4e49\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_6b745522a22241958dceedc63b20de22.setOption(option_6b745522a22241958dceedc63b20de22);
    </script>
<br/>                <div id="1e256039938c48598628c6e6a1f82e6a" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_1e256039938c48598628c6e6a1f82e6a = echarts.init(
            document.getElementById('1e256039938c48598628c6e6a1f82e6a'), 'white', {renderer: 'canvas'});
        var option_1e256039938c48598628c6e6a1f82e6a = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                30,
                13,
                28,
                9,
                23,
                8,
                10,
                6,
                11,
                25,
                24,
                8,
                26,
                20,
                1,
                1,
                9,
                28,
                23,
                13,
                10,
                20,
                18,
                14,
                11,
                27,
                30,
                14,
                18,
                24
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A"
            ],
            "selected": {
                "\u5546\u5bb6A": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "0\u5929",
                "1\u5929",
                "2\u5929",
                "3\u5929",
                "4\u5929",
                "5\u5929",
                "6\u5929",
                "7\u5929",
                "8\u5929",
                "9\u5929",
                "10\u5929",
                "11\u5929",
                "12\u5929",
                "13\u5929",
                "14\u5929",
                "15\u5929",
                "16\u5929",
                "17\u5929",
                "18\u5929",
                "19\u5929",
                "20\u5929",
                "21\u5929",
                "22\u5929",
                "23\u5929",
                "24\u5929",
                "25\u5929",
                "26\u5929",
                "27\u5929",
                "28\u5929",
                "29\u5929"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-DataZoom\uff08slider-\u6c34\u5e73\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "dataZoom": {
        "show": true,
        "type": "slider",
        "realtime": true,
        "start": 20,
        "end": 80,
        "orient": "horizontal",
        "zoomLock": false
    }
};
        chart_1e256039938c48598628c6e6a1f82e6a.setOption(option_1e256039938c48598628c6e6a1f82e6a);
    </script>
<br/>                <div id="8978e25e0bd040d38b4b1c99289ecf2d" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_8978e25e0bd040d38b4b1c99289ecf2d = echarts.init(
            document.getElementById('8978e25e0bd040d38b4b1c99289ecf2d'), 'white', {renderer: 'canvas'});
        var option_8978e25e0bd040d38b4b1c99289ecf2d = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#6e7074",
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                30,
                13,
                28,
                9,
                23,
                8,
                10,
                6,
                11,
                25,
                24,
                8,
                26,
                20,
                1,
                1,
                9,
                28,
                23,
                13,
                10,
                20,
                18,
                14,
                11,
                27,
                30,
                14,
                18,
                24
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A"
            ],
            "selected": {
                "\u5546\u5bb6A": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "0\u5929",
                "1\u5929",
                "2\u5929",
                "3\u5929",
                "4\u5929",
                "5\u5929",
                "6\u5929",
                "7\u5929",
                "8\u5929",
                "9\u5929",
                "10\u5929",
                "11\u5929",
                "12\u5929",
                "13\u5929",
                "14\u5929",
                "15\u5929",
                "16\u5929",
                "17\u5929",
                "18\u5929",
                "19\u5929",
                "20\u5929",
                "21\u5929",
                "22\u5929",
                "23\u5929",
                "24\u5929",
                "25\u5929",
                "26\u5929",
                "27\u5929",
                "28\u5929",
                "29\u5929"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-DataZoom\uff08slider-\u5782\u76f4\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "dataZoom": {
        "show": true,
        "type": "slider",
        "realtime": true,
        "start": 20,
        "end": 80,
        "orient": "vertical",
        "zoomLock": false
    }
};
        chart_8978e25e0bd040d38b4b1c99289ecf2d.setOption(option_8978e25e0bd040d38b4b1c99289ecf2d);
    </script>
<br/>                <div id="f1c501898ae1438786d5a2fd1678e448" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_f1c501898ae1438786d5a2fd1678e448 = echarts.init(
            document.getElementById('f1c501898ae1438786d5a2fd1678e448'), 'white', {renderer: 'canvas'});
        var option_f1c501898ae1438786d5a2fd1678e448 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#ca8622",
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                30,
                13,
                28,
                9,
                23,
                8,
                10,
                6,
                11,
                25,
                24,
                8,
                26,
                20,
                1,
                1,
                9,
                28,
                23,
                13,
                10,
                20,
                18,
                14,
                11,
                27,
                30,
                14,
                18,
                24
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A"
            ],
            "selected": {
                "\u5546\u5bb6A": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "0\u5929",
                "1\u5929",
                "2\u5929",
                "3\u5929",
                "4\u5929",
                "5\u5929",
                "6\u5929",
                "7\u5929",
                "8\u5929",
                "9\u5929",
                "10\u5929",
                "11\u5929",
                "12\u5929",
                "13\u5929",
                "14\u5929",
                "15\u5929",
                "16\u5929",
                "17\u5929",
                "18\u5929",
                "19\u5929",
                "20\u5929",
                "21\u5929",
                "22\u5929",
                "23\u5929",
                "24\u5929",
                "25\u5929",
                "26\u5929",
                "27\u5929",
                "28\u5929",
                "29\u5929"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-DataZoom\uff08inside\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "dataZoom": {
        "show": true,
        "type": "inside",
        "realtime": true,
        "start": 20,
        "end": 80,
        "orient": "horizontal",
        "zoomLock": false
    }
};
        chart_f1c501898ae1438786d5a2fd1678e448.setOption(option_f1c501898ae1438786d5a2fd1678e448);
    </script>
<br/>                <div id="4998eb00d59b451893b51fa8d048fbad" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_4998eb00d59b451893b51fa8d048fbad = echarts.init(
            document.getElementById('4998eb00d59b451893b51fa8d048fbad'), 'white', {renderer: 'canvas'});
        var option_4998eb00d59b451893b51fa8d048fbad = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#bda29a",
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                30,
                13,
                28,
                9,
                23,
                8,
                10,
                6,
                11,
                25,
                24,
                8,
                26,
                20,
                1,
                1,
                9,
                28,
                23,
                13,
                10,
                20,
                18,
                14,
                11,
                27,
                30,
                14,
                18,
                24
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A"
            ],
            "selected": {
                "\u5546\u5bb6A": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "0\u5929",
                "1\u5929",
                "2\u5929",
                "3\u5929",
                "4\u5929",
                "5\u5929",
                "6\u5929",
                "7\u5929",
                "8\u5929",
                "9\u5929",
                "10\u5929",
                "11\u5929",
                "12\u5929",
                "13\u5929",
                "14\u5929",
                "15\u5929",
                "16\u5929",
                "17\u5929",
                "18\u5929",
                "19\u5929",
                "20\u5929",
                "21\u5929",
                "22\u5929",
                "23\u5929",
                "24\u5929",
                "25\u5929",
                "26\u5929",
                "27\u5929",
                "28\u5929",
                "29\u5929"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-DataZoom\uff08slider+inside\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "dataZoom": [
        {
            "show": true,
            "type": "slider",
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false
        },
        {
            "show": true,
            "type": "inside",
            "realtime": true,
            "start": 20,
            "end": 80,
            "orient": "horizontal",
            "zoomLock": false
        }
    ]
};
        chart_4998eb00d59b451893b51fa8d048fbad.setOption(option_4998eb00d59b451893b51fa8d048fbad);
    </script>
<br/>                <div id="4ad9d081e17d40e7827066d5fca99bd6" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_4ad9d081e17d40e7827066d5fca99bd6 = echarts.init(
            document.getElementById('4ad9d081e17d40e7827066d5fca99bd6'), 'white', {renderer: 'canvas'});
        var option_4ad9d081e17d40e7827066d5fca99bd6 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#6d8346",
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                53,
                117,
                59,
                82,
                101,
                125,
                120
            ],
            "barCategoryGap": 0,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A"
            ],
            "selected": {
                "\u5546\u5bb6A": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u54c8\u58eb\u5947",
                "\u8428\u6469\u8036",
                "\u6cf0\u8fea",
                "\u91d1\u6bdb",
                "\u7267\u7f8a\u72ac",
                "\u5409\u5a03\u5a03",
                "\u67ef\u57fa"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u76f4\u65b9\u56fe",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_4ad9d081e17d40e7827066d5fca99bd6.setOption(option_4ad9d081e17d40e7827066d5fca99bd6);
    </script>
<br/>                <div id="92e0fc925b8e44f9a34192785573afd1" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_92e0fc925b8e44f9a34192785573afd1 = echarts.init(
            document.getElementById('92e0fc925b8e44f9a34192785573afd1'), 'white', {renderer: 'canvas'});
        var option_92e0fc925b8e44f9a34192785573afd1 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c4ccd3",
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "series0",
            "data": [
                {
                    "name": "\u54c8\u58eb\u5947",
                    "value": 10,
                    "itemStyle": {
                        "color": "#749f83"
                    }
                },
                {
                    "name": "\u8428\u6469\u8036",
                    "value": 20,
                    "itemStyle": {
                        "color": "#749f83"
                    }
                },
                {
                    "name": "\u6cf0\u8fea",
                    "value": 30,
                    "itemStyle": {
                        "color": "#749f83"
                    }
                },
                {
                    "name": "\u91d1\u6bdb",
                    "value": 40,
                    "itemStyle": {
                        "color": "#749f83"
                    }
                },
                {
                    "name": "\u7267\u7f8a\u72ac",
                    "value": 50,
                    "itemStyle": {
                        "color": "#749f83"
                    }
                },
                {
                    "name": "\u5409\u5a03\u5a03",
                    "value": 60,
                    "itemStyle": {
                        "color": "#749f83"
                    }
                },
                {
                    "name": "\u67ef\u57fa",
                    "value": 70,
                    "itemStyle": {
                        "color": "#749f83"
                    }
                },
                {
                    "name": "\u6cb3\u9a6c",
                    "value": 80,
                    "itemStyle": {
                        "color": "#749f83"
                    }
                },
                {
                    "name": "\u87d2\u86c7",
                    "value": 70,
                    "itemStyle": {
                        "color": "#d48265"
                    }
                },
                {
                    "name": "\u8001\u864e",
                    "value": 60,
                    "itemStyle": {
                        "color": "#d48265"
                    }
                },
                {
                    "name": "\u5927\u8c61",
                    "value": 50,
                    "itemStyle": {
                        "color": "#d48265"
                    }
                },
                {
                    "name": "\u5154\u5b50",
                    "value": 40,
                    "itemStyle": {
                        "color": "#d48265"
                    }
                },
                {
                    "name": "\u718a\u732b",
                    "value": 30,
                    "itemStyle": {
                        "color": "#d48265"
                    }
                },
                {
                    "name": "\u72ee\u5b50",
                    "value": 20,
                    "itemStyle": {
                        "color": "#d48265"
                    }
                }
            ],
            "barCategoryGap": 0,
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "series0"
            ],
            "selected": {
                "series0": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u54c8\u58eb\u5947",
                "\u8428\u6469\u8036",
                "\u6cf0\u8fea",
                "\u91d1\u6bdb",
                "\u7267\u7f8a\u72ac",
                "\u5409\u5a03\u5a03",
                "\u67ef\u57fa",
                "\u6cb3\u9a6c",
                "\u87d2\u86c7",
                "\u8001\u864e",
                "\u5927\u8c61",
                "\u5154\u5b50",
                "\u718a\u732b",
                "\u72ee\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u76f4\u65b9\u56fe\uff08\u989c\u8272\u533a\u5206\uff09",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_92e0fc925b8e44f9a34192785573afd1.setOption(option_92e0fc925b8e44f9a34192785573afd1);
    </script>
<br/>                <div id="a03a857279274df08718693dba81afc7" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_a03a857279274df08718693dba81afc7 = echarts.init(
            document.getElementById('a03a857279274df08718693dba81afc7'), 'white', {renderer: 'canvas'});
        var option_a03a857279274df08718693dba81afc7 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                10,
                20,
                30,
                40,
                50,
                40
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                20,
                10,
                40,
                30,
                40,
                50
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "axisLabel": {
                "show": true,
                "position": "top",
                "rotate": -15,
                "margin": 8
            },
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u540d\u5b57\u5f88\u957f\u7684X\u8f74\u6807\u7b7e1",
                "\u540d\u5b57\u5f88\u957f\u7684X\u8f74\u6807\u7b7e2",
                "\u540d\u5b57\u5f88\u957f\u7684X\u8f74\u6807\u7b7e3",
                "\u540d\u5b57\u5f88\u957f\u7684X\u8f74\u6807\u7b7e4",
                "\u540d\u5b57\u5f88\u957f\u7684X\u8f74\u6807\u7b7e5",
                "\u540d\u5b57\u5f88\u957f\u7684X\u8f74\u6807\u7b7e6"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u65cb\u8f6cX\u8f74\u6807\u7b7e",
            "subtext": "\u89e3\u51b3\u6807\u7b7e\u540d\u5b57\u8fc7\u957f\u7684\u95ee\u9898",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_a03a857279274df08718693dba81afc7.setOption(option_a03a857279274df08718693dba81afc7);
    </script>
<br/>                <div id="1d9f6b96e3d14624aba03277e8bea6a0" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_1d9f6b96e3d14624aba03277e8bea6a0 = echarts.init(
            document.getElementById('1d9f6b96e3d14624aba03277e8bea6a0'), 'white', {renderer: 'canvas'});
        var option_1d9f6b96e3d14624aba03277e8bea6a0 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                65,
                63,
                65,
                98,
                117,
                90,
                114
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                31,
                38,
                71,
                125,
                60,
                26,
                143
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u54c8\u58eb\u5947",
                "\u8428\u6469\u8036",
                "\u6cf0\u8fea",
                "\u91d1\u6bdb",
                "\u7267\u7f8a\u72ac",
                "\u5409\u5a03\u5a03",
                "\u67ef\u57fa"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-Graphic \u7ec4\u4ef6\u793a\u4f8b",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "graphic": [
        {
            "type": "group",
            "diffChildrenByName": false,
            "children": [
                {
                    "type": "rect",
                    "$action": "merge",
                    "rotation": 0,
                    "left": "center",
                    "top": "center",
                    "bounding": "all",
                    "z": 100,
                    "zlevel": 0,
                    "silent": false,
                    "invisible": false,
                    "ignore": false,
                    "cursor": "pointer",
                    "draggable": false,
                    "progressive": false,
                    "width": 0,
                    "height": 0,
                    "shape": {
                        "x": 0,
                        "y": 0,
                        "width": 400,
                        "height": 50
                    },
                    "style": {
                        "fill": "rgba(0,0,0,0.3)",
                        "line_width": 0
                    }
                },
                {
                    "type": "text",
                    "$action": "merge",
                    "rotation": 0,
                    "left": "center",
                    "top": "center",
                    "bounding": "all",
                    "z": 100,
                    "zlevel": 0,
                    "silent": false,
                    "invisible": false,
                    "ignore": false,
                    "cursor": "pointer",
                    "draggable": false,
                    "progressive": false,
                    "width": 0,
                    "height": 0,
                    "style": {
                        "text": "pyecharts bar chart",
                        "x": 0,
                        "y": 0,
                        "font": "bold 26px Microsoft YaHei",
                        "textAlign": "left",
                        "fill": "#fff",
                        "line_width": 0
                    }
                }
            ],
            "$action": "merge",
            "rotation": Math.PI / 4,
            "right": 110,
            "bottom": 110,
            "bounding": "raw",
            "z": 100,
            "zlevel": 0,
            "silent": false,
            "invisible": false,
            "ignore": false,
            "cursor": "pointer",
            "draggable": false,
            "progressive": false,
            "width": 0,
            "height": 0
        }
    ]
};
        chart_1d9f6b96e3d14624aba03277e8bea6a0.setOption(option_1d9f6b96e3d14624aba03277e8bea6a0);
    </script>
<br/>                <div id="91b3c7009a7d44b88a36dcd3671a9d55" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_91b3c7009a7d44b88a36dcd3671a9d55 = echarts.init(
            document.getElementById('91b3c7009a7d44b88a36dcd3671a9d55'), 'white', {renderer: 'canvas'});
        var option_91b3c7009a7d44b88a36dcd3671a9d55 = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                75,
                103,
                134,
                149,
                41,
                53,
                24
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                58,
                116,
                149,
                125,
                150,
                129,
                82
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u6cb3\u9a6c",
                "\u87d2\u86c7",
                "\u8001\u864e",
                "\u5927\u8c61",
                "\u5154\u5b50",
                "\u718a\u732b",
                "\u72ee\u5b50"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-Brush\u793a\u4f8b",
            "subtext": "\u6211\u662f\u526f\u6807\u9898",
            "padding": 5,
            "itemGap": 10
        }
    ],
    "brush": {
        "toolbox": [
            "rect",
            "polygon",
            "keep",
            "clear"
        ],
        "brushType": "rect",
        "brushMode": "single",
        "transformable": true,
        "brushStyle": {
            "borderWidth": 1,
            "color": "rgba(120,140,180,0.3)",
            "borderColor": "rgba(120,140,180,0.8)"
        },
        "throttleType": "fixRate",
        "throttleDelay": 0,
        "removeOnClick": true
    }
};
        chart_91b3c7009a7d44b88a36dcd3671a9d55.setOption(option_91b3c7009a7d44b88a36dcd3671a9d55);
    </script>
<br/>                <div id="8f8620a714d04bbcae4b5e59db2be38b" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_8f8620a714d04bbcae4b5e59db2be38b = echarts.init(
            document.getElementById('8f8620a714d04bbcae4b5e59db2be38b'), 'white', {renderer: 'canvas'});
        var option_8f8620a714d04bbcae4b5e59db2be38b = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "color": [
        "#c23531",
        "#2f4554",
        "#61a0a8",
        "#d48265",
        "#749f83",
        "#ca8622",
        "#bda29a",
        "#6e7074",
        "#546570",
        "#c4ccd3",
        "#f05b72",
        "#ef5b9c",
        "#f47920",
        "#905a3d",
        "#fab27b",
        "#2a5caa",
        "#444693",
        "#726930",
        "#b2d235",
        "#6d8346",
        "#ac6767",
        "#1d953f",
        "#6950a1",
        "#918597"
    ],
    "series": [
        {
            "type": "bar",
            "name": "\u5546\u5bb6A",
            "data": [
                80,
                47,
                84,
                84,
                140,
                150,
                45
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "itemStyle": {
                "color":         function (params) {            if (params.value > 0 && params.value < 50) {                return 'red';            } else if (params.value > 50 && params.value < 100) {                return 'blue';            }            return 'green';        }        
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6B",
            "data": [
                144,
                73,
                59,
                80,
                36,
                27,
                79
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "itemStyle": {
                "color":         function (params) {            if (params.value > 0 && params.value < 50) {                return 'red';            } else if (params.value > 50 && params.value < 100) {                return 'blue';            }            return 'green';        }        
            }
        },
        {
            "type": "bar",
            "name": "\u5546\u5bb6C",
            "data": [
                54,
                123,
                119,
                84,
                146,
                59,
                67
            ],
            "barCategoryGap": "20%",
            "label": {
                "show": true,
                "position": "top",
                "margin": 8
            },
            "itemStyle": {
                "color":         function (params) {            if (params.value > 0 && params.value < 50) {                return 'red';            } else if (params.value > 50 && params.value < 100) {                return 'blue';            }            return 'green';        }        
            }
        }
    ],
    "legend": [
        {
            "data": [
                "\u5546\u5bb6A",
                "\u5546\u5bb6B",
                "\u5546\u5bb6C"
            ],
            "selected": {
                "\u5546\u5bb6A": true,
                "\u5546\u5bb6B": true,
                "\u5546\u5bb6C": true
            },
            "show": true,
            "padding": 5,
            "itemGap": 10,
            "itemWidth": 25,
            "itemHeight": 14
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                "\u54c8\u58eb\u5947",
                "\u8428\u6469\u8036",
                "\u6cf0\u8fea",
                "\u91d1\u6bdb",
                "\u7267\u7f8a\u72ac",
                "\u5409\u5a03\u5a03",
                "\u67ef\u57fa"
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ],
    "title": [
        {
            "text": "Bar-\u81ea\u5b9a\u4e49\u67f1\u72b6\u989c\u8272",
            "padding": 5,
            "itemGap": 10
        }
    ]
};
        chart_8f8620a714d04bbcae4b5e59db2be38b.setOption(option_8f8620a714d04bbcae4b5e59db2be38b);
    </script>
<br/>                <div id="146a84a560634b2eac9d696c893f42aa" class="chart-container" style="width:900px; height:500px;"></div>
    <script>
        var chart_146a84a560634b2eac9d696c893f42aa = echarts.init(
            document.getElementById('146a84a560634b2eac9d696c893f42aa'), 'light', {renderer: 'canvas'});
        var option_146a84a560634b2eac9d696c893f42aa = {
    "animation": true,
    "animationThreshold": 2000,
    "animationDuration": 1000,
    "animationEasing": "cubicOut",
    "animationDelay": 0,
    "animationDurationUpdate": 300,
    "animationEasingUpdate": "cubicOut",
    "animationDelayUpdate": 0,
    "series": [
        {
            "type": "bar",
            "name": "product1",
            "data": [
                {
                    "value": 12,
                    "percent": 0.8
                },
                {
                    "value": 23,
                    "percent": 0.5227272727272727
                },
                {
                    "value": 33,
                    "percent": 0.868421052631579
                },
                {
                    "value": 3,
                    "percent": 0.05454545454545454
                },
                {
                    "value": 33,
                    "percent": 0.4342105263157895
                }
            ],
            "stack": "stack1",
            "barCategoryGap": "50%",
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "formatter": function(x){return Number(x.data.percent * 100).toFixed() + '%';}
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        },
        {
            "type": "bar",
            "name": "product2",
            "data": [
                {
                    "value": 3,
                    "percent": 0.2
                },
                {
                    "value": 21,
                    "percent": 0.4772727272727273
                },
                {
                    "value": 5,
                    "percent": 0.13157894736842105
                },
                {
                    "value": 52,
                    "percent": 0.9454545454545454
                },
                {
                    "value": 43,
                    "percent": 0.5657894736842105
                }
            ],
            "stack": "stack1",
            "barCategoryGap": "50%",
            "label": {
                "show": true,
                "position": "right",
                "margin": 8,
                "formatter": function(x){return Number(x.data.percent * 100).toFixed() + '%';}
            },
            "rippleEffect": {
                "show": true,
                "brushType": "stroke",
                "scale": 2.5,
                "period": 4
            }
        }
    ],
    "legend": [
        {
            "data": [
                "product1",
                "product2"
            ],
            "selected": {
                "product1": true,
                "product2": true
            }
        }
    ],
    "tooltip": {
        "show": true,
        "trigger": "item",
        "triggerOn": "mousemove|click",
        "axisPointer": {
            "type": "line"
        },
        "textStyle": {
            "fontSize": 14
        },
        "borderWidth": 0
    },
    "xAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            },
            "data": [
                1,
                2,
                3,
                4,
                5
            ]
        }
    ],
    "yAxis": [
        {
            "show": true,
            "scale": false,
            "nameLocation": "end",
            "nameGap": 15,
            "gridIndex": 0,
            "inverse": false,
            "offset": 0,
            "splitNumber": 5,
            "minInterval": 0,
            "splitLine": {
                "show": false,
                "lineStyle": {
                    "width": 1,
                    "opacity": 1,
                    "curveness": 0,
                    "type": "solid"
                }
            }
        }
    ]
};
        chart_146a84a560634b2eac9d696c893f42aa.setOption(option_146a84a560634b2eac9d696c893f42aa);
    </script>
<br/>    </div>
    <script>
    </script>
</body>
</html>
