# Last Update:2022-09-01 14:23:10
##
# @file alarm_static.py
# @brief : 告警统计
# <AUTHOR>
# @version 0.1.00
# @date 2020-01-18
import pymysql 
import os
import json
import time
from crccheck.crc import Crc64, CrcXmodem

alarm_info={}
alarm_static_file = "/opt/GeekSec/STL/.crontab/alarm_static_file.json"
#读取文件
os.system( "mkdir -p /opt/GeekSec/STL/.crontab/")
if os.path.exists(alarm_static_file):
    try:
        alarm_info = json.load(open(alarm_static_file,"r"))
        if 'id' not in alarm_info:
            alarm_info["id"] = 0
        if 'ruleid' not in  alarm_info :
            alarm_info["ruleid"] = 0
    except ValueError:
        alarm_info["id"] = 0
        alarm_info["ruleid"] = 0
        alarm_info["whiteid"] = 0
else :
    alarm_info["id"] = 0
    alarm_info["ruleid"] = 0
    alarm_info["whiteid"] = 0
from loadMysqlPasswd import mysql_passwd
passwd = mysql_passwd()
#
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
insert_map = {}
update_map = {}

tag_family = {}
def rule_statis_rule_inuo():
    sql = "select max(id) as maxid  from tb_rule_statistic_info" ;
    maxid = 0 
    maxid  = s_mysql(sql,cursor)[0]['maxid']
    if maxid  is None:
        maxid = 0
    sql  = " select rule_id ,sum(sum_bytes)   as bytes  , max(time) as last_time  from  tb_rule_statistic_info  where  id >  " + str(alarm_info["ruleid"]) +" and id  <= " +str(maxid) +" group by rule_id order by sum(sum_bytes) desc "
    relset = s_mysql(sql,cursor)
    num = 0
    for row in relset:
        rule_id  = row['rule_id']
        if rule_id >= 35000 and rule_id < 150001 :
            u_sql = "update tb_rule set total_sum_bytes =  total_sum_bytes + "+str(row['bytes']) +" , last_size_time = "+str(row['last_time'])+"  where  rule_id = "+str(rule_id);
            num  = num + 1
            idu_mysql(u_sql , cursor , db)

        elif (rule_id > 30500 and  rule_id < 32000) or (rule_id > 169001 and  rule_id < 170000): 
            u_sql = "update tb_model set total_sum_bytes =  total_sum_bytes + "+str(row['bytes']) +" , last_size_time = "+str(row['last_time'])+"  where  model_id  = "+str(rule_id);
            num  = num + 1
            idu_mysql(u_sql , cursor , db)
        elif  rule_id > 150000 and  rule_id < 169000:
            u_sql = "update tb_forensics_white_list set total_sum_bytes =  total_sum_bytes + "+str(row['bytes']) +" , last_size_time = "+str(row['last_time'])+"  where  rule_id  = "+str(rule_id);
            num  = num + 1
            idu_mysql(u_sql , cursor , db)
        if num >3000 :
            db.commit()
    db.commit()


    alarm_info["ruleid"]= maxid
def getKey(tag_id  , ttime):
    key  = Crc64.calc(bytes(str(tag_id)+ str(ttime), encoding='utf-8'))
    return key
#getNnTagNum()
rule_statis_rule_inuo()
print(alarm_info)
json.dump(alarm_info,open(alarm_static_file,"w"))

# rule  
