# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/9/23 14:13
# 文件名称 : start_analysis.py
# 开发工具 : PyCharm
import sys,os,json,pymysql.cursors,time
from elasticsearch import Elasticsearch
# 写文件函数
def write_file(filename , context):
    os.system("echo \"\">"+filename)
    fnew = open(filename, "w")
    fnew.write(context)
    fnew.close()
# mysql查询
def s_mysql(sql,cursor):
    cursor.execute(sql)
    return cursor.fetchall()
# mysql增删改
def idu_mysql(sql,cursor):
    cursor.execute(sql)
    db.commit()
# 获取分析目标
def get_targets():
    res = s_mysql("select * from tb_analysis_report where type = 0 and state = 1", cursor)
    return res
def get_targer_tags(str0,str1,str2):
    tags = s_mysql(str2, cursor)
    if len(tags) > 0:
        if str0 != 0:
            str0["tags"] = tags[0]["tags"]
        str1["tags"] = tags[0]["tags"]
    else:
        if str0 != 0:
            str0["tags"] = ""
        str1["tags"] = ""
def get_targer_list(str0,str1,str2):
    lists = s_mysql(str2, cursor)
    if len(lists) > 0:
        if str0 != 0:
            str0["black_list"] = lists[0]["black_list"]
            str0["white_list"] = lists[0]["white_list"]
        str1["black_list"] = lists[0]["black_list"]
        str1["white_list"] = lists[0]["white_list"]
    else:
        if str0 != 0:
            str0["black_list"] = 0
            str0["white_list"] = 0
        str1["black_list"] = 0
        str1["white_list"] = 0
def get_targer_remarks(str0,str1,str2):
    remarks = s_mysql(str2, cursor)
    if len(remarks) > 0:
        if str0 != 0:
            str0["remarks"] = remarks[0]["remarks"]
        str1["remarks"] = remarks[0]["remarks"]
    else:
        if str0 != 0:
            str0["remarks"] = ""
        str1["remarks"] = ""
def get_ip_server_port(str0,str1):
    port = s_mysql("select count(DISTINCT port) os_num from IP_Server_Port where IP = '" + str0 + "'", cursor)
    if len(port) > 0:
        str1["open_service_num"] = port[0]["os_num"]
    else:
        str1["open_service_num"] = 0
def get_ip_client_port(str0,str1):
    port = s_mysql("select count(DISTINCT port) as_num from IP_Client_Port where srcip = = '" + str0 + "'", cursor)
    if len(port) > 0:
        str1["access_service_num"] = port[0]["as_num"]
    else:
        str1["access_service_num"] = 0
# ip_report(IP报告)
def ip_report(obj,obj_json):
    print("ip_report")
    ip_arr = []
    if obj["target_type"] == 1:
        b_ip = obj["target_name"].split("-")[0]
        e_ip = obj["target_name"].split("-")[1]
        res = s_mysql(
            "select * from IP_INFO where inet_aton(IP) between inet_aton('" + b_ip + "') and inet_aton('" + e_ip + "') and begin_time between " + begintime + " and " + endtime + " or end_time between " + begintime + " and " + endtime,
            cursor)
        if len(res):
            for row in res:
                get_targer_list(0, row,
                                "select blackList as black_list,whiteList as white_list from IP_LIST where IP = '" +
                                obj["target_name"] + "'")
                get_targer_tags(0, row,
                                "select group_concat( Tag_Text ) as tags from TAG_INFO where Tag_Id IN ( select tagId from ip_tag where ip = '" +
                                obj["target_name"] + "')")
                ip_arr.append(row)
            obj["black_list"] = 101
            obj["white_list"] = 101
            obj["tags"] = ""
    else:
        res = s_mysql("select * from IP_INFO where IP = '" + obj[
            "target_name"] + "' and begin_time between " + begintime + " and " + endtime + " or end_time between " + begintime + " and " + endtime,
                      cursor)
        if len(res) > 0:
            get_targer_list(obj, res[0],
                            "select blackList as black_list,whiteList as white_list from IP_LIST where IP = '" + obj[
                                "target_name"] + "'")
            get_targer_tags(obj, res[0],
                            "select group_concat( Tag_Text ) as tags from TAG_INFO where Tag_Id IN ( select tagId from ip_tag where ip = '" +
                            obj["target_name"] + "')")
            ip_arr.append(res[0])
    if len(ip_arr) > 0:
        #1 basic_info(基础信息)
        basic_info = []
        #2 service_info(服务信息) --> [open_service(开放服务),access_service(访问服务)]
        service_info = []
        #3 ip_comm(通信IP) --> [mutual_server_comm(互为服务器通信),no_relate_domain_comm(未关联域名通信),relate_domain_comm(关联域名通信)]
        ip_comm = []
        #4 client_dns
        client_dns = []
        #5 passive_dns
        passive_dns = []
        #6 passive_cert
        passive_cert = []
        #7 client_cert
        client_cert = []
        #8 alarm_info --> [alarm_events(告警事件),suspect_conn(可疑连接)]
        alarm_info = []
        for ipx in ip_arr:
            #1
            ip1_json = {}
            ip1_json["ip"] = ipx["IP"]
            geographical_position = ""
            if ipx["country"] != "Unknown" and ipx["country"] != "" and not ipx["country"] is None:
                geographical_position = geographical_position + ipx["country"]
            if ipx["subdivisions"] != "Unknown" and ipx["subdivisions"] != "" and not ipx["subdivisions"] is None:
                geographical_position = geographical_position + ipx["subdivisions"]
            if ipx["city"] != "Unknown" and ipx["city"] != "" and not ipx["city"] is None:
                geographical_position = geographical_position + ipx["city"]
            ip1_json["geographical_position"] = geographical_position
            ip1_json["begin_time"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(ipx["begin_time"]))
            ip1_json["end_time"] = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(ipx["end_time"]))
            ip1_json["black_list"] = ipx["black_list"]
            ip1_json["white_list"] = ipx["white_list"]
            ip1_json["tags"] = ipx["tags"]
            get_targer_remarks(0, ip1_json,
                               "select remark as remarks from ip_remark where ip = '" + ipx["IP"] + "'")
            get_ip_server_port(ipx["IP"], ip1_json)
            get_ip_client_port(ipx["IP"], ip1_json)
            basic_info.append(ip1_json)
            #2
            ip2_json = {}
            ip2_json["ip"] = ipx["IP"]
            ip2_json["open_service"] = s_mysql("select ip_pro,port,app as app_id,count as conn_num,srcip as s_ip,toBytes as to_bytes,fromBytes as from_bytes from IP_Client_Port WHERE IP = '" + ipx["IP"] + "'",cursor)
            ip2_json["access_service"] = s_mysql("select ip_pro,port,app as app_id,count as conn_num,IP as d_ip,toBytes as to_bytes,fromBytes as from_bytes from IP_Client_Port WHERE srcip = '" + ipx["IP"] + "'",cursor)
            service_info.append(ip2_json)
            #3
            ip3_json = {}
            ip3_json["ip"] = ipx["IP"]
            ip3_json["mutual_server_comm"] = []
            ip3_json["no_relate_domain_comm"] = []
            ip3_json["relate_domain_comm"] = []
            ip_comm.append(ip3_json)
            #4
            ip4_json = {}
            ip4_json["ip"] = ipx["IP"]
            client_dns.append(ip4_json)
            #5
            ip5_json = {}
            ip5_json["ip"] = ipx["IP"]
            passive_dns.append(ip5_json)
            #6
            ip6_json = {}
            ip6_json["ip"] = ipx["IP"]
            passive_cert.append(ip6_json)
            #7
            ip7_json = {}
            ip7_json["ip"] = ipx["IP"]
            client_cert.append(ip7_json)
            #8
            ip8_json = {}
            ip8_json["ip"] = ipx["IP"]
            ip8_json["alarm_events"] = []
            ip8_json["suspect_conn"] = []
            alarm_info.append(ip8_json)
        #1
        obj_json["basic_info"] = basic_info
        #2
        obj_json["service_info"] = service_info
        #3
        obj_json["ip_comm"] = ip_comm
        #4
        obj_json["client_dns"] = client_dns
        #5
        obj_json["passive_dns"] = passive_dns
        #6
        obj_json["passive_cert"] = passive_cert
        #7
        obj_json["client_cert"] = client_cert
        #8
        obj_json["alarm_info"] = alarm_info
        create_report(obj)
# domain_report(域名报告)
def domain_report(obj,obj_json):
    print("domain_report")
# certsha1_report(文件hash报告)
def certsha1_report(obj,obj_json):
    print("certsha1_report")
# 创建报告
def create_report(obj):
    isExists_task_report = os.path.exists()
    if not isExists_task_report:
        os.makedirs(dir_path)
    file_path = dir_path + "start_analysis_" + obj["target_type_name"] + "_" + obj["target_name"] + "_" + time.strftime(
        "%Y-%m-%d %H:%M:%S", time.localtime(begintime)) + "-" + time.strftime("%Y-%m-%d %H:%M:%S",
                                                                              time.localtime(endtime)) + ".json"
    write_file(file_path, json.dumps(report_json, ensure_ascii=False))
    idu_mysql(
        "INSERT INTO tb_analysis_report_result(report_target_name, report_target_type, task_id, tags, report_type, report_time, report_path) VALUES ('" +
        obj["target_name"] + "', " + str(obj["target_type"]) + ", " + str(obj["task_id"]) + ", '" + str(
            obj["tags"]) + "', " + str(obj["black_list"]) + ", 0, UNIX_TIMESTAMP(NOW()), '" + dir_path + "');", cursor)
    idu_mysql("UPDATE tb_analysis_report SET state = 0 WHERE id = " + str(obj["id"]), cursor)
    print("创建报告成功!")
    report_json.clear()
# begin
report_json = {}
# ES地址
es = Elasticsearch(["127.0.0.1:19200"])
# 报告生成路径
dir_path = "/var/ftp/analysis_report/"
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
begintime = int(sys.argv[1])
endtime = int(sys.argv[1])
analysis_targets = get_targets()
if len(analysis_targets) > 0:
    for row in analysis_targets:
        if row["target_type"] == 0:
            row["target_type_name"] = "单IP"
            report_json["ip_report"] = {}
            ip_report(row,report_json["ip_report"])
        if row["target_type"] == 1:
            row["target_type_name"] = "IP范围"
            report_json["ip_report"] = {}
            ip_report(row, report_json["ip_report"])
        if row["target_type"] == 2:
            row["target_type_name"] = "域名"
            report_json["domain_report"] = {}
            domain_report(row,report_json["domain_report"])
        if row["target_type"] == 3:
            row["target_type_name"] = "文件hash"
            report_json["certsha1_report"] = {}
            certsha1_report(row,report_json["certsha1_report"])
