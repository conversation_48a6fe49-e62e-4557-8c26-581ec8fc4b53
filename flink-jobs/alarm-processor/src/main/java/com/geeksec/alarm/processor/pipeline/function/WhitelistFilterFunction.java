package com.geeksec.alarm.processor.pipeline.function;

import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.whitelist.AlarmWhitelistClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 告警白名单过滤函数
 * 用于过滤掉在白名单中的告警
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
public class WhitelistFilterFunction implements FilterFunction<Alarm> {

    private final AlarmWhitelistClient whitelistClient;

    // 监控指标
    private transient Counter totalAlarms;
    private transient Counter filteredAlarms;
    private transient Counter whitelistHits;
    private transient Counter whitelistMisses;

    public WhitelistFilterFunction(AlarmProcessorConfig config) {
        // 从配置创建白名单客户端
        this.whitelistClient = new AlarmWhitelistClient(config);
    }

    /**
     * 初始化监控指标
     */
    public void initMetrics(MetricGroup metricGroup) {
        this.totalAlarms = metricGroup.counter("total_alarms");
        this.filteredAlarms = metricGroup.counter("filtered_alarms");
        this.whitelistHits = metricGroup.counter("whitelist_hits");
        this.whitelistMisses = metricGroup.counter("whitelist_misses");
    }

    @Override
    public boolean filter(Alarm alarm) throws Exception {
        totalAlarms.inc();

        // 如果白名单客户端未初始化，直接通过
        if (whitelistClient == null) {
            log.warn("白名单客户端未初始化，告警直接通过: alarmId={}", alarm.getAlarmId());
            return true;
        }

        try {
            // 检查告警是否在白名单中
            boolean inWhitelist = whitelistClient.isInWhitelist(alarm);

            if (inWhitelist) {
                whitelistHits.inc();
                filteredAlarms.inc();
                log.debug("告警在白名单中，已过滤: victim={}, attacker={}, alarmType={}",
                        alarm.getDstIp(), alarm.getSrcIp(), alarm.getAlarmType());
                return false; // 过滤掉
            } else {
                whitelistMisses.inc();
                log.debug("告警不在白名单中，继续处理: victim={}, attacker={}, alarmType={}",
                        alarm.getDstIp(), alarm.getSrcIp(), alarm.getAlarmType());
                return true; // 继续处理
            }

        } catch (Exception e) {
            log.error("白名单检查失败，告警直接通过: victim={}, attacker={}, alarmType={}",
                    alarm.getDstIp(), alarm.getSrcIp(), alarm.getAlarmType(), e);
            return true; // 出错时直接通过，避免丢失告警
        }
    }
}