{{- if .Values.flinkJobs.alarmCdcSync.enabled -}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: flink-alarm-cdc-job
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: flink-job
    flink-job: alarm-cdc-sync
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: flink-alarm-cdc-job
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: flink-alarm-cdc-job
        app.kubernetes.io/instance: {{ .Release.Name }}
        flink-job: alarm-cdc-sync
    spec:
      containers:
      - name: flink-alarm-cdc-job
        image: {{ .Values.global.registry }}/nta/flink-alarm-cdc-sync:{{ .Values.global.tag }}
        imagePullPolicy: {{ .Values.global.imagePullPolicy }}
        env:
        - name: FLINK_PROPERTIES
          value: |
            jobmanager.rpc.address: flink-jobmanager
            jobmanager.rpc.port: 6123
            parallelism.default: {{ .Values.flinkJobs.alarmCdcSync.parallelism | default 2 }}
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: password
        - name: DORIS_USERNAME
          valueFrom:
            secretKeyRef:
              name: doris-credentials
              key: username
        - name: DORIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: doris-credentials
              key: password
        volumeMounts:
        - name: flink-config
          mountPath: /opt/flink/conf
          readOnly: true
        - name: job-artifacts
          mountPath: /opt/flink/usrlib
          readOnly: true
        resources:
          {{- toYaml .Values.flinkJobs.alarmCdcSync.resources | nindent 10 }}
        livenessProbe:
          httpGet:
            path: /
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: flink-config
        configMap:
          name: flink-alarm-cdc-config
      - name: job-artifacts
        emptyDir: {}
      restartPolicy: Always
      {{- with .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.global.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end -}}
