##
# @file png_test.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-02-14

import json
import time 
import sys
import re
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.pagesizes import letter
import matplotlib as mpl
mpl.use('Agg')
import matplotlib.pyplot as plt
import numpy as np


from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak, Table, TableStyle
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.units import mm
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics


# 注册字体
pdfmetrics.registerFont(TTFont('msyh', '/opt/GeekSec/web/rule_syn/Fonts/msyh.ttc'))
pdfmetrics.registerFont(TTFont('msyhbd', '/opt/GeekSec/web/rule_syn/Fonts/msyhbd.ttc'))
pdfmetrics.registerFont(TTFont('msyhl', '/opt/GeekSec/web/rule_syn/Fonts/msyhl.ttc'))

file_name = "test.pdf"
# 两个值的柱状图
def show_two_value_bar(name, y1 ,y2 ,label,png_file_name):
    x = np.arange(len(name))
    width = 0.25
    label_len = len(label)
    plt.bar(x, y1,  width=width, label=label[0],color='darkorange')
    if (label_len == 2):
        plt.bar(x + width, y2, width=width, label=label[1], color='deepskyblue', tick_label=name)

    # 显示在图形上的值
    for a, b in zip(x,y1):
        plt.text(a, b, b, ha='center', va='bottom')
    if (label_len == 2):
        for a,b in zip(x,y2):
            plt.text(a+width, b, b, ha='center', va='bottom')

    plt.xticks()
    plt.legend(loc="upper left")  # 防止label和图像重合显示不出来
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.ylabel('value')
    plt.xlabel('line')
    plt.rcParams['savefig.dpi'] = 300  # 图片像素
    plt.rcParams['figure.dpi'] = 300  # 分辨率
    plt.rcParams['figure.figsize'] = (15.0, 8.0)  # 尺寸
    plt.title("title")
    plt.savefig(png_file_name)
    plt.show()
class Graphs:
    def __init__(self):
        pass
    # 绘制标题
    @staticmethod
    def draw_title(name_title, fontName, fontSize, leading, alignment):
        style = getSampleStyleSheet()
        ct = style['Normal']
        ct.fontName = fontName
        ct.fontSize = fontSize
        #  设置行距
        ct.leading = leading
        #  颜色
        ct.textColor = colors.black
        #  居中
        ct.alignment = alignment
        #  添加标题并居中
        title = Paragraph(name_title, ct)
        return title
def to_pdf():
    content = list()
    # %添加标题
     # 二 任务归属
    content.append(Graphs.draw_title("二 任务归属", 'msyh', 14, 30, 0))
    content.append(Graphs.draw_title(" 1 回话统计", 'msyh', 12, 30, 0))
    # 三  关联分析
    content.append(Graphs.draw_title("三 关联分析", 'msyh', 14, 30, 0))
    # 1 联域名
    content.append(Graphs.draw_title(" 1 关联域名", 'msyh', 12, 30, 0))
    # 2 兄弟域名
    content.append(Graphs.draw_title(" 2 兄弟域名", 'msyh', 12, 30, 0))
    #content.append(Table(domain_corr_domain, colWidths=50,
    #        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
    #            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
    #            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    # 三  回话分析
    content.append(Graphs.draw_title("三 回话分析", 'msyh', 14, 30, 0))
    # 1 客户端
    content.append(Graphs.draw_title(" 1 客户端", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("  ii 时序分布", 'msyh', 12, 30, 0))
    #添加表格样式
    #img = Image.open("./eclick.baidu.comdns_histogram.png")
    content.append(Spacer(1, 20 * mm))
    img = Image('eclick.baidu.com_http_histogram.png')
    img.drawHeight = 120 * mm
    img.drawWidth = 140 * mm
    img.hAlign = TA_LEFT
    content.append(img)
    content.append(Spacer(1, 10 * mm))




    doc = SimpleDocTemplate(file_name, pagesize=letter)
    doc.build(content)
to_pdf()
