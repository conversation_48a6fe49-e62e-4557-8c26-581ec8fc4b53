# Last Update:2020-02-27 16:21:58
##
# @file test.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-02-27

import os,sys
from analisys_common_tools import drew_heatmap_png,drew_countplot_png,drew_barplot_png

data=[]
for i in range(4):
    data_t = []
    for  l in range(5):
        data_t.append(i*10+l)
    data.append(data_t)
xLabel=["1902","1903","1904","1905"]
yLabel=["test1","test2","test3","test4","test5"]
title_name = "test"
png_file_name = "wangxiang.png"
#drew_heatmap_png(data,xLabel ,yLabel ,title_name ,png_file_name)

drew_barplot_png(data,xLabel,"test1",yLabel,"test2","test",png_file_name)

 
