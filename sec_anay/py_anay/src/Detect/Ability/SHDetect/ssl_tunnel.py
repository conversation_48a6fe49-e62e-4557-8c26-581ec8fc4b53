import sys
sys.path.append("./")

import PyGksec.GkHelper.AlarmHelper as alarm_helper
import PyGksec.GkHelper.ESHelper as eh
from Detect.Ability.SHDetect.alarm_comon import TAGTEXT_ALARMTEXT_MAP,TAGTEXT_TAGID_MAP, TAGID_TAGTEXT_MAP

from utils.redis_utils.redis_helper import redis_not_alarm
from utils.mysql_utils.mysql_helper import mysql_not_white

tag_names = [i for i in TAGTEXT_ALARMTEXT_MAP if TAGTEXT_ALARMTEXT_MAP[i]=="加密通道攻击行为"]
tag_session_ids = []
for i in tag_names:
    tag_session_ids.append(TAGTEXT_TAGID_MAP[i])

def expend_aip_info(task_id,batch_id,aip):
    table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="dns")
    detail["query"]["bool"]["must"].append({"term": {"DomainIp":aip}})    
    domain_aggs = eh.make_term_agg(query_key="Domain.keyword",result_key="domain_agg",size=20)
    detail["aggs"] = domain_aggs
    data =  eh.load_agg_data(table,detail,data_key="domain_agg")
    if data:
        return set(d["key"] for d in data)
    return set()

def get_df_reasons(df):
    label_ip_map = df[["dIp", "Labels"]].set_index("dIp").to_dict(orient='dict')["Labels"]
    labels_list = df["Labels"].tolist()
    label_all,label_id_all = [],[]
    for labels in labels_list:
        for label in labels:
            if label in tag_session_ids:
                label_all.append(TAGID_TAGTEXT_MAP[label])
                label_id_all.append(label)
    label_all=list(set(label_all))
    label_id_all = list(set(label_id_all))
    dip_label_dic = {}
    for dip in label_ip_map:
        for label in label_id_all:
            if label in label_ip_map[dip]:
                if dip in dip_label_dic:
                    if label not in dip_label_dic[dip]:
                        dip_label_dic[dip].append(TAGID_TAGTEXT_MAP[label])
                else:
                    labels = []
                    labels.append(TAGID_TAGTEXT_MAP[label])
                    dip_label_dic[dip] = labels
    reasons,reason=[],{}
    str_label = ""
    for dip in dip_label_dic:
        label_dip=dip_label_dic[dip]
        str_label+=f"攻击方:{dip},使用的加密通道攻击工具:{label_dip}\n        "
    reason["key"] = "发现的攻击方使用的加密通道攻击工具对应关系为"
    reason["actual_value"] = str_label
    reasons.append(reason)

    return reasons,label_id_all,dip_label_dic

def detect_ssl_tunnel_alarm(task_id,batch_id):
    table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="connectinfo")
    detail["query"]["bool"]["must"].append({"terms": {"Labels":tag_session_ids}})
    eh.describe(table,detail)
    df = eh.load_es_data_to_DF(table,detail,ignore_file=True)
    if df is None:
        return
    df.groupby(["sIp"],as_index=False).filter(lambda x : write_ssl_software_alarm(x,task_id,batch_id,alarm_name="加密通道攻击行为")) 

def get_alarm_info(alarm_data,df):
    attack_family = []
    targets = []# 
    alarm_data["attacker"] = [{"ip":aip} for aip in df.dIp.drop_duplicates()]#dip去重就是aip，目的IP
    for aip in alarm_data["attacker"]:
        targets.append({"name":aip["ip"],"type":"ip","labels":[]})
    return attack_family,targets

def write_ssl_software_alarm(df,task_id,batch_id,alarm_name,model_name="流量特征分析",alarm_type="模型"):
    alarm_data = alarm_helper.get_alarm_json_data(alarm_name)
    reason_list = []
    reasons,label_all,dip_label_dic = get_df_reasons(df)
    reason_list += reasons
    victim_list = []
    for vip in df.sIp.drop_duplicates():#sip去重就是vip，源IP
        alarm_data["victim"].append({"ip":vip})
        victim_list.append(vip)

    attack_family,targets = get_alarm_info(alarm_data,df)
    
    # 获取attack_chain_list
    dip_labelId_dic = {}
    for i in dip_label_dic:
        labels = dip_label_dic[i]
        labels = [TAGTEXT_TAGID_MAP[x] for x in labels]
        dip_labelId_dic[i] = labels
    attack_chain_list = []
    for victim in victim_list:
        for i in dip_labelId_dic:
            labels=dip_labelId_dic[i]
            for label in labels:
                attack_chain_list.append(f"{victim}_{i}_{label}")
    
    alarm_data["attack_chain_list"] = attack_chain_list
    session_id = []
    session_id_list = df["SessionId"].tolist()
    for i in session_id_list:
        session_id.append(i)
    session_id = list(set(session_id))
    alarm_data["alarm_session_list"] = session_id
    alarm_data["alarm_related_label"] = label_all
    if len(alarm_data["attacker"])>10:
        alarm_data["attacker"]=alarm_data["attacker"][0:10]
    alarm_data["attack_family"] = attack_family
    alarm_data["alarm_reason"] = reason_list
    alarm_data["targets"] = targets
    alarm_data["alarm_principle"] = '使用恶意软件进行恶意通讯，使用计算机或者移动设备内的资源进行恶意通信。'
    alarm_data["alarm_handle_method"] = "检测到当前网络存在恶意软件运行，受害者主机的CPU、GPU被复杂运算的恶意程序占用，或被攻击者用作跳板入侵内网\n 1. 确认告警：\n        -对告營进行分析，确认告營正确性 \n        -对日志进行分析。确认所有中招主机 \n        \n 2.现状确认： \n        -分析安全告警，告警最早发现时间 \n        \n 3.处置之际：\n        -在主机上清理恶意程序\n        "
    alarm_data["alarm_type"] = alarm_type
    if alarm_data["targets"] != [] and alarm_data["victim"]!=[] and alarm_data["attacker"]!=[] and redis_not_alarm(attack_chain_list) and mysql_not_white(attack_chain_list):
        alarm_data = alarm_helper.append_alarm_task_info(alarm_data,task_id,batch_id)
        # print(json.dumps(alarm_data,indent=4,ensure_ascii=False))
        alarm_helper.add_remote_alarm_json(alarm_data)

if __name__=="__main__":
    task_id, batch_id = eh.get_run_arg('connectinfo*')
    detect_ssl_tunnel_alarm(task_id, batch_id)