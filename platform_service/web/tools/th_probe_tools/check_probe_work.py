##
# @file check_network.py
# @brief:
# <AUTHOR>
# @version 0.1.00
# @date 2021-06-28

import sys,os,pymysql,json
import psutil,time
########## 
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
        base_json = json.load(load_f)
passwd = "root"
if os.path.exists("/opt/GeekSec/pubconfig/.mysql"):
    with open("/opt/GeekSec/pubconfig/.mysql"):
        passwd = f.read()
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["db_port"],user='root',password=passwd,db='push_database',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()

def cmd(cmdstr):
    print(cmdstr)
    arr = []
    p = os.popen(cmdstr)
    lines = p.readlines()
    for line in lines:
         arr.append(line.strip('\n'))
    print(arr)
    return arr
# 绑定解绑网卡
bind_net_list = []
def dev_bind_no(net_id , bind_or_n , sysdir):
    if bind_or_n == False :  ### 解绑网卡 
         cmd("/opt/GeekSec/th/bin/usertools/dpdk-devbind.py -b " +sysdir +" "+net_id)
    else:
        cmd("/opt/GeekSec/th/bin/usertools/dpdk-devbind.py -b  igb_uio "+net_id)
netBytes = {}
def readNetBytes(net_id , m):
    cmdstr = "/opt/GeekSec/th/bin/usertools/dpdk-devbind.py --status  | grep "+net_id  
    res = cmd(cmdstr)
    if len(res) > 0:
        netName = res[0].split(" ")[7].split("=")[1]
        cmdstr = "cat /proc/net/dev | grep "+netName
        res2=cmd(cmdstr)
        res2_list = res2[0].split()
        print(res2_list)
        nbytes = int(res2_list[1]) + int(res2_list[9])
        if netName not in netBytes:
            netBytes[netName] = nbytes
            return
        if nbytes  - netBytes[netName]  <  100:
            print("网卡: "+netName +"  网卡ID: "+net_id+" : 未接线或线路无流量" )
        else:
            print("网卡: "+netName +"  网卡ID: "+net_id+" 网络流量是:" + str(bytes/ms))
### 读取绑卡的列表
def CheckNetData():
    os.system("/etc/init.d/thdd stop")
    os.system("cd /opt/GeekSec/th/bin/ && ./dpdk_ready.sh")
    cmdstr = "/opt/GeekSec/th/bin/usertools/dpdk-devbind.py --status  | grep drv=igb_uio"
    res = cmd(cmdstr)
    print("*****************绑定网口及流量大小检测*******************")
    for i in res:
        bind_net_list.append(i)
    for i in bind_net_list:
        print(i)
        net_id = i.split(" ")[0]
        sysdir = i.split("=")[1]
        dev_bind_no(net_id , False , sysdir)
        #### 读取 网卡 流量
    #  读取网卡的流量时间
    print("等待网卡激活并接受数据")
    for i in bind_net_list:
        net_id = i.split(" ")[0]
        readNetBytes(net_id,120)
    time.sleep(120)
    for i in bind_net_list:
        net_id = i.split(" ")[0]
        readNetBytes(net_id,120)
    print("*************************************************************")
    ### 检测完成 DPDK 流量绑定
    for i in bind_net_list:
        net_id = i.split(" ")[0]
        dev_bind_no(net_id , True , "")
####  PB 生成文件检测， 检测单天的生成
def checkPbPreo():
    os.system("/etc/init.d/thdd restart")
    ######
    time.sleep(600)
    ### 检测当前时间生成的PB文件 ##### 
    pbName = int(int(time.time())/60) - 10
    cmds = "find  /data/pbfiles/ -name "
    for i in range(0,20):
        cmdstr = cmds + str(pbName)+"*.pb"
        cmdstr1 = cmds + str(pbName +1 )+"*.pb"
        cmdstr2 = cmds + str(pbName + 2 )+"*.pb"
        cmdstr3 = cmds + str(pbName + 3 )+"*.pb"
        cmdstr4 = cmds + str(pbName + 4 )+"*.pb"
        resy = []
        res = cmd(cmdstr)
        res1 = cmd(cmdstr1)
        res2 = cmd(cmdstr2)
        res3 = cmd(cmdstr3)
        res4 = cmd(cmdstr4)
        if res != None:
            resy.append(res)
        if res1 != None:
            resy.append(res1)
        if res2 != None:
            resy.append(res2)
        if res3 != None:
            resy.append(res3)
        if res4 != None:
            resy.append(res4)
        
        for r in resy :
            if len(r) > 0 :
                print("生成PB正常")
                return 
        time.sleep(10)
    print("pb未生成 , 请检查探针的运行状态")

### 检测mysql 的数据
def checkMysqlPbCrea():
    ### 检测推送数据 mysql ####
    sql = "select pb_ps from tb_pb_save where  unix_timestamp(now()) < times + 120 and pb_ps > 0 limit 1 "
    res = s_mysql(sql,cursor)
    for row in res :
        if row["pb_ps"] > 0:
            print("pb 数据库态势正常 ")
            return
    print(" 数据库PB态势异常")
def checkMysqlNetCrea():
    pass
#### 
if __name__=='__main__':
    print("#### 开始检测探针工作#########")
    #CheckNetData()
    print("********** 网卡流量检测 *********************")
    os.system("/opt/GeekSec/th/bin/test_if_traffic.sh")
    print("********** 网卡驱动检测 *********************")
    os.system("/opt/GeekSec/th/bin/test_if_drv.sh")
    print("********** PB 生成检测 *********************")
    checkPbPreo()
    print("********** MYSQL  推送检测*********************")
    checkMysqlPbCrea()
    checkMysqlNetCrea()
