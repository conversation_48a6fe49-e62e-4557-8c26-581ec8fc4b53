# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/6/24 23:42
# 文件名称 : ip_passivedns.py
# 开发工具 : PyCharm
import json,pymysql.cursors,sys,re,hashlib,socket
# 将ASC转String
def ascToStr(str):
    strObj = ''
    strArr = str.split(",")
    for row in strArr:
        strObj = strObj + chr(int(row))
    return strObj
# 将String计算Hash
def strToHash(str):
    hhaasshh = hashlib.sha1()
    hhaasshh.update(str.encode("utf8"))
    strHash = hhaasshh.hexdigest()
    return strHash
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def domain_ip(ip,p):
    if ip != '' and not ip is None:
        if isIP(ip) == False:
            res_d_ip = s_mysql("select DANAME,ip,time,begin_time,end_time,num,DANAME1,type,(select value from tb_valset where val_id = type and valset_id = 'DNSType') as typeName,maxTTL,minTTL from PassiveDNS where DANAME = '" + ip + "';", tidb_cursor)
            if len(res_d_ip) > 0:
                for row in res_d_ip:
                    p.append(row)
                    domain_ip(row['ip'], p)
def isIP(str):
    #fl = re.compile('^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$')
    #if fl.match(str):
    if is_ipv4(str) or is_ipv6(str):
        return True
    else:
        return False

def is_ipv4(str):
    try:
        socket.inet_pton(socket.AF_INET, str)
    except AttributeError:  # no inet_pton here, sorry
        try:
            socket.inet_aton(str)
        except socket.error:
            return False
        return str.count('.') == 3
    except socket.error:  # not a valid ip
        return False
    return True

def is_ipv6(str):
    try:
        socket.inet_pton(socket.AF_INET6, str)
    except socket.error:  # not a valid ip
        return False
    return True

base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
# 获取参数
param = sys.argv[1]
paramStr = ascToStr(param)
paramHash = strToHash(paramStr)
paramValue = json.loads(paramStr)["domainName"]
tidb = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='statistical_information_base',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
passive = []
result_passive = s_mysql("select DANAME,ip,time,begin_time,end_time,num,DANAME1,type,(select value from tb_valset where val_id = type and valset_id = 'DNSType') as typeName,maxTTL,minTTL from PassiveDNS where DANAME = '" + paramValue + "';",tidb_cursor)
if len(result_passive) > 0:
    for row in result_passive:
        passive.append(row)
        domain_ip(row['ip'],passive)
flag = s_mysql("select count(*) as num from tb_cache where hash = '" + paramHash + "';", tidb_cursor)
if flag[0]['num'] > 0:
    idu_mysql("update tb_cache set text = '" + json.dumps(passive,ensure_ascii=False) + "',param = '" + paramStr + "' where hash = '" + paramHash + "';", tidb_cursor, tidb)
else:
    idu_mysql("insert into tb_cache (text,param,hash) values ('" + json.dumps(passive,ensure_ascii=False) + "','" + paramStr + "','" + paramHash + "');", tidb_cursor, tidb)
tidb.close()

