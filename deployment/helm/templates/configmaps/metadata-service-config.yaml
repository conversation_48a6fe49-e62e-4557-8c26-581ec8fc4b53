{{- if .Values.services.metadata-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: metadata-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    # 元数据服务配置

    # 服务器配置
    server:
      port: 8093
      servlet:
        context-path: /metadata

    # 应用名称
    spring:
      application:
        name: metadata-service

      # 数据库连接配置
      datasource:
        url: jdbc:postgresql://{{ include "nta.postgresqlConfig" . | fromYaml | get "host" }}:{{ include "nta.postgresqlConfig" . | fromYaml | get "port" }}/{{ .Values.services.metadata-service.database }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
        driver-class-name: org.postgresql.Driver

    # MyBatis类型别名包配置
    mybatis-plus:
      type-aliases-package: com.geeksec.metadata.entity

    # Elasticsearch配置
    elasticsearch:
      host: ${ELASTICSEARCH_HOST}
      port: ${ELASTICSEARCH_PORT}
      # 连接配置
      connection:
        timeout: 5000
        socket-timeout: 60000
        max-retry-timeout: 60000
      # 索引配置
      indices:
        metadata-index: nta_metadata
        # 分片配置
        shards: 3
        replicas: 1

    # 元数据服务特定配置
    metadata:
      # 缓存配置
      cache:
        enabled: true
        ttl: 3600  # 缓存时间（秒）
        max-size: 10000
      # 验证配置
      validation:
        enabled: true
        strict-mode: false
      # 索引配置
      indexing:
        # 是否自动创建索引
        auto-create-index: true
        # 批量索引大小
        batch-size: 1000
        # 索引刷新间隔
        refresh-interval: 30

    # 生产环境特定配置
    knife4j:
      production: {{ eq .Values.global.springProfiles "prod" }}

    # 服务配置
    services: {}
{{- end -}}
