{{- if .Values.infrastructure.postgresql.enabled }}
# PostgreSQL 小文件 CSV 数据 ConfigMap
# 大文件通过 PVC 挂载方式处理
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-csv-small-files
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
binaryData:
  # 只包含小于 1MB 的 CSV 文件
  {{- range $path, $_ := .Files.Glob (printf "%s/csv/small/*.csv" .Values.global.helmFilesPath) }}
  {{- $filename := base $path }}
  {{ $filename }}: {{ $.Files.Get $path | b64enc }}
  {{- end }}

  # 系统配置文件
  {{- range $path, $_ := .Files.Glob (printf "%s/csv/config/*.csv" .Values.global.helmFilesPath) }}
  {{- $filename := base $path }}
  {{ $filename }}: {{ $.Files.Get $path | b64enc }}
  {{- end }}
{{- end }}
