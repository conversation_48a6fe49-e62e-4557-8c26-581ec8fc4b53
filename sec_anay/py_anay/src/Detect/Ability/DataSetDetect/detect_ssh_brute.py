
import sys
sys.path.append("./")

import copy 
import pandas as pd

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.AlarmHelper as alarm_helper
import PyGksec.GkHelper.TagHelper as tag_helper
from utils.redis_utils.redis_helper import redis_not_alarm
from utils.mysql_utils.mysql_helper import mysql_not_white


def get_alarm_reason(df:pd.DataFrame) -> list[dict]:
    """
        告警原因生成
        :param DataFrame df: ES会话数据
        :return list[dict]: 字典组成的列表
    """
    alarm_reason = []
    alarm_reason.append({"key":"1. 检测发现22端口向攻击方发送高频率SYN,ACK包或多爆破端口且会话相似", "actual_value":f"{df['pkt_speed'].to_list()[0]}次/s"})
    alarm_reason.append({"key":"2. 检测发现攻击方向对方发送大量认证信息", "actual_value":"0x637572766532"})

    return alarm_reason
        
def get_related_labels(df:pd.DataFrame, include_tags) -> list[dict]:
    """
        告警关联标签,该告警包含的标签和实际关联标签到标签ID映射转换
        :param DataFrame df: ES会话数据
        :param str include_tags: 告警关联的标签名称
        :return list: 关联标签ID
    """
    label_sets = {label for labels in df["Labels"].to_list() for label in labels}

    if include_tags is None or not include_tags or pd.isna(include_tags):
        return []

    # 关联标签ID转换
    tags = include_tags.split("/")
    tag_id_sets = {tag_helper.Tag_Text_Map[tag_name]["Tag_Id"] for tag_name in tags if tag_name in tag_helper.Tag_Text_Map}

    # 实际关联标签
    return list(label_sets & tag_id_sets)

def write_ssh_alarm(df:pd.DataFrame, alarm_name, task_id, batch_id) -> None:
    """
        ICMP隧道告警生成与推送
        :param df: DataFrame格式ES会话数据
        :param alarm_name: 告警名称
        :param task_id: 任务id
        :param batch_id: 批次id
        :return None: 无返回
    """
    alarm_data = alarm_helper.get_alarm_json_data(alarm_name=alarm_name)
    alarm_data["attack_route"] = []
    alarm_data["victim"] = [{"ip": vip} for vip in df.dIp.drop_duplicates()][:10]
    alarm_data["attacker"] = [{"ip": aip} for aip in df.sIp.drop_duplicates()][:10]
    alarm_data["targets"] = [{"name": aip["ip"], "type":"ip", "labels":[]} for aip in alarm_data["attacker"]][:10]

    alarm_data["alarm_reason"] = get_alarm_reason(df)
    alarm_data["alarm_principle"] = '检测发现大量登录失败的SSH连接，存在SSH爆破风险。' 
    alarm_data["alarm_handle_method"] = "检查服务器是否是否存在SSH爆破情况，将该告警IP过滤。"

    alarm_data["alarm_related_label"] = get_related_labels(df, alarm_helper.ALARM_KNOW_NAME_MAP[alarm_name]["include_tags"])
    alarm_data["alarm_session_list"] = df["SessionId"].drop_duplicates().tolist()[:10]
    df["alarm_related_label"] = df.Labels.map(lambda x: list(set(x) & set(alarm_data["alarm_related_label"])))
    df["alarm_related_label"] = df.alarm_related_label.map(lambda x: x if len(x) > 0 else [alarm_data['alarm_knowledge_id']])
    alarm_data["attack_chain_list"] = list({f"{x['dIp']}_{x['sIp']}_{label}" for _, x in df.iterrows() for label in x['alarm_related_label']})

    if alarm_data["targets"] != [] and alarm_data["victim"] != [] and alarm_data["attacker"] != [] and redis_not_alarm(alarm_data["attack_chain_list"]) and mysql_not_white(alarm_data["attack_chain_list"]):
        alarm_data = alarm_helper.append_alarm_task_info(alarm_data, task_id, batch_id)
        alarm_helper.add_remote_alarm_json(alarm_data)

def filter_ssh_datas(df: pd.DataFrame, pkt_speed_threshold: int, port_cnt_threshold: int):
    """
        疑似SSH爆破数据筛选

        :param pd.DataFrame df: 备选数据
        :param int pkt_speed_threshold: 包速率阈值
        :param int port_cnt_threshold: 源端口阈值
        :return DataFrame df: 返回疑似SSH爆破会话数据
    """
    result = []
    df = df[(df["sport_cnt"]>port_cnt_threshold) & (df["pkt_speed"]>pkt_speed_threshold)]

    for _, group in df.groupby(["sip", "dip"]):
        r = group[["snum", "dnum", "sbytes", "dbytes"]]
        # 为了统一衡量不同列数据的变异性，使用变异系数（CV）指标。变异系数是标准差除以均值，用百分数表示，即：CV = (标准差 / 均值) * 100%
        stds = r.std()
        cvs = (stds/r.mean()*100).to_list()

        if all(cv < 20 for cv in cvs) and len([cv for cv in cvs if cv < 10]) >= 3:  # 变异系数小于20,且实际至少三项小于10[当前样本max(cv) < 12,至少三项小于8]
            result.append(group)

    if len(result) == 0:
        return None
    
    df = pd.concat(result).drop_duplicates()
    
    if df.shape[0] > 0:
        return df
    else:
        return None


def agg_ssh_data(table, base_detail, pkt_speed_threshold, port_cnt_threshold) -> pd.DataFrame:
    """
        聚合疑似SSH爆破会话数据

        :param str table: ES索引
        :param dict base_detail: ES基础查询条件
        :param int pkt_speed_threshold: 包速率阈值
        :param int port_cnt_threshold: 源端口阈值
        :return DataFrame df: 返回疑似SSH爆破会话数据
    """
    sport_dict = {
        "doc['sIp'].value":"sip",
        "doc['sPort'].value":"sport",
        "doc['dIp'].value":"dip",
        "doc['pkt.sPayloadNum'].value":"snum",
        "doc['pkt.dPayloadNum'].value":"dnum",
        "doc['pkt.sPayloadBytes'].value":"sbytes",
        "doc['pkt.dPayloadBytes'].value":"dbytes",
        "doc['Duration'].value":"duration",
        }
     
    detail = copy.deepcopy(base_detail)
    detail["aggs"] = eh.make_term_agg(query_key=sport_dict, result_key="ssh_brute")
    
    for es_result in eh.load_agg_data_of_split_time(table, detail, data_key="ssh_brute"):
        if es_result is None or len(es_result) == 0:
            continue
        es_data = [d["key"].split("#") for d in es_result]

        df = pd.DataFrame(es_data, columns=list(sport_dict.values()))

        for key in ["snum", "dnum", "sbytes", "dbytes", "duration"]:
            df[key] = df[key].map(lambda x: int(x))
        df["sport_cnt"] = df.groupby(["sip","dip"])["sport"].transform("count")
        df["dnum_sum"] = df.groupby(["sip","dip"])["dnum"].transform("sum")
        df["duration"] = df.groupby(["sip","dip"])["duration"].transform("max")
        df["pkt_speed"] = df.apply(lambda r: r["dnum_sum"]/r["duration"] if r["duration"] > 0 else r["dnum_sum"], axis=1)

        tf = filter_ssh_datas(df, pkt_speed_threshold, port_cnt_threshold)
        if tf is not None:
            yield tf
        
def check_payload(table, base_detail, df, payload_feature="637572766532", payload_cnt=5) -> pd.DataFrame:
    """
        检查攻击方是否向对方发送大量认证信息(0x637572766532)

        :param str table: ES索引
        :param dict base_detail: ES基础查询条件
        :param DataFrame df: 查询数据
        :param str payload_feature: 负载匹配特征
        :param str payload_cnt: 负载匹配阈值
        :return DataFrame df: 返回符合条件的会话数据
    """
    detail = copy.deepcopy(base_detail)

    detail["query"]["bool"]["must"].append({"term": {"sIp":df["sip"].to_list()[0]}})
    detail["query"]["bool"]["must"].append({"term": {"dIp":df["dip"].to_list()[0]}})
    detail["query"]["bool"]["must"].append({"terms": {"sPort":df["sport"].to_list()}})

    es_data = eh.load_data(table, detail)
    if not es_data:
        return  pd.DataFrame()
    
    data_list = []
    for data in es_data:
        if "pkt" not in data:
            continue 

        if "sPayload" in data["pkt"] and len(data["pkt"]["sPayload"]) > 0 and any(payload_feature in payload for payload in data["pkt"]["sPayload"]):
            data_list.append(data)

            if len(data_list) > payload_cnt:
                break

    if len(data_list) == 0:
        return pd.DataFrame()
    
    df = pd.DataFrame(data_list)

    return df

def make_ssh_query(task_id: int = -1, batch_id: int = -1):
    """
        SSH基本ES查询规则
        :param task_id: 任务id,默认为-1
        :param batch_id: 批次id,默认为-1
    """
    table, detail = eh.get_basic_query(es_type="connect*", task_id=task_id, batch_id=batch_id)

    detail["query"]["bool"]["must"].append({"term": {"AppName":"APP_SSH"}})
    detail["query"]["bool"]["must"].append({"term": {"dPort":"22"}})

    detail["query"]["bool"]["must"].append({"range": {"pkt.sPayloadNum":{"gte": 2}}})
    detail["query"]["bool"]["must"].append({"range": {"pkt.dPayloadNum":{"gte": 3}}})
    detail["query"]["bool"]["must"].append({"range": {"pkt.sPayloadBytes":{"gt": 32}}})
    detail["query"]["bool"]["must"].append({"range": {"pkt.dPayloadBytes":{"gt": 32}}})

    return table, detail

def detect_ssh_brute(task_id: int = -1, batch_id: int = -1, pkt_speed_threshold: int = 15, port_cnt_threshold: int = 3) -> None:
    """
        检测SSH爆破破解,生成告警并推送
        :param task_id: 任务id,默认为-1
        :param batch_id: 批次id,默认为-1
        :param pkt_speed_threshold: 包速率阈值,默认为15
        :param int port_cnt_threshold: 源端口阈值
        :return: 无返回
    """
    table, base_detail = make_ssh_query(task_id, batch_id)

    for sf in agg_ssh_data(table, base_detail, pkt_speed_threshold, port_cnt_threshold):
       df = sf.groupby(["sip","dip"], as_index=False).apply(lambda group: check_payload(table, base_detail, group))

       if df.shape[0] > 0:
        sf = sf[["sip", "dip", "pkt_speed"]]
        df = pd.merge(df, sf, left_on = ["sIp", "dIp"], right_on = ["sip","dip"], how = "inner")

        df.groupby(["dIp"], as_index=False).apply(lambda group:write_ssh_alarm(group, alarm_name="加密通道攻击行为", task_id=task_id, batch_id=batch_id))

if __name__ == '__main__':
    task_id, batch_id = eh.get_latest_task_id('connectinfo*')
    detect_ssh_brute(task_id=task_id, batch_id=batch_id)