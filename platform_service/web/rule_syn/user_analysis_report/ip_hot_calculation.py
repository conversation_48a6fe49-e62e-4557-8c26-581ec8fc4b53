# Last Update:2020-02-27 09:50:00
##
# @file ip_hot_calculation.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-02-27

import os,sys
def ip2int(ip):
    ip_list = ip.strip().split('.')
    ip_int = int(ip_list[0])*256**3+int(ip_list[1])*256**2+int(ip_list[2])*256**1+int(ip_list[3])*256**0
    return ip_int


def int2ip():
    num = int(raw_input('请输入需要转换成IP的整数: '))
    iplist = []
    for n in range(4):
        num,mod = divmod(num,256)
        iplist.insert(0,str(mod))
    return '.'.join(iplist)


def iplist_to_iphot(iplist):
    hash_list=[0]*1000
    for ip in iplist :
        ip_hash = ip2int(ip)
        num = int(ip_hash%997)
        hash_list[num] = 1
    l = 0 
    for w in hash_list :
        if w == 1:
            l= l+ 1
    return l




def  iplist_to_iphot_add(iplist , hash_list):
    for ip in iplist : 
        ip_hash = ip2int(ip)
        num = int(ip_hash%997)
        hash_list[num] = 1
    l = 0 
    for w in hash_list :
        if w == 1:
            l= l+ 1
    return (l,hash_list)

