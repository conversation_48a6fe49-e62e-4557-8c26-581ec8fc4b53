import sys
import time
sys.path.append("./")

import os
from PyGksec.GkConfig import base_dir
import pandas as pd
import PyGksec.GkHelper.ESHelper as eh
from Detect.Finger.GetFingerFromES import get_finger_data_of_esdata
import csv
from PyGksec.GkUtils import common

GKFingerFile = os.path.join(base_dir,"GkData/Knowledge/gk_fingers.csv")
GKFinger_DF = pd.read_csv(GKFingerFile)

Test_Finger_File = os.path.join(base_dir,"GkData/ja3_finger_test.csv")
Test_Finger_DF = pd.read_csv(Test_Finger_File)

finger_id_list = GKFinger_DF["finger_es"].tolist()
gk_finger = ['finger_es','finger_content','ja3_hash','es_type','finger_type']


def update_finger_csv(task_id,batch_id):
    table,detail = eh.get_basic_query(task_id=task_id,batch_id=batch_id,es_type="connectinfo")
    detail["query"]["bool"]["must_not"].append({"term": {"sSSLFinger":"null"}})
    detail["query"]["bool"]["must_not"].append({"term": {"sSSLFinger":""}})
    detail["query"]["bool"]["must_not"].append({"term": {"sSSLFinger":0}})
    detail["query"]["bool"]["must_not"].append({"term": {"dSSLFinger":""}})
    detail["query"]["bool"]["must_not"].append({"term": {"dSSLFinger":"null"}})
    detail["query"]["bool"]["must_not"].append({"term": {"dSSLFinger":0}})
    detail["query"]["bool"]["must_not"].append({"terms": {"sSSLFinger":finger_id_list}})
    detail["query"]["bool"]["must_not"].append({"terms": {"dSSLFinger":finger_id_list}})
    detail["query"]["bool"]["must"].append({"term": {"AppName":"APP_SSL"}})
    eh.describe(table,detail)
    df = eh.load_es_data_to_DF(table,detail,ignore_file=True)
    if df is None:
        print("未查询到信息")
        return
    else:
        print("查询到了更新信息")
    
    finger_update_sSSLFinger_list = list(set(df["sSSLFinger"].tolist()))
    finger_update_dSSLFinger_list = list(set(df["dSSLFinger"].tolist()))#s指纹和d指纹不重复，
    # finger_list = list(set(finger_update_sSSLFinger_list+finger_update_dSSLFinger_list))

    with open(GKFingerFile,mode='a',newline='',encoding='utf8') as gkfingercsv: 
        write_gkfingercsv = csv.writer(gkfingercsv)
        for finger_id in finger_update_sSSLFinger_list:
            finger_info = get_finger_data_of_esdata(finger_id,"sSSLFinger",batch_id)
            if finger_info==common.UNKNOWN:
                continue
            finger_info["finger_es"]=finger_id
            gkfinger_info = update_finger_type_with_customcsv(finger_info,"ja3")
            write_gkfingercsv.writerow(gkfinger_info)
            # print(gkfinger_info)
        for finger_id in finger_update_dSSLFinger_list:
            finger_info = get_finger_data_of_esdata(finger_id,"dSSLFinger",batch_id)
            if finger_info==common.UNKNOWN:
                continue
            finger_info["finger_es"]=finger_id
            gkfinger_info = update_finger_type_with_customcsv(finger_info,"ja3s")
            write_gkfingercsv.writerow(gkfinger_info)
            # print(gkfinger_info)

def update_finger_type_with_customcsv(finger_info,ja3_type):
    # 取客户的字符串，如果查出来的ja3是在用户的表格里，则用用户给的信息更新，用的是字符串in字符串判断
    if ja3_type == "ja3":
        custom_dic = Test_Finger_DF[["finger_tag", "ja3"]].set_index("finger_tag").to_dict(orient='dict')["ja3"]
    elif ja3_type == "ja3s":
        custom_dic = Test_Finger_DF[["finger_tag", "ja3s"]].set_index("finger_tag").to_dict(orient='dict')["ja3s"]
    else:
        print("ja3类型错误")
        return
    for finger_tag in custom_dic.keys():
        if finger_info["ja3_hash"] in custom_dic[finger_tag]:
            finger_info["finger_type"] = finger_tag
            break
    gkfinger_info=[]
    for name in gk_finger:
        gkfinger_info.append(finger_info[name])
    return gkfinger_info


if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    while (True):
        update_finger_csv(task_id,batch_id)
        time.sleep(3600)


