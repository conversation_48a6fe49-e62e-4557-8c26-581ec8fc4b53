# Last Update:2022-05-23 16:09:50
##
# @file user_ip_position_conver.py
# @brief  : 用户IP 地址转换到配置文件
# <AUTHOR>
# @version 0.1.00
# @date 2020-11-03

import  os,pymysql,json,sys
base_json = {}
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
print("["+passwd+"]")
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
use_ip_json = []
def ip_netmesh(ip , mesh,country,province,city):
    mesh_list = mesh.split(".")
    mesh_1 = int(mesh_list[0])
    mesh_2 = int(mesh_list[1])
    mesh_3 = int(mesh_list[2])
    mesh_4 = int(mesh_list[3])
    mesh = 32
    if mesh_1 == 255 and  mesh_2 == 255 and mesh_3 ==255 and  mesh_4 == 255 :
        mesh = 32
    elif mesh_1 == 255 and  mesh_2 == 255 and mesh_3 == 255 and  mesh_4 == 0 :
        mesh = 24
    elif mesh_1 == 255 and  mesh_2 == 255 and mesh_3 == 0 and  mesh_4 == 0 :
        mesh = 16
    elif mesh_1 == 255 and  mesh_2 == 0 and mesh_3 == 0 and  mesh_4 == 0  :
        mesh = 8
    use_ip_json.append({"ip":ip,"mesh":mesh,"country":country,"province":province,"city":city}) ;
    print(use_ip_json)

def mysql_read_conf(task_id ):
    sql = "select * from tb_use_ip_position where task_id = "+ task_id 
    results =  s_mysql(sql,cursor)
    print(results)
    for row in results :
        ip_netmesh(row['ip'],row['net_mesh'], row['country'],row['province'],row['city'])
if len(sys.argv) == 3:
     task_id = sys.argv[1]
     batch_id = sys.argv[2]
     mysql_read_conf(task_id)
     json.dump(use_ip_json,open("user_ip_position.json","w+"), ensure_ascii=False)
     if task_id == "0"  or task_id == "1" : 
         task_path = "/opt/GeekSec/th/bin/conf/" + task_id+"/"
     else:
         task_path = os.path.join("/opt/GeekSec/task/",task_id , batch_id ,"conf")
     os.system("\cp -rf  user_ip_position.json  "+task_path)
     #os.system("\cp -rf  user_ip_position.json  /opt/GeekSec/th/bin/conf/")
else : 
     print("参数错误")
