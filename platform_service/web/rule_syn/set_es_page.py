# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/8/26 18:32
# 文件名称 : set_es_page.py
# 开发工具 : PyCharm
import json,os
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
#print("curl -XPUT '" + base_json["es_es"] + "_all/_settings' -H \"Content-Type: application/json\" -d '{\"index.max_result_window\":\"100000000\"}'")
os.system("curl -XPUT '" + base_json["es_es"] + "/_all/_settings' -H \"Content-Type: application/json\" -d '{\"index.max_result_window\":\"100000000\"}'")
