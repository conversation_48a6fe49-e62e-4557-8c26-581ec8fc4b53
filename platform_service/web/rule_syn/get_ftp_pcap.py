# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/11/12 20:19
# 文件名称 : get_ftp_pcap.py
# 开发工具 : PyCharm
import os, sys, json
param = ''
def get_dir(cmd,arr):
    p = os.popen(cmd)
    lines = p.readlines()
    for line in lines:
        arr.append(line.strip('\n'))
    arr = arr[1:len(arr)]
    return arr
list_arr = []
def get_run_list(param):
    isExists = os.path.exists(param)
    dir_arr = get_dir("ls -l " + param + " |awk '{print $NF}'", [])
    if len(dir_arr) > 0:
        for row in dir_arr:
            obj = {}
            obj["dir"] = param + row
            list_arr.append(obj)
if sys.argv[1] == "all":
    for i in os.listdir("/var/ftp/pcapfiles/"): 
        for p in  os.listdir("/var/ftp/pcapfiles/"+i+"/"):
            param = "/var/ftp/pcapfiles/"+i+"/"+p+"/"
            get_run_list(param)
else:
    for k in  os.listdir("/var/ftp/pcapfiles/"):
        path = "/var/ftp/pcapfiles/"
        if False and len(k) > 6 and  os.path.isdir("/var/ftp/pcapfiles/" +k):
            path = "/var/ftp/pcapfiles/" +k 
            for i in os.listdir(path): 
                if os.path.islink(path+"/" +i) :
                    path_t = os.readlink(path+"/" +i)
                    for p in  os.listdir(path_t):
                        param = path_t+"/"+p+"/"+ sys.argv[1] + "/"
                        if os.path.exists(param):
                            get_run_list(param)
                elif os.path.isdir(path+"/"+i):
                    for p in  os.listdir(path+"/"+i+"/"):
                        param = path+"/"+i+"/"+p+"/"+ sys.argv[1] + "/"
                        if os.path.exists(param):
                            get_run_list(param)
        elif os.path.isdir("/var/ftp/pcapfiles/"  ):
            path = "/var/ftp/pcapfiles/"
            for i in os.listdir(path): 
                if os.path.islink(path+"/" +i):
                    path_t = os.readlink(path+"/" +i)
                    for p in  os.listdir(path_t):
                        param = path_t+"/"+p+"/"+ sys.argv[1] + "/"
                        if os.path.exists(param):
                            get_run_list(param)
                elif os.path.isdir(path+"/" +i) :
                    for p in  os.listdir(path+"/"+i+"/"):
                        param = path+i+"/"+p+"/"+ sys.argv[1] + "/"
                        if os.path.exists(param):
                            get_run_list(param)
print(json.dumps([i for n, i in enumerate(list_arr) if i not in list_arr[:n]]))
