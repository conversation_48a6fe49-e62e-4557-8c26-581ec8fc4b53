# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/7/1 20:12
# 文件名称 : fullflow_tag.py
# 开发工具 : PyCharm
import requests,json,pymysql.cursors,sys,hashlib
# 将ASC转String
def ascToStr(str):
    strObj = ''
    strArr = str.split(",")
    for row in strArr:
        strObj = strObj + chr(int(row))
    return strObj
# 将String计算Hash
def strToHash(str):
    hhaasshh = hashlib.sha1()
    hhaasshh.update(str.encode("utf8"))
    strHash = hhaasshh.hexdigest()
    return strHash
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()

base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='localhost',port=base_json["db_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
tidb = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()

url = "http://localhost:" + str(base_json["webhandle_port"]) + "/web_handle"

# 获取参数
param = sys.argv[1]
paramStr = ascToStr(param)
#paramStr = '{"type":"FullFlow_Tag","sessionId":"845556148195408824"}'
paramHash = strToHash(paramStr)
paramValue = json.loads(paramStr)["sessionId"]
objects = {}
sessionIdTag = ''
sql_session_tag = s_mysql("select group_concat(tai.Tag_Text,' -- ',tai.Black_List) as sessionIdTag from SESSION_ID_TAG sit left join TAG_INFO tai on tai.Tag_Id = sit.tag_id where sit.session_id = '" + paramValue + "';",tidb_cursor)
if not sql_session_tag[0]["sessionIdTag"] is None:
    sessionIdTag = sql_session_tag[0]["sessionIdTag"]

es_param = json.dumps({"type":"ES_CURD","indexType":"connectinfo_*/_search","paramValue":"{'query': {'term': {'SessionId': '" + paramValue + "'}}}"})

es_result = requests.post(url, data=es_param)

if len(json.loads(es_result.text)['data']['hits']['hits']) > 0:
    labels = json.loads(es_result.text)['data']['hits']['hits'][0]['_source']['Labels']
    if not labels is None:
        if len(labels) > 0:
            for row in labels:
                sql_rule_tag = s_mysql("select rule_id,rule_name,rule_level from tb_rule where rule_id = " + str(row) + ";", cursor)
                if len(sql_rule_tag) > 0:
                    strings = str(sql_rule_tag[0]["rule_id"]) + "(" + sql_rule_tag[0]["rule_name"] + ") -- " + str(sql_rule_tag[0]["rule_level"])
                    if sessionIdTag != '':
                        sessionIdTag = sessionIdTag + "," + strings
                    else:
                        sessionIdTag = sessionIdTag + strings
                else:
                    strings = str(row) + "(Unknown) -- 0"
                    if sessionIdTag != '':
                        sessionIdTag = sessionIdTag + "," + strings
                    else:
                        sessionIdTag = sessionIdTag + strings
objects["count"] = 1
list = []
sessionIdTagObj = {}
if sessionIdTag == '':
    sessionIdTagObj["sessionIdTag"] = None
else:
    sessionIdTagObj["sessionIdTag"] = sessionIdTag
list.append(sessionIdTagObj)
objects["list"] = list
flag = s_mysql("select count(*) as num from tb_cache where hash = '" + paramHash + "';", tidb_cursor)
if flag[0]['num'] > 0:
    idu_mysql("update tb_cache set text = '" + json.dumps(objects,ensure_ascii=False) + "',param = '" + paramStr + "' where hash = '" + paramHash + "';", tidb_cursor, tidb)
else:
    idu_mysql("insert into tb_cache (text,param,hash) values ('" + json.dumps(objects,ensure_ascii=False) + "','" + paramStr + "','" + paramHash + "');", tidb_cursor, tidb)
db.close()
tidb.close()
