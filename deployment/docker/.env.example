# NTA Platform 环境配置

# 版本信息
TAG=3.0.0-SNAPSHOT
VERSION=3.0.0-SNAPSHOT

# 基础设施版本
REDIS_VERSION=7.2.4
POSTGRESQL_VERSION=15.4.0
ELASTIC_VERSION=8.12.0
KAFKA_VERSION=3.9.0
NEBULA_VERSION=3.8.0
NGINX_VERSION=1.25.3
DORIS_VERSION=2.1.7

# 监控服务版本
PROMETHEUS_VERSION=v2.50.1
ALERTMANAGER_VERSION=v0.27.0
GRAFANA_VERSION=10.4.0
NODE_EXPORTER_VERSION=v1.7.0
REDIS_EXPORTER_VERSION=v1.58.0
POSTGRESQL_EXPORTER_VERSION=v0.15.0
DORIS_EXPORTER_VERSION=0.5.0
ES_EXPORTER_VERSION=1.5.0
KAFKA_EXPORTER_VERSION=v1.7.0
NEBULA_EXPORTER_VERSION=v3.6.0
CADVISOR_VERSION=v0.47.2

# Grafana 配置
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=admin

# 数据路径配置
PERMANENT_DATA_PATH=/data/nta
REMOVABLE_DATA_PATH=/data/nta

# 数据库配置
POSTGRESQL_ROOT_PASSWORD=password
POSTGRESQL_DATABASE=nta
POSTGRESQL_USERNAME=nta
POSTGRESQL_PASSWORD=password

# Redis 配置
REDIS_PASSWORD=redis

# Elasticsearch 配置
ELASTIC_USERNAME=elastic
ELASTIC_PASSWORD=elastic

# Kafka 配置
KAFKA_USERNAME=admin
KAFKA_PASSWORD=admin
KAFKA_BROKER_ID=1
KAFKA_LISTENERS=PLAINTEXT://:9092
KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092

# Nebula Graph 配置
NEBULA_USER=root
NEBULA_PASSWORD=nebula
NEBULA_SPACE=nta
NEBULA_PARTITION_NUM=3
NEBULA_REPLICA_FACTOR=3

# Apache Doris 配置
DORIS_FE_HTTP_PORT=8030
DORIS_FE_QUERY_PORT=9030
DORIS_FE_EDIT_LOG_PORT=9010
DORIS_BE_WEBSERVER_PORT=8040
DORIS_BE_HEARTBEAT_PORT=9050
DORIS_BE_BRPC_PORT=8060
DORIS_ROOT_PASSWORD=doris
DORIS_REPLICATION_NUM=3
DORIS_BE_DISK_USAGE_PERCENT=80
DORIS_BE_MEMORY_LIMIT=80
DORIS_FE_MEMORY_LIMIT=4Gi
DORIS_BE_MEMORY_LIMIT=8Gi

# 微服务配置
AUTH_SERVICE_PORT=8081
ANALYSIS_SERVICE_PORT=8082
GRAPH_SERVICE_PORT=8083
SEARCH_SERVICE_PORT=8085
NOTIFICATION_SERVICE_PORT=8086
TASK_SERVICE_PORT=8087
CONFIG_SERVICE_PORT=8088
SYSTEM_SERVICE_PORT=8089

# 时区配置
TZ=Asia/Shanghai

# Docker Registry
REGISTRY=hb.gs.lan

# 开发环境配置
HOST_IP=127.0.0.1
SPRING_PROFILES=prod

# 资源限制
AUTH_SERVICE_MEMORY=2Gi
ANALYSIS_SERVICE_MEMORY=4Gi
GRAPH_SERVICE_MEMORY=2Gi
SEARCH_SERVICE_MEMORY=2Gi
NOTIFICATION_SERVICE_MEMORY=1Gi
TASK_SERVICE_MEMORY=1Gi
CONFIG_SERVICE_MEMORY=1Gi
SYSTEM_SERVICE_MEMORY=1Gi
