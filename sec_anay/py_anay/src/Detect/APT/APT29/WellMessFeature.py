import copy
import numpy as np

def transPayload(payload):
    result = ""
    for start in range(0,len(payload),2):
        ch_int = int(payload[start:start+2],16)
        ch = chr(ch_int)
        result += ch
    return result


def getWellMessPayloadFeature(payload):
    payload_str = ""
    ch_range_dict = {
        "space" : [32,32],
        "num": [48,57],
        "lower": [97,122],
        "upper": [65,90]
    }
    ch_count_dict = dict((key,0) for key in ch_range_dict)
    for start in range(0,len(payload),2):
        ch_int = int(payload[start:start+2],16)
        ch = chr(ch_int)
        payload_str += ch
        for key,range_list in ch_range_dict.items():
            if ch_int >= range_list[0] and ch_int <= range_list[1]:
                ch_count_dict[key] += 1
    f = copy.deepcopy(ch_count_dict)
    for key,val in ch_count_dict.items():
        f[f"{key}_ratio"] = val / len(payload_str)
    words = payload_str.strip().split(" ")
    lens = [len(w) for w in words]
    f["max_word_len"] = max(lens)
    f["mean_word_len"] = np.mean(lens)
    f["std_word_len"] = np.std(lens)
    f["word_cnt"] = len(lens)
    return f



def getWellMessCookieFeature(cookie):
    ch_range_dict = {
        "base_special_char" : {"+","/","="},
        "ext_special_char" : {".","%"},
        "left_char" : {"-","_"},
        "split_char": {";"},
        "num": ["0","9"],
        "lower": ["a","z"],
        "upper": ["A","Z"]
    }
    ch_count_dict = dict((key,0) for key in ch_range_dict)
    for ch in cookie:
        for key,range_list in ch_range_dict.items():
            if type(range_list)==list and ch >= range_list[0] and ch <= range_list[1]:
                ch_count_dict[key] += 1
            if type(range_list)==set and ch in range_list:
                ch_count_dict[key] += 1
    ch_count_dict["num_ratio"] = ch_count_dict["num"]/len(cookie)
    ch_count_dict["upper_ratio"] = ch_count_dict["upper"]/len(cookie)
    ch_count_dict["lower_ratio"] = ch_count_dict["lower"]/len(cookie)
    return ch_count_dict


def detectWellMessCookie(cookie):
    if cookie is None or len(cookie)==0:
        return False
    f = getWellMessCookieFeature(cookie)
    over_dict = {"base_special_char":10,"ext_special_char":5,"num_ratio":0.1,"lower_ratio":0.3,"upper_ratio":0.3}
    less_dcit = {"left_char":0,"split_char":1}
    for key,val in over_dict.items():
        if f[key] < val:
            return False
    for key,val in less_dcit.items():
        if f[key] > val:
            return False
    return True

def detectWellMessPayload(payload):
    if payload is None or len(payload)==0:
        return False
    f = getWellMessPayloadFeature(payload)
    over_dict = {"num_ratio":0.05,"lower_ratio":0.2,"upper_ratio":0.3,"std_word_len":0.3,"word_cnt":3}
    less_dcit = {"max_word_len":10,"mean_word_len":6}
    for key,val in over_dict.items():
        if f[key] < val:
            return False
    for key,val in less_dcit.items():
        if f[key] > val:
            return False
    return True

if __name__=="__main__":
    payload = "5A53686651207A77324B2030684254326C6C2049564E7620644670542054477738205832736B53454520714D6A56204B5444744C2E204F473532522045314E20"
    cookie = "HgTRdQ2t=64Vm1+mdDjM+lqq1L+yG1KY+ttpjt+c0yWu+4WDJ7+lWL9W+INgn0+mMn%3Am+oFM67.+wh9lp+MsSDK+B7kFU+5%3ApdW+c%3Aa4s.+ypBuf+%3Ayh3a+nNAuS+C0zeX+bmy%3Ay.+6dhp7+fuG%3Ao+a4Aq%3A+GDqKS+6ZXNW.+iNglw+AX0FT+Ot8Ky+so9Ld+b9YeP.+h1JUr+MbW8o+fry4Z+HQ0G%2CR%3A+wn0aG9K.+FdLfzRZ+3zI415R+vBpPR4Z+dZuDhTT+AvYWsFS.+ dMMzvgDU=VZdaeGu+qON73xw+YXlLtZt+ZB6SElT+jdwPGzw.+up0oTlw+hHxFrC5+QaLyyqf+cvc0nMR+LMkulAy.+IyO%3APN1+6GY+++"
    cookie = "Lsec2AUe=Hua+6EhtGQu+rdhw6+JE2d%2Cc+G%2CIAdc+ZQpc4V+KhQzq+NKmJF4O+IyA9Kfl.+17%3AUe+eZ4C+cuMq1Ql+imnzyv+1dCFw+aJw+vrxeT1+I%2CEVC; GTEZEnK1=6o.+E%2C4+gT8+N2rgU+0SD+i1fOy+pCgxM+XYMv%2CirQE"
    print(getWellMessPayloadFeature(payload))
    print(detectWellMessPayload(payload))
    # print(getWellMessCookieFeature(cookie))
    # print(detectWellMessCookie(cookie))

