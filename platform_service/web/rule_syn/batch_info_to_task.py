##
# @file batch_info_to_task.py
# @brief :把批次的统计信息转换到任务中 
# <AUTHOR>
# @version 0.1.00
# @date 2020-04-05
import sys,json
import pymysql,os
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()

# 查询所有的任务
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
if len(sys.argv) != 2:
    task_info  = json.load(open("/opt/GeekSec/task/task_info.json","r"))
    task_id  = str(int(task_info['task_id']))
else:
    task_id  = sys.argv[1]
sql =  "select a.batch_id from tb_task_batch  a where a.task_id = "+task_id 
relust = s_mysql(sql , cursor)
tb_server_port_all ={}
tb_client_port_all ={}
external_ip_all = {}
find_interior_ip_all ={}
interior_ip_server_all ={}
interior_ip_server_port_all ={}
external_ip_server_all = {}
external_ip_server_port_all = {}
batch_flow_hit_all ={}
batch_mac_mac_t_all ={}
batch_mac_info_t_all = {}
bOnLine = False
if bOnLine:
    path = "/data/File/"+task_id+"/"
    tb_server_port_all = json.load(open(path+"add_ip_server_port_list"))
    tb_client_port_all = json.load(open(path+"add_ip_clinet_port_list"))
    find_interior_ip_all = json.load(open(path+"external_ip"))
    find_interior_ip_all = json.load(open(path+"find_interior_ip"))
    interior_ip_server_all  = json.load(open(path+"interior_ip_server"))
    interior_ip_server_port_all = json.load(open(path+"interior_ip_server_port"))
    external_ip_server_all = json.load(open(path+"external_ip_server"))
    external_ip_server_port_all = json.load(open(path+"external_ip_server_port"))
    batch_flow_hit_all = json.load(open(path+"batch_flow_hit"))
    batch_mac_mac_t_all = json.load(open(path+"batch_mac_mac_t_all"))

for  row in relust :
    path = "/data/File/"+task_id+"/"+str(int(row["batch_id"]))+"/"
    if os.path.exists(path+"add_ip_server_port_list"):
        tb_server_port = json.load(open(path+"add_ip_server_port_list",'r'))
        for k in tb_server_port :
            v = tb_server_port[k]
            if k not in tb_server_port_all:
                tb_server_port_all[k] = v
            else:
                vl = tb_server_port_all[k]
                vl["count"] = vl["count"] + v['count']
                vl["send_bytes"] = vl["send_bytes"] + v['send_bytes']
                vl["recv_bytes"] = vl["recv_bytes"] + v['recv_bytes']
                if vl["begin_time"] > v['begin_time']:
                    vl["begin_time"] =  v['begin_time']
                if  vl["end_time"] < v['end_time']:
                    vl['end_time']= v['end_time']
    # add_ip_clinet_port_list
    file_p = path + "add_ip_clinet_port_list"
    if os.path.exists(file_p):
        tb_client_port = json.load(open(file_p,'r'))
        for k in tb_client_port :
            v = tb_client_port[k]
            if k not in tb_client_port_all:
                tb_client_port_all[k] = v
            else:
                vl = tb_client_port_all[k]
                vl["count"] = vl["count"] + v['count']
                vl["send_bytes"] = vl["send_bytes"] + v['send_bytes']
                vl["recv_bytes"] = vl["recv_bytes"] + v['recv_bytes']
                if vl["begin_time"] > v['begin_time']:
                    vl["begin_time"] =  v['begin_time']
                if  vl["end_time"] < v['end_time']:
                    vl['end_time']= v['end_time']
    # **** 
    file_p = path + "external_ip"
    if os.path.exists(file_p):
        external_ip =  json.load(open(file_p,'r'))
        for k in  external_ip :
            v = external_ip[k]
            if k not in external_ip_all:
                external_ip_all[k] = v
            else:
                 external_ip_all[k] =  external_ip_all[k] + v


    file_p = path + "find_interior_ip"
    if os.path.exists(file_p):
        find_interior_ip  =  json.load(open(file_p,'r'))
        for k in  find_interior_ip :
            v = find_interior_ip[k]
            if k not in find_interior_ip_all:
                find_interior_ip_all[k] = v
            else:
                 find_interior_ip_all[k] =  find_interior_ip_all[k] + v
      # 
    file_p = path + "interior_ip_server_port"
    if os.path.exists(file_p):
        interior_ip_server_port  =  json.load(open(file_p,'r'))
        print(interior_ip_server_port)
        for k in  interior_ip_server_port :
             if k in interior_ip_server_port_all:
                 if  type(interior_ip_server_port[k]) == type({}):
                    interior_ip_server_port_all[k]['count'] =  interior_ip_server_port_all[k]['count'] + interior_ip_server_port[k]['count']
                 else:
                    interior_ip_server_port_all[k]['count'] =  interior_ip_server_port_all[k]['count'] + interior_ip_server_port[k]

             else :
                 interior_ip_server_port_all[k] = {}
                 t = interior_ip_server_port_all[k]
                 if  type(interior_ip_server_port[k]) == type({}):
                      t['count'] = interior_ip_server_port[k]['count']
                 else:
                      t['count'] = interior_ip_server_port[k]
                 t['port'] = k 
                 #interior_ip_server_port_all[k] = {"count":interior_ip_server_port[k]['count'] ,"port":k}
                 #interior_ip_server_port_all[k]['count'] = interior_ip_server_port[k]['count']
    file_p = path + "interior_ip_server"
    if os.path.exists(file_p):
        interior_ip_server  =  json.load(open(file_p,'r'))
        for k in  interior_ip_server :
            v = interior_ip_server[k]
            if k not in interior_ip_server_all:
                interior_ip_server_all[k] = v
            else:
                vl = interior_ip_server_all[k]
                vl["count"] = vl["count"] + v['count']
                if vl["begin_time"] > v['begin_time']:
                    vl["begin_time"] =  v['begin_time']
                if  vl["end_time"] < v['end_time']:
                    vl['end_time']= v['end_time']
    # ****  
    file_p = path + "external_ip_server_port"
    if os.path.exists(file_p):
        external_ip_server_port  =  json.load(open(file_p,'r'))
        print(external_ip_server_port)
        for k in  external_ip_server_port :
             if k  in external_ip_server_port_all:
                 if type(external_ip_server_port[k]) == type({}):
                     external_ip_server_port_all[k]['count'] =  external_ip_server_port_all[k]['count'] + external_ip_server_port[k]['count']
                 else:
                     external_ip_server_port_all[k]['count'] =  external_ip_server_port_all[k]['count'] + external_ip_server_port[k]
             else :
                 if type(external_ip_server_port[k]) == type({}):
                     external_ip_server_port_all[k]={"count":external_ip_server_port[k]['count'],"port":k}
                 else:
                     external_ip_server_port_all[k]={"count":external_ip_server_port[k],"port":k}
                 #external_ip_server_port_all[k]['count'] = external_ip_server_port[k]['count']
    file_p = path + "external_ip_server"
    if os.path.exists(file_p):
        external_ip_server  =  json.load(open(file_p,'r'))
        for k in  external_ip_server :
            v = external_ip_server[k]
            if k not in external_ip_server_all:
                external_ip_server_all[k] = v
            else:
                vl = external_ip_server_all[k]
                print(vl)
                vl["count"] = vl["count"] + v['count']
                if "begin_time" in vl :
                    if vl["begin_time"] > v['begin_time']:
                        vl["begin_time"] =  v['begin_time']
                    if  vl["end_time"] < v['end_time']:
                        vl['end_time']= v['end_time']
    # ****  
    file_p = path + "batch_flow_hit"
    if os.path.exists(file_p):
        batch_flow_hit  =  json.load(open(file_p,'r'))
        for k in  batch_flow_hit :
            v = batch_flow_hit[k]
            if k not in batch_flow_hit_all:
                batch_flow_hit_all[k] = v
            else:
                vl = batch_flow_hit_all[k]
                vl["bytes"] = vl["bytes"] + v['bytes']
                vl["connect_num"] = vl["connect_num"] + v['connect_num']
     
    file_p = path + "mac_ip_bind_t"
    if os.path.exists(file_p):
        mac_info  =  json.load(open(file_p,'r'))
        print(mac_info)
        for k in  mac_info:
            v = mac_info[k]
            if k not in batch_mac_info_t_all:
                batch_mac_info_t_all[k] = v
            else:
                vl =batch_mac_info_t_all[k]
                for ip in v :
                    if ip not in v :
                        vl[ip] = {}
                    for port in v[ip]:
                        if port not in vl[ip]:
                            vl[ip][port] =  1
                        else:
                             vl[ip][port] =  vl[ip][port]  + 1

    file_p = path + "mac_mac_info_all"
    if os.path.exists(file_p):
        mac_mac_info  =  json.load(open(file_p,'r'))
        for k in  mac_mac_info:
            v = mac_mac_info[k]
            if k not in batch_mac_mac_t_all:
                batch_mac_mac_t_all[k] = v
            else:
                vl =batch_mac_mac_t_all[k]
                vl["count"] = vl["count"] + v['count']

path = "/data/File/"+task_id+"/"
bpath = path
if os.path.exists(path) == False:
    os.system("mkdir  -p "+path)
json.dump(tb_server_port_all,open(path+"add_ip_server_port_list","w+"))
json.dump(tb_client_port_all,open(path+"add_ip_clinet_port_list","w+"))
json.dump(external_ip_all,open(path+"external_ip","w+"))
json.dump(find_interior_ip_all,open(path+"find_interior_ip","w+"))
json.dump(interior_ip_server_all,open(path+"interior_ip_server","w+"))
json.dump(interior_ip_server_port_all,open(path+"interior_ip_server_port","w+"))
json.dump(external_ip_server_all,open(path+"external_ip_server","w+"))
json.dump(external_ip_server_port_all,open(path+"external_ip_server_port","w+"))
json.dump(batch_flow_hit_all,open(path+"batch_flow_hit","w+"))
json.dump(batch_mac_mac_t_all,open(path+"batch_mac_mac_t_all","w+"))
json.dump(batch_mac_info_t_all,open(path+"batch_mac_info_t_all","w+"))
os.system("python3 /opt/GeekSec/web/rule_syn/analysis_mac_situation.py "+task_id)
if bOnLine:
    os.system("rm -rf  "+bpath+"/*")
