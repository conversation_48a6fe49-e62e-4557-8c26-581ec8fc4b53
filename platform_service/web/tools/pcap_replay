#!/bin/python3
##
# @file pcap_replay.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-09-27


import os,sys
network = ""
# 扫描目录的文件
list_t = []
def read_dir_cap(path):
    if os.path.isdir(path):
        for root, dirs, files in os.walk(path, topdown=False):
            for name in files:
                if name.endswith(".pcap") or name.endswith(".cap") or name.endswith(".pcapng"):
                    file_name = os.path.join(root, name)
                    list_t.append(file_name)
    else :
        list_t.append(path)
def pcap_replay(list_t,path):
    read_dir_cap(path)

    astr = ""
    for  k in list_t :
        astr = astr + " " +k 
    for w_file in list_t:
        #print("tcpreplay -i "+network+ " "+ astr + " "+w_file)
        os.system("tcpreplay -i "+network+ " "+ astr+ " "+w_file)
cer_type = 0
path = ""
for k in sys.argv:
    if cer_type == 0 :
        if k.startswith( "-i"):
            cer_type  = 1
        elif k.startswith("-P"):
            cer_type = 2
        else:
            list_t.append(k)

    elif cer_type == 1:
        network = k
        cer_type = 0
    elif cer_type == 2:
        path = k
        cer_type = 0

print(path)
if path == "":
    print("请指定发送文件的目录或文件路径 -P 目录/文件路径")
    sys.exit(1)
#read_dir_cap(path)
pcap_replay(list_t , path)

