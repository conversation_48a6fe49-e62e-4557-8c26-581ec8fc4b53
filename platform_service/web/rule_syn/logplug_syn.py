# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : logplug.py
# 开发工具 : PyCharm
import os,json,pymysql.cursors
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
def write_file(filename , context):
    os.system("echo \"\">"+filename)
    fnew = open(filename, "w")
    fnew.write(context)
    fnew.close()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
cursor.execute("select plug_json from tb_log_plug;")
context=""
results = cursor.fetchall()
for row in results:
    fname =row['plug_json']
    context=context + "\n\n"+fname
os.system("echo \"\">logplug.json")
write_file("logplug.json",context)
os.system("echo \"logplug_syn runing\">>log.log")
# 数据同步
#os.system ("cp -rf logplug.json /opt/GeekSec/th/bin/plugin/msg_handle/user/logplug.json")
db.close()

