##
# @file batch_plugin.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-04-28
import sys,os,pymysql.cursors,xml.etree.ElementTree as ET ,json,time
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()

task_id  =  sys.argv[1]
batch_id =  sys.argv[2]

base_json = {}
def task_mkdir(path):
    os.system("mkdir -p "+ path + "/db")
    os.system("mkdir -p "+ path + "/conf")
    os.system("mkdir -p "+ path + "/Config")
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
if task_id == "0"  or task_id == "1":
    task_path =  "/opt/GeekSec/th/bin/conf/"+str(task_id)+ "/"
    task_mkdir(task_path)
else:
    task_path = "/opt/GeekSec/task/"+str(task_id)+"/"+str(batch_id)+"/conf/"
    task_mkdir(task_path)
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
print(passwd)
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
    db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
 
plusin_json = {"proto_parse":{"should_log_def":1,"plugin":[]},"full_flow":{"should_log_def":1,"plugin":[]}}

sql = "select  task_id , batch_id , full_flow_should_log_def  ,  parse_proto_should_log_def   from  tb_task_batch where state =1 and batch_id =  "+ batch_id  + " and task_id = " + task_id
res =s_mysql(sql  ,cursor) 
for row  in res :
    plusin_json["proto_parse"]["should_log_def"] = int(row["parse_proto_should_log_def"]) 
    plusin_json["full_flow"]["should_log_def"] = int(row["full_flow_should_log_def"]) 
      
sql2 = "select  a.batch_id , a.plugin_id ,a.should_log_def , b.plugin_name , b.plugin_type  from  tb_batch_plugin  a  ,  tb_plugin b  where a.plugin_id = b.plugin_id and a.batch_id = "+str(batch_id)
res2 =s_mysql(sql2  ,cursor) 
print(res2)
for row  in res2 :
     plugin_one_json = {"id":int(row["plugin_id"]), "name":row["plugin_name"], "should_log":int(row["should_log_def"])} 
     if row["plugin_type"] == 2:
         plusin_json["proto_parse"]["plugin"].append(plugin_one_json)
     if row["plugin_type"] == 1:
         plusin_json["full_flow"]["plugin"].append(plugin_one_json)
print(task_path + "plugin_conf.json")
json.dump(plusin_json , open( task_path + "plugin_conf.json","w+"))
