-- 使用nta数据库
USE nta;

CREATE TABLE IF NOT EXISTS ods_alarm_log (
    `alarm_id` VARCHAR(32) NOT NULL COMMENT '告警唯一ID (MD5哈希)',
    `event_time` DATETIME NOT NULL COMMENT '事件发生时间',
    `alarm_create_time` DATETIME NOT NULL COMMENT '告警记录创建时间 (系统生成)',
    `alarm_update_time` DATETIME COMMENT '告警记录更新时间',
    `alarm_knowledge_id` BIGINT COMMENT '告警知识库ID (关联预定义告警类型)',
    `alarm_name` VARCHAR(255) COMMENT '告警名称',
    `alarm_desc` TEXT COMMENT '告警详细描述',
    `alarm_status` VARCHAR(20) DEFAULT 'OPEN' COMMENT '告警状态 (OPEN:未处理, ACKNOWLEDGED:已确认, RESOLVED:已解决, FALSE_POSITIVE:误报)',
    `alarm_type` VARCHAR(100) COMMENT '告警分类 (例如：Exploit, Reconnaissance, Malware)',
    `alarm_reason` JSON COMMENT '告警触发原因列表 (JSON数组，每个对象包含key和actual_value)',
    `alarm_suggestion` TEXT COMMENT '处置建议',
    `alarm_detail` JSON COMMENT '告警技术细节 (JSON对象，可包含如隧道信息、连接统计等)',
    `model_id` VARCHAR(100) COMMENT '检测模型ID',
    `rule_id` BIGINT COMMENT '检测规则ID (关联alarm_rules表)',
    `rule_name` VARCHAR(255) COMMENT '检测规则名称',
    `resolved_at` DATETIME COMMENT '告警解决时间',
    `resolved_by` VARCHAR(100) COMMENT '解决人员',
    `attack_chain_name` ARRAY<VARCHAR(100)> COMMENT '攻击链阶段名称列表 (例如：MITRE ATT&CK Tactic)',
    `attack_stage` VARCHAR(100) COMMENT '攻击阶段细分 (例如：MITRE ATT&CK Technique ID)',
    `attack_family` JSON COMMENT '威胁家族/攻击组织信息 (JSON数组，每个对象包含family_name和family_type)',
    `attack_level` VARCHAR(20) COMMENT '告警等级/严重程度 (LOW, MEDIUM, HIGH, CRITICAL)',
    `threat_type` VARCHAR(100) COMMENT '具体威胁类型 (例如：Ransomware, Phishing, Botnet)',
    `confidence` DOUBLE COMMENT '检测结果置信度 (范围0.0-1.0)',
    `attacker` JSON COMMENT '攻击者信息列表 (JSON数组，每个对象包含ip, port, geo, asset_info等)',
    `victim` JSON COMMENT '受害者信息列表 (JSON数组，每个对象包含ip, port, geo, asset_info, host, app_name等)',
    `targets` JSON COMMENT '其他关联目标信息列表 (JSON数组，每个对象包含name, type, labels等)',
    `ioc` JSON COMMENT '威胁指标IOC信息列表 (JSON数组，每个对象包含ioc_value, ioc_type, risk_level, desc等)',
    `src_ip` VARCHAR(45) COMMENT '主要源IP地址 (IPv4或IPv6)',
    `src_port` INT COMMENT '主要源端口',
    `src_mac` VARCHAR(17) COMMENT '主要源MAC地址',
    `dst_ip` VARCHAR(45) COMMENT '主要目的IP地址 (IPv4或IPv6)',
    `dst_port` INT COMMENT '主要目的端口',
    `dst_mac` VARCHAR(17) COMMENT '主要目的MAC地址',
    `vlan_id` INT COMMENT 'VLAN ID',
    `protocol` VARCHAR(50) COMMENT '网络协议 (例如：TCP, UDP, ICMP, HTTP)',
    `duration` BIGINT COMMENT '会话/事件持续时间 (单位：毫秒)',
    `session_id` VARCHAR(255) COMMENT '关联网络会话ID',
    `data_source` VARCHAR(100) COMMENT '数据来源标识 (例如：探针ID, 日志源类型)',
    `original_event_ids` ARRAY<VARCHAR(255)> COMMENT '原始事件ID列表 (用于溯源)',
    `pcap_file_list` ARRAY<VARCHAR(255)> COMMENT '关联Pcap文件路径或名称列表',
    `task_id` VARCHAR(100) COMMENT '关联检测任务ID',
    -- ConnectBasicInfo 中的字段，已扁平化处理
    `src_packets` BIGINT COMMENT '源发送包数',
    `dst_packets` BIGINT COMMENT '目的接收包数',
    `src_bytes` BIGINT COMMENT '源发送字节数',
    `dst_bytes` BIGINT COMMENT '目的接收字节数',
    `src_payload_bytes` BIGINT COMMENT '源发送载荷字节数',
    `dst_payload_bytes` BIGINT COMMENT '目的接收载荷字节数',
    `src_fragment_count` BIGINT COMMENT '源IP分片数',
    `dst_fragment_count` BIGINT COMMENT '目的IP分片数',
    `src_retran_count` BIGINT COMMENT '源TCP重传数',
    `dst_retran_count` BIGINT COMMENT '目的TCP重传数',
    -- BasicTunnel 中的字段，已扁平化处理 (也可考虑放入alarm_detail JSON中)
    `tunnel_type` VARCHAR(50) COMMENT '检测到的隧道类型 (例如：HTTP, DNS, SSL, ICMP)',
    `tunnel_entropy` DOUBLE COMMENT '隧道载荷熵值',
    `tunnel_byte_distribution` TEXT COMMENT '隧道字节分布特征描述',
    `tunnel_message_distribution` TEXT COMMENT '隧道消息分布特征描述',
    `tunnel_periodicity` DOUBLE COMMENT '隧道通信周期性特征值'
)
ENGINE=OLAP
DUPLICATE KEY(`alarm_id`, `event_time`)
COMMENT "告警日志表，存储检测到的各类安全告警详细信息"
-- PARTITION BY RANGE(`event_time`) () -- 实际使用时请删除此行注释，并配置具体分区或使用下面的动态分区
DISTRIBUTED BY HASH(`alarm_id`) BUCKETS 16 -- 分桶数可根据集群规模调整
PROPERTIES (
    "replication_allocation" = "tag.location.default: 3", -- 副本策略，根据实际配置修改
    "dynamic_partition.enable" = "true",
    "dynamic_partition.time_unit" = "DAY",
    "dynamic_partition.start" = "-30",      -- 保留最近30天的分区
    "dynamic_partition.end" = "3",        -- 预创建未来3天的分区
    "dynamic_partition.prefix" = "p",
    "dynamic_partition.buckets" = "16"     -- 每个动态分区的分桶数
    -- 可根据需要添加其他PROPERTIES，如bloom_filter_columns, storage_format等
);
