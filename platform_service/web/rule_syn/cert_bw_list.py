# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : cert_bw_list.py
# 开发工具 : PyCharm
import os,pymysql.cursors,json
def write_file(filename , context):
    os.system("echo \"\">"+filename)
    fnew = open(filename, "w")
    fnew.write(context)
    fnew.close()
base_json = {}
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
cursor.execute("select certbw_json from tb_cert_bw;")
context=""
results = cursor.fetchall()
for row in results:
    fname =row['certbw_json']
    context=context + "\n\n"+fname
os.system("echo \"\">certbwlist.json")
write_file("certbwlist.json",context)
os.system("echo \"cert_bw_list runing\">>log.log")
isExists = os.path.exists("/opt/GeekSec/th/bin/conf/")
if not isExists:
    os.makedirs("/opt/GeekSec/th/bin/conf/")
os.system ("cp -rf certbwlist.json /opt/GeekSec/th/bin/conf/certbwlist.json")
db.close()

