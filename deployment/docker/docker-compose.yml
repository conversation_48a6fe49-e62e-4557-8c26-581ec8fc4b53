version: '3.8'

services:
  # 基础设施服务
  redis:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/bitnami/redis:${REDIS_VERSION:-7.0.12}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_DISABLE_COMMANDS=FLUSHDB,FLUSHALL,CONFIG
      - REDIS_RDB_POLICY_DISABLED=yes
    volumes:
      - ${PERMANENT_DATA_PATH:-/data/nta}/redis:/bitnami/redis/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: always

  postgresql:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/bitnami/postgresql:${POSTGRESQL_VERSION:-15.4.0}
    volumes:
      - ${PERMANENT_DATA_PATH:-/data/nta}/postgresql:/bitnami/postgresql/data
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - POSTGRESQL_ROOT_USER=postgres
      - POSTGRESQL_ROOT_PASSWORD=${POSTGRESQL_ROOT_PASSWORD:-password}
      - POSTGRESQL_USERNAME=${POSTGRESQL_USERNAME:-nta}
      - POSTGRESQL_PASSWORD=${POSTGRESQL_PASSWORD:-password}
      - POSTGRESQL_DATABASE=${POSTGRESQL_DATABASE:-nta}
    healthcheck:
      test: ["CMD", "pg_isready", "-h", "localhost", "-U", "postgres"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: always
    ports:
      - "5432:5432"

  elasticsearch:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/bitnami/elasticsearch:${ELASTIC_VERSION:-7.17.14}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - ELASTICSEARCH_NODE_NAME=elasticsearch
      - ELASTICSEARCH_HEAP_SIZE=4g
      - ELASTICSEARCH_ENABLE_SECURITY=false
      - ELASTICSEARCH_LOCK_ALL_MEMORY=yes
    volumes:
      - ${REMOVABLE_DATA_PATH:-/data/nta}/elasticsearch/data:/opt/bitnami/elasticsearch/data
      - ./conf/elasticsearch.yml:/opt/bitnami/elasticsearch/config/elasticsearch.yml:ro
    healthcheck:
      test: ["CMD", "curl", "--silent", "--fail", "localhost:9200/_cluster/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: always
    ports:
      - "9200:9200"

  kafka:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/bitnami/kafka:${KAFKA_VERSION:-3.9.0}
    ports:
      - "9092:9092"
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      # 基本配置
      - KAFKA_CFG_NODE_ID=0
      - KAFKA_CFG_MESSAGE_MAX_BYTES=20000000
      # KRaft 模式配置
      - KAFKA_KRAFT_CLUSTER_ID=MkU3OEVBNTcwNTJENDM2Qk
      - KAFKA_CFG_PROCESS_ROLES=broker,controller
      - KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka:9093
      # 监听器配置
      - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093,INTERNAL://:9094
      - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://127.0.0.1:9092,INTERNAL://kafka:9094
      - KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=PLAINTEXT:PLAINTEXT,CONTROLLER:PLAINTEXT,INTERNAL:PLAINTEXT
      - KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_CFG_INTER_BROKER_LISTENER_NAME=INTERNAL
      # 日志配置
      - KAFKA_CFG_LOG_RETENTION_BYTES=536870912000
      - KAFKA_CFG_LOG_SEGMENT_BYTES=250000000
      - KAFKA_CFG_LOG_RETENTION_CHECK_INTERVAL_MS=30000
      - KAFKA_CFG_LOG_RETENTION_HOURS=72
    volumes:
      - ${REMOVABLE_DATA_PATH:-/data/nta}/kafka:/bitnami/kafka/data
    healthcheck:
      test: ["CMD", "kafka-topics.sh", "--list", "--bootstrap-server", "localhost:9094"]
      interval: 30s
      timeout: 20s
      retries: 3
      start_period: 30s
    restart: always

  nebula-metad:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/vesoft/nebula-metad:${NEBULA_VERSION:-3.8.0}
    environment:
      - USER=root
    command:
      - --meta_server_addrs=nebula-metad:9559
      - --local_ip=nebula-metad
      - --ws_ip=nebula-metad
      - --port=9559
      - --ws_http_port=19559
      - --data_path=/data/meta
      - --log_dir=/logs/meta
      - --v=0
      - --minloglevel=0
    healthcheck:
      test: ["CMD", "curl", "-sf", "http://nebula-metad:19559/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    volumes:
      - ${REMOVABLE_DATA_PATH:-/data/nta}/nebula/data/meta:/data
      - ${REMOVABLE_DATA_PATH:-/data/nta}/nebula/logs/meta:/logs
    restart: always
    cap_add:
      - SYS_PTRACE
    ports:
      - "9559:9559"
      - "19559:19559"

  nebula-storaged:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/vesoft/nebula-storaged:${NEBULA_VERSION:-3.8.0}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - USER=root
    command:
      - --meta_server_addrs=nebula-metad:9559
      - --local_ip=nebula-storaged
      - --ws_ip=nebula-storaged
      - --port=9779
      - --ws_http_port=19779
      - --data_path=/data/storage
      - --log_dir=/logs/storage
      - --v=0
      - --minloglevel=0
    depends_on:
      - nebula-metad
    healthcheck:
      test: ["CMD", "curl", "-sf", "http://nebula-storaged:19779/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    volumes:
      - ${REMOVABLE_DATA_PATH:-/data/nta}/nebula/data/storage:/data
      - ${REMOVABLE_DATA_PATH:-/data/nta}/nebula/logs/storage:/logs
    restart: always
    cap_add:
      - SYS_PTRACE
    ports:
      - "9779:9779"
      - "19779:19779"

  nebula-graphd:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/vesoft/nebula-graphd:${NEBULA_VERSION:-3.8.0}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - USER=root
    command:
      - --meta_server_addrs=nebula-metad:9559
      - --port=9669
      - --local_ip=nebula-graphd
      - --ws_ip=nebula-graphd
      - --ws_http_port=19669
      - --log_dir=/logs/graph
      - --v=0
      - --minloglevel=0
    depends_on:
      - nebula-storaged
    healthcheck:
      test: ["CMD", "curl", "-sf", "http://nebula-graphd:19669/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    volumes:
      - ${REMOVABLE_DATA_PATH:-/data/nta}/nebula/logs/graph:/logs
    restart: always
    cap_add:
      - SYS_PTRACE
    ports:
      - "9669:9669"
      - "19669:19669"

  # 微服务
  auth-service:
    image: ${REGISTRY:-hb.gs.lan}/nta/auth-service:${TAG:-latest}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES:-prod}
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_DATABASE=nta
      - DB_USERNAME=${POSTGRESQL_USERNAME:-nta}
      - DB_PASSWORD=${POSTGRESQL_PASSWORD:-password}
    depends_on:
      postgresql:
        condition: service_healthy
    restart: always
    ports:
      - "8081:8081"

  analysis-service:
    image: ${REGISTRY:-hb.gs.lan}/nta/analysis-service:${TAG:-latest}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES:-prod}
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_DATABASE=nta
      - DB_USERNAME=${POSTGRESQL_USERNAME:-nta}
      - DB_PASSWORD=${POSTGRESQL_PASSWORD:-password}
      - ELASTICSEARCH_HOSTS=elasticsearch:9200
    depends_on:
      postgresql:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    restart: always
    ports:
      - "8082:8082"

  graph-service:
    image: ${REGISTRY:-hb.gs.lan}/nta/graph-service:${TAG:-latest}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES:-prod}
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_DATABASE=nta
      - DB_USERNAME=${POSTGRESQL_USERNAME:-nta}
      - DB_PASSWORD=${POSTGRESQL_PASSWORD:-password}
      - NEBULA_HOSTS=nebula-graphd:9669
      - NEBULA_USERNAME=root
      - NEBULA_PASSWORD=nebula
    depends_on:
      postgresql:
        condition: service_healthy
      nebula-graphd:
        condition: service_healthy
    restart: always
    ports:
      - "8083:8083"

  search-service:
    image: ${REGISTRY:-hb.gs.lan}/nta/search-service:${TAG:-latest}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES:-prod}
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_DATABASE=nta
      - DB_USERNAME=${POSTGRESQL_USERNAME:-nta}
      - DB_PASSWORD=${POSTGRESQL_PASSWORD:-password}
      - ELASTICSEARCH_HOSTS=elasticsearch:9200
    depends_on:
      postgresql:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    restart: always
    ports:
      - "8085:8085"

  notification-service:
    image: ${REGISTRY:-hb.gs.lan}/nta/notification-service:${TAG:-latest}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES:-prod}
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_DATABASE=nta
      - DB_USERNAME=${POSTGRESQL_USERNAME:-nta}
      - DB_PASSWORD=${POSTGRESQL_PASSWORD:-password}
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9094
    depends_on:
      postgresql:
        condition: service_healthy
      kafka:
        condition: service_healthy
    restart: always
    ports:
      - "8086:8086"

  task-service:
    image: ${REGISTRY:-hb.gs.lan}/nta/task-service:${TAG:-latest}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES:-prod}
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_DATABASE=nta
      - DB_USERNAME=${POSTGRESQL_USERNAME:-nta}
      - DB_PASSWORD=${POSTGRESQL_PASSWORD:-password}
    depends_on:
      postgresql:
        condition: service_healthy
    restart: always
    ports:
      - "8087:8087"

  config-service:
    image: ${REGISTRY:-hb.gs.lan}/nta/config-service:${TAG:-latest}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES:-prod}
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_DATABASE=nta
      - DB_USERNAME=${POSTGRESQL_USERNAME:-nta}
      - DB_PASSWORD=${POSTGRESQL_PASSWORD:-password}
    depends_on:
      postgresql:
        condition: service_healthy
    restart: always
    ports:
      - "8088:8088"

  system-service:
    image: ${REGISTRY:-hb.gs.lan}/nta/system-service:${TAG:-latest}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES:-prod}
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_DATABASE=nta
      - DB_USERNAME=${POSTGRESQL_USERNAME:-nta}
      - DB_PASSWORD=${POSTGRESQL_PASSWORD:-password}
    depends_on:
      postgresql:
        condition: service_healthy
    restart: always
    ports:
      - "8089:8089"

  security-service:
    image: ${REGISTRY:-hb.gs.lan}/nta/security-service:${TAG:-latest}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - SPRING_PROFILES_ACTIVE=${SPRING_PROFILES:-prod}
      - POSTGRESQL_HOST=postgresql
      - POSTGRESQL_PORT=5432
      - POSTGRESQL_DATABASE=nta
      - DB_USERNAME=${POSTGRESQL_USERNAME:-nta}
      - DB_PASSWORD=${POSTGRESQL_PASSWORD:-password}
    depends_on:
      postgresql:
        condition: service_healthy
    restart: always
    ports:
      - "8090:8090"

  # 前端服务
  frontend:
    image: ${REGISTRY:-hb.gs.lan}/nta/frontend:${TAG:-latest}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
    depends_on:
      - auth-service
      - analysis-service
      - graph-service
      - search-service
      - notification-service
      - task-service
      - config-service
      - system-service
      - security-service
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: always
    ports:
      - "80:80"

  # 网关服务
  nginx-gateway:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/bitnami/nginx:${NGINX_VERSION:-1.25.3}
    ports:
      - "443:443"
    volumes:
      - ./conf/nginx.conf:/opt/bitnami/nginx/conf/nginx.conf
      - ./conf/ssl/cert.pem:/etc/nginx/cert/cert.pem
      - ./conf/ssl/key.pem:/etc/nginx/cert/key.pem
    depends_on:
      - frontend
      - auth-service
      - analysis-service
      - graph-service
      - search-service
      - notification-service
      - task-service
      - config-service
      - system-service
      - security-service
    restart: always

  # 监控服务
  prometheus:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/prom/prometheus:${PROMETHEUS_VERSION:-v2.50.1}
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./prometheus/rules:/etc/prometheus/rules
      - ${PERMANENT_DATA_PATH:-/data/nta}/prometheus:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    ports:
      - "9090:9090"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  pushgateway:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/prom/pushgateway:${PUSHGATEWAY_VERSION:-v1.7.0}
    ports:
      - "9091:9091"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:9091/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  alertmanager:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/prom/alertmanager:${ALERTMANAGER_VERSION:-v0.27.0}
    volumes:
      - ./prometheus/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - ${PERMANENT_DATA_PATH:-/data/nta}/alertmanager:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
    ports:
      - "9093:9093"
    restart: always
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:9093/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  grafana:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/grafana/grafana:${GRAFANA_VERSION:-10.4.0}
    volumes:
      - ${PERMANENT_DATA_PATH:-/data/nta}/grafana:/var/lib/grafana
      - ./prometheus/grafana/provisioning:/etc/grafana/provisioning
      - ./prometheus/grafana/dashboards:/var/lib/grafana/dashboards
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin}
      - GF_USERS_ALLOW_SIGN_UP=false
    ports:
      - "3000:3000"
    restart: always
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  node-exporter:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/prom/node-exporter:${NODE_EXPORTER_VERSION:-v1.7.0}
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    ports:
      - "9100:9100"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:9100/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  redis-exporter:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/oliver006/redis_exporter:${REDIS_EXPORTER_VERSION:-v1.58.0}
    environment:
      - REDIS_ADDR=redis://redis:6379
    ports:
      - "9121:9121"
    restart: always
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:9121/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  postgresql-exporter:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/prometheuscommunity/postgres-exporter:${POSTGRESQL_EXPORTER_VERSION:-v0.15.0}
    environment:
      - DATA_SOURCE_NAME=postgresql://${POSTGRESQL_USERNAME:-nta}:${POSTGRESQL_PASSWORD:-password}@postgresql:5432/${POSTGRESQL_DATABASE:-nta}?sslmode=disable
    ports:
      - "9187:9187"
    restart: always
    depends_on:
      - postgresql
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:9187/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  # Apache Doris 服务
  doris-fe:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/apache/doris:${DORIS_VERSION:-2.1.7}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - FE_SERVERS=doris-fe:9010
      - DORIS_ROLE=fe
    volumes:
      - ${PERMANENT_DATA_PATH:-/data/nta}/doris/fe/meta:/doris/fe/meta
      - ${PERMANENT_DATA_PATH:-/data/nta}/doris/fe/log:/doris/fe/log
      - ./conf/fe.conf:/doris/fe/conf/fe.conf
    ports:
      - "8030:8030" # HTTP 端口
      - "9030:9030" # MySQL 协议端口
      - "9020:9020" # RPC 端口
      - "9010:9010" # 编辑日志端口
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8030/api/bootstrap"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: always

  doris-be:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/apache/doris:${DORIS_VERSION:-2.1.7}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
      - FE_SERVERS=doris-fe:9010
      - DORIS_ROLE=be
    volumes:
      - ${PERMANENT_DATA_PATH:-/data/nta}/doris/be/storage:/doris/be/storage
      - ${PERMANENT_DATA_PATH:-/data/nta}/doris/be/log:/doris/be/log
      - ./conf/be.conf:/doris/be/conf/be.conf
    ports:
      - "8040:8040" # HTTP 端口
      - "9060:9060" # Thrift 服务端口
      - "9050:9050" # 心跳服务端口
      - "8060:8060" # BRPC 端口
    depends_on:
      doris-fe:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8040/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: always

  doris-exporter:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/apache/doris-exporter:${DORIS_EXPORTER_VERSION:-0.5.0}
    environment:
      - DORIS_FE_HOST=doris-fe
      - DORIS_FE_QUERY_PORT=9030
      - DORIS_FE_HTTP_PORT=8030
      - DORIS_BE_HOST=doris-be
      - DORIS_BE_HTTP_PORT=8040
      - DORIS_USERNAME=root
      - DORIS_PASSWORD=
    ports:
      - "9177:9177"
    depends_on:
      doris-fe:
        condition: service_healthy
      doris-be:
        condition: service_healthy
    restart: always

  elasticsearch-exporter:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/justwatch/elasticsearch_exporter:${ES_EXPORTER_VERSION:-1.5.0}
    command:
      - '--es.uri=http://elasticsearch:9200'
      - '--es.all=true'
      - '--es.indices=true'
      - '--es.cluster_settings=true'
      - '--web.listen-address=:9114'
    ports:
      - "9114:9114"
    restart: always
    depends_on:
      - elasticsearch
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:9114/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  kafka-exporter:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/danielqsj/kafka-exporter:${KAFKA_EXPORTER_VERSION:-v1.7.0}
    command:
      - '--kafka.server=kafka:9094'
      - '--web.listen-address=:9308'
    ports:
      - "9308:9308"
    restart: always
    depends_on:
      - kafka
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:9308/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  nebula-exporter:
    image: ${REGISTRY:-hb.gs.lan}/proxy_cache/vesoft/nebula-stats-exporter:${NEBULA_EXPORTER_VERSION:-v3.6.0}
    environment:
      - TZ=${TZ:-Asia/Shanghai}
    command:
      - '--listen-address=:9201'
      - '--metric-endpoint=http://nebula-graphd:19669,http://nebula-metad:19559,http://nebula-storaged:19779'
      - '--interval=15'
    ports:
      - "9201:9201"
    restart: always
    depends_on:
      - nebula-graphd
      - nebula-metad
      - nebula-storaged
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:9201/metrics"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  cadvisor:
    image: ${REGISTRY:-hb.gs.lan}/gcr.io/cadvisor/cadvisor:${CADVISOR_VERSION:-v0.47.2}
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    ports:
      - "8080:8080"
    restart: always
    privileged: true
    devices:
      - /dev/kmsg:/dev/kmsg
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://localhost:8080/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
