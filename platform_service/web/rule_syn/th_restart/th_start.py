# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/15 10:53
# 文件名称 : th_start.py
# 开发工具 : PyCharm
import os,json,pymysql.cursors
import sys
base_json = {}
if len(sys.argv) == 3 or len(sys.argv) == 4  :
    pass
else:
    sys.exit(1)
task_id = sys.argv[1]
batch_id = sys.argv[2]
print("len(sys.argv)  ===== ", len(sys.argv) )
path = ""
if len(sys.argv)  == 4:
    path  = sys.argv[3]
print("path ====== ",path)
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
#os.system("pkill -9 thd")
#os.system("pkill -9 th_engine")

    #os.system("/opt/GeekSec/th/bin/thd.worker.restart_analyse  "+task_id )
if  task_id == "0"  or  task_id == "1":
   print("/opt/GeekSec/th/bin/thd.worker.restart  "+task_id )
   os.system("/opt/GeekSec/th/bin/thd.worker.restart  "+task_id )
else:
   print("cd /opt/GeekSec/web/rule_syn/th_restart/ &&  python3 thd_docker.py   "+task_id + " "+ batch_id + " "+path )
   os.system("cd /opt/GeekSec/web/rule_syn/th_restart/ &&  python3 thd_docker.py   "+task_id + " "+ batch_id  + " "+path)
    #os.system("cd /opt/GeekSec/th/bin/ && nohup ./thd >/dev/null &")
    #os.system("/etc/init.d/thdd restart")
##db.close()
