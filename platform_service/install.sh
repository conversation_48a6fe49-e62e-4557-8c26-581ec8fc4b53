#!/bin/bash
# 获取当前工作目录
current_dir=$PWD

# 创建所需目录
mkdir -p /opt/GeekSec/STL /opt/GeekSec/task /opt/GeekSec/lmdb_utils

# 清理旧配置并复制新配置
rm -rf /opt/GeekSec/pubconfig /opt/GeekSec/pubconfig_docker
cp -rf ${current_dir}/package/pubconfig /opt/GeekSec/
cp -rf ${current_dir}/package/pubconfig/thd_offline_conf /opt/GeekSec/task/
cp -f ${current_dir}/package/pubconfig/pcap_import_manager.py /opt/GeekSec/task/
cp -rf ${current_dir}/package/pubconfig_docker /opt/GeekSec/
cp -rf ${current_dir}/package/web /opt/GeekSec/
cp -rf ${current_dir}/package/cronjobs /opt/GeekSec/

# 解压并安装探针同步脚本
cd ${current_dir}/package
tar -zxvf PrivateRule_Install.cc71a3aec0bca3591cb216b1dee5ac9ba3c8fb0c.tar.gz
cd PrivateRule_Install
/bin/bash ./install.sh

# 创建 systemd 服务
create_systemd_service() {
    local service_name=$1
    local description=$2
    local type=$3
    local work_dir=$4
    local command=$5
    local script_path=$6

    # 根据服务类型设置重启策略
    local restart_policy=""
    if [ "$type" = "simple" ]; then
        restart_policy="Restart=always
RestartSec=5"
    fi

    # 设置环境变量
    local env_vars="PATH=/root/miniconda3/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin"

    # 创建 systemd 服务单元文件
    {
        echo "[Unit]"
        echo "Description=${description}"
        echo "After=network.target"
        echo
        echo "[Service]"
        echo "Type=${type}"
        # 如果 work_dir 不为空，则添加 WorkingDirectory 字段
        if [ -n "$work_dir" ]; then
            echo "WorkingDirectory=${work_dir}"
        fi
        echo "Environment=${env_vars}"
        echo "ExecStart=${command} ${script_path}"
        # 如果 restart_policy 不为空，则添加重启策略
        if [ -n "$restart_policy" ]; then
            echo "${restart_policy}"
        fi
        echo
        echo "[Install]"
        echo "WantedBy=multi-user.target"
    } | tee /etc/systemd/system/${service_name}.service

    systemctl daemon-reload
    systemctl enable ${service_name}
}

PYTHON3_PATH=/root/miniconda3/bin/python3
SH_PATH=/bin/sh

create_systemd_service create-kafka-dir "Create Kafka Data Directory" oneshot "" "/bin/mkdir -p" "/data/kafka"
create_systemd_service pcap-import-manager "PCAP Import Manager Service" simple "/opt/GeekSec/task" "${PYTHON3_PATH}" "/opt/GeekSec/task/pcap_import_manager.py"
create_systemd_service sysinfo-collector "System Information Collection Service" simple "/opt/GeekSec/web/sys_info" "${SH_PATH}" "/opt/GeekSec/web/sys_info/sys_run.sh"
create_systemd_service http-server "HTTP Server Service" simple "/opt/GeekSec/web/HttpServer" "${PYTHON3_PATH}" "/opt/GeekSec/web/HttpServer/HttpServer.py"
create_systemd_service pcap-browsing "PCAP File Browsing Service" simple "/opt/GeekSec/web/tools" "${PYTHON3_PATH}" "/opt/GeekSec/web/tools/pcap_file_browser.py"
create_systemd_service pcap-exporter "PCAP Exporting Service" simple "/opt/GeekSec/web/rule_syn" "${SH_PATH}" "/opt/GeekSec/web/rule_syn/download_pcap_run.sh"
create_systemd_service system-time "System Time Recording Service" oneshot "/opt/GeekSec/web/rule_syn" "${PYTHON3_PATH}" "/opt/GeekSec/web/rule_syn/system_time.py"

# 添加 NTA Infrastructure Services 服务
cat <<EOF | tee /etc/systemd/system/nta-infra.service
[Unit]
Description=NTA Infrastructure Services
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
WorkingDirectory=/opt/GeekSec/docker
ExecStart=/usr/bin/docker compose --profile infra up -d
ExecStop=/usr/bin/docker compose --profile infra down
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

# 添加 NTA App Services 服务
cat <<EOF | tee /etc/systemd/system/nta-app.service
[Unit]
Description=NTA App Services
After=docker.service
Requires=docker.service

[Service]
Type=oneshot
WorkingDirectory=/opt/GeekSec/docker
ExecStart=/usr/bin/docker compose --profile app up -d
ExecStop=/usr/bin/docker compose --profile app down
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload
systemctl enable nta-infra
systemctl enable nta-app

# 待添加 cronjobs
new_cron_jobs=$(cat <<EOF
*/1 * * * * /root/miniconda3/bin/python3 /opt/GeekSec/cronjobs/elasticsearch_meta_index_rebuilder.py
*/60 * * * * /bin/bash /opt/GeekSec/cronjobs/clean_fdisk.sh
*/1 * * * * /bin/bash /opt/GeekSec/cronjobs/rule_static.sh
*/60 * * * * /bin/bash /opt/GeekSec/cronjobs/full2rule.sh
* */3 * * * /bin/bash /opt/GeekSec/cronjobs/download_pcap.sh
*/120 * * * * /bin/bash /opt/GeekSec/cronjobs/ObjTarLevelDo.sh
EOF
)
# 获取当前的 cronjobs
current_cron_jobs=$(crontab -l 2>/dev/null)

# 逐行检查并添加新的 cron 任务
updated_cron_jobs="$current_cron_jobs"
while IFS= read -r new_job; do
    if ! echo "$current_cron_jobs" | grep -Fxq "$new_job"; then
        updated_cron_jobs="$updated_cron_jobs"$'\n'"$new_job"
    fi
done <<< "$new_cron_jobs"

# 更新 crontab
echo "$updated_cron_jobs" | crontab -

# 赋予 sys_run.sh 和 checkseq 执行权限
chmod +x /opt/GeekSec/web/sys_info/sys_run.sh
chmod +x /opt/GeekSec/web/sys_info/checkseq

# 复制 web 目录到指定目录
cp -rf  ${current_dir}/package/web /opt/GeekSec/web/rule_syn/fdisk/

# 同步脚本内容
cd /opt/GeekSec/web/rule_syn/ && python3 /opt/GeekSec/web/rule_syn/th_syn.py 0 100001

# 停止并重启 thd 服务
/opt/GeekSec/th/bin/thd.all.stop
/opt/GeekSec/th/bin/thd.all.restart
