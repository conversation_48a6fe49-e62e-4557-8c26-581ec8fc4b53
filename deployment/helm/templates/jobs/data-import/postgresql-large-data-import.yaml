{{- if .Values.infrastructure.postgresql.enabled }}
# PostgreSQL大文件数据导入Job（PVC挂载版本）
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-postgresql-large-data-import
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: postgresql-import
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "20"  # 在大文件初始化之后执行
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  backoffLimit: 3
  activeDeadlineSeconds: 7200  # 2小时超时
  template:
    metadata:
      labels:
        app: postgresql-large-data-import-job
        {{- include "nta.labels" . | nindent 8 }}
    spec:
      restartPolicy: OnFailure
      initContainers:
        - name: wait-for-postgresql-ready
          image: postgres:15-alpine
          command:
            - /bin/sh
            - -c
            - |
              # 等待PostgreSQL完全就绪
              until pg_isready -h {{ .Release.Name }}-postgresql-rw -p 5432 -U postgres; do
                echo "Waiting for PostgreSQL to be ready..."
                sleep 5
              done
              
              # 等待基础表结构创建完成
              until PGPASSWORD=$POSTGRES_PASSWORD psql -h {{ .Release.Name }}-postgresql-rw -p 5432 -U postgres -d nta_knowledge -c "SELECT 1 FROM information_schema.tables WHERE table_name = 'domain_knowledge';" 2>/dev/null | grep -q "1"; do
                echo "Waiting for base tables to be created..."
                sleep 10
              done
              
              echo "PostgreSQL is ready for chunked data import!"
          env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "postgresql" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "postgresql" "root" $) }}
      containers:
        - name: postgresql-large-file-import
          image: postgres:15-alpine
          command:
            - /bin/sh
            - -c
            - |
              set -e
              echo "开始PostgreSQL大文件数据导入（PVC挂载方式）..."
              
              # 连接配置
              export PGHOST="{{ .Release.Name }}-postgresql-rw"
              export PGPORT="5432"
              export PGUSER="postgres"
              export PGDATABASE="nta_knowledge"
              
              # 创建导入状态表
              psql -c "
              CREATE TABLE IF NOT EXISTS import_status (
                  table_name VARCHAR(255) PRIMARY KEY,
                  file_path VARCHAR(500),
                  file_size_mb DECIMAL(10,2),
                  total_rows INTEGER DEFAULT 0,
                  start_time TIMESTAMP,
                  end_time TIMESTAMP,
                  status VARCHAR(50) DEFAULT 'PENDING'
              );"

              # 定义要导入的大文件表
              declare -A LARGE_FILES
              LARGE_FILES[domain_knowledge]="/data/csv/large/domain_knowledge.csv"
              LARGE_FILES[tb_domain_alexa]="/data/csv/large/tb_domain_alexa.csv"
              LARGE_FILES[tb_threat_info]="/data/csv/large/tb_threat_info.csv"
              
              # 导入大文件数据
              for table_name in "${!LARGE_FILES[@]}"; do
                  file_path=${LARGE_FILES[$table_name]}

                  echo "开始导入表: $table_name (文件: $file_path)"

                  # 检查文件是否存在
                  if [ ! -f "$file_path" ]; then
                      echo "错误: 文件 $file_path 不存在!"
                      continue
                  fi

                  # 获取文件大小
                  file_size_mb=$(du -m "$file_path" | cut -f1)

                  # 记录导入开始
                  psql -c "
                  INSERT INTO import_status (table_name, file_path, file_size_mb, start_time, status)
                  VALUES ('$table_name', '$file_path', $file_size_mb, CURRENT_TIMESTAMP, 'IN_PROGRESS')
                  ON CONFLICT (table_name) DO UPDATE SET
                      file_path = '$file_path',
                      file_size_mb = $file_size_mb,
                      start_time = CURRENT_TIMESTAMP,
                      status = 'IN_PROGRESS',
                      total_rows = 0;"
                  
                  # 清空现有数据
                  psql -c "TRUNCATE TABLE $table_name CASCADE;"

                  # 直接导入大文件
                  echo "  开始导入文件: $file_path (大小: ${file_size_mb}MB)"

                  # 使用COPY命令直接导入大文件
                  start_time=$(date +%s)

                  # 执行导入
                  if psql -c "COPY $table_name FROM '$file_path' WITH (FORMAT csv, HEADER true, DELIMITER ',');" 2>&1 | tee /tmp/import_${table_name}.log; then
                      # 获取导入的行数
                      total_rows=$(psql -t -c "SELECT COUNT(*) FROM $table_name;" | tr -d ' ')
                      end_time=$(date +%s)
                      duration=$((end_time - start_time))

                      echo "  成功导入 $total_rows 行，耗时 ${duration} 秒"
                      import_status="COMPLETED"
                  else
                      echo "  导入失败，查看日志: /tmp/import_${table_name}.log"
                      total_rows=0
                      import_status="FAILED"
                  fi
                  
                  # 更新导入状态
                  psql -c "
                  UPDATE import_status
                  SET end_time = CURRENT_TIMESTAMP,
                      status = '$import_status',
                      total_rows = $total_rows
                  WHERE table_name = '$table_name';"

                  echo "  表 $table_name 导入完成，状态: $import_status，总计 $total_rows 行"
              done
              
              # 更新统计信息
              echo "更新表统计信息..."
              for table_name in "${!CHUNKED_TABLES[@]}"; do
                  psql -c "ANALYZE $table_name;"
                  echo "  已更新 $table_name 统计信息"
              done
              
              # 显示最终结果
              echo ""
              echo "=== 分片数据导入结果 ==="
              psql -c "
              SELECT 
                  table_name as \"表名\",
                  chunk_count as \"分片数\",
                  imported_chunks as \"已导入分片\",
                  total_rows as \"总记录数\",
                  status as \"状态\",
                  EXTRACT(EPOCH FROM (end_time - start_time)) as \"耗时(秒)\"
              FROM import_status 
              ORDER BY table_name;"
              
              # 验证数据完整性
              total_imported=0
              failed_tables=0
              
              for table_name in "${!CHUNKED_TABLES[@]}"; do
                  count=$(psql -t -c "SELECT COUNT(*) FROM $table_name;" | tr -d ' ')
                  if [ "$count" -eq "0" ]; then
                      echo "错误: 表 $table_name 没有数据!"
                      failed_tables=$((failed_tables + 1))
                  else
                      total_imported=$((total_imported + count))
                  fi
              done
              
              echo "总计导入记录数: $total_imported"
              
              if [ $failed_tables -gt 0 ]; then
                  echo "错误: $failed_tables 个表导入失败!"
                  exit 1
              fi
              
              echo "所有分片数据导入成功完成!"
          env:
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.credentialsSecretName" (dict "type" "postgresql" "root" $) }}
                  key: {{ include "nta.credentialsSecretKey" (dict "type" "postgresql" "root" $) }}
          volumeMounts:
            - name: large-data-volume
              mountPath: /data
              readOnly: true
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "2Gi"
              cpu: "1500m"
      volumes:
        - name: large-data-volume
          persistentVolumeClaim:
            claimName: {{ .Release.Name }}-postgresql-large-data
{{- end }}
