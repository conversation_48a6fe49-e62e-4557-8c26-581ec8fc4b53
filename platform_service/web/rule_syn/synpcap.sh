#!/bin/bash
#参数1为规则ID
name1=$1
#参数2为规则名称
name2=$2
#时间戳
#cur_sec_and_ns=`date '+%s-%N'`
#cur_sec=${cur_sec_and_ns%-*}
#mv /data/pcapfiles/${name1} /data/pcapfiles_his/${name1}_${name2}_${cur_sec}
#echo "synpcap.sh-----begintime : `date`" >> /opt/GeekSec/web/thdd.log
/etc/init.d/thdd stop
#echo "thdd stop : `date`" >> /opt/GeekSec/web/thdd.log
python3 /opt/GeekSec/web/rule_syn/synpcap.py $1
#echo "synpcap.sh-----endtime : `date`" >> /opt/GeekSec/web/thdd.log
