#!/bin/bash
while true;
do
    # web_server
    web_server_count=`ps -ef | grep api | grep -v grep|wc -l`
    if [ ${web_server_count} -lt 1 ];then
        /opt/GeekSec/web/web_server/start.sh
    else
        echo "running......web_server"
    fi
    # WebHandle
    WebHandle_count=`ps -ef | grep TH_WebServer.jar | grep -v grep|wc -l`
    if [ ${WebHandle_count} -lt 1 ];then
        /opt/GeekSec/web/WebHandle/bin/stop.sh
        cd /opt/GeekSec/web/WebHandle/bin/
        nohup ./start.sh >/dev/null &
    else
        echo "running......WebHandle"
    fi
    # sys_psutil.py
    sys_psutil_count=`ps -ef | grep sys_psutil.py | grep -v grep|wc -l`
    if [ ${sys_psutil_count} -lt 1 ];then
        cd /opt/GeekSec/STL/
        python3 sys_psutil.py >/dev/null &
    else
        echo "running......sys_psutil.py"
    fi
    # pushMsg2Socket.py
    pushMsg2Socket_count=`ps -ef | grep pushMsg2Socket.py | grep -v grep|wc -l`
    if [ ${pushMsg2Socket_count} -lt 1 ];then
        cd /opt/GeekSec/STL/pushMsgNew/
        python3 pushMsg2Socket.py >/dev/null &
    else
        echo "running......pushMsg2Socket.py"
    fi
    sleep 3
done
