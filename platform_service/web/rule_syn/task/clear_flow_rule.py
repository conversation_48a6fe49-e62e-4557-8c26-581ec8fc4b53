#!/usr/bin/python
# -*- coding: UTF-8 -*-
import os

os.system("kill -9 `ps  -eaf | grep thd | grep -v grep | awk '{print $2}'`")
os.system("kill -9 `ps  -eaf | grep th_engine | grep -v grep | awk '{print $2}'`")

import pymysql.cursors
passwd = "root"
if os.path.exists("/opt/GeekSec/pubconfig/.mysql"):
    with open("/opt/GeekSec/pubconfig/.mysql"):
        passwd = f.read()
db = pymysql.connect(host='localhost',port=3306,user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
cursor.execute("delete from tb_rule;")
db.commit()
os.system("echo \"clear_flow_rule --> delete from tb_rule\">>log.log")
cursor.execute("delete from tb_lib_config;")
db.commit()
os.system("echo \"clear_flow_rule --> delete from tb_lib_config\">>log.log")
cursor.execute("delete from roule_id_total_statistic;")
db.commit()
os.system("echo \"clear_flow_rule --> delete from roule_id_total_statistic\">>log.log")
db.close()

os.system("cd /opt/GeekSec/th/bin/ && nohup ./thd >/dev/null &")
