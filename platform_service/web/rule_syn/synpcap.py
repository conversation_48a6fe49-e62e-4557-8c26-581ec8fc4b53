# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : synpcap.py
# 开发工具 : PyCharm
import sys, pymysql.cursors, json, time, os
saved_file = "/opt/GeekSec/th/bin/rule_statistics.map"

def removeSavedRuleBytes(rule_id):
    rule_id = str(rule_id)+":"
    outlines = []
    for line in open(saved_file).readlines():
        if not line.startswith(rule_id):
            outlines.append(line)
    outf = open(saved_file,"w+")
    outf.writelines(outlines)

# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    #print(sql)
    cur.execute(sql)
    x_db.commit()
def is_exists(path_file):
    return os.path.exists(path_file)
def get_dir(cmd,arr):
    p = os.popen(cmd)
    lines = p.readlines()
    for line in lines:
        arr.append(line.strip('\n'))
    return arr
if not is_exists("/data/pcapfiles_his/"):
    os.makedirs("/data/pcapfiles_his/")
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
time_s = str(time.time()).split('.')[0]
p_ruleId = sys.argv[1]
p_batch = ""
if len(sys.argv) == 3:
    p_batch = sys.argv[2]
for i in os.listdir("/data/pcapfiles/"): 
    temp_dir = os.path.join("/data/pcapfiles/", i)
    print("************** ",temp_dir)
    if os.path.isdir(temp_dir) or os.path.islink(temp_dir):
      if p_batch == "":
           for ii in os.listdir(temp_dir):
               p_batch =  ii  
               t_path = temp_dir+"/"+p_batch+"/"+p_ruleId
               if os.path.exists(t_path+"") == True:
                  os.system("rm -rf " + t_path)
      else:
         t_path = temp_dir+"/"+p_batch+"/"+p_ruleId
         if os.path.exists(t_path+"") == True:
              os.system("rm -rf " + t_path)
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
db = pymysql.connect(host=base_json['tidb_host'],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
#idu_mysql("delete from tb_rule_statistic_info where rule_id = " + p_ruleId + ";", cursor, db)
idu_mysql("update tb_rule set total_sum_bytes = 0 where rule_id = " + p_ruleId + ";", cursor, db)
idu_mysql("update tb_model set total_sum_bytes = 0 where model_id = " + p_ruleId + ";", cursor, db)
#idu_mysql("delete from tb_alarm where tag_id = " + p_ruleId + ";", cursor, db)
db.close()

