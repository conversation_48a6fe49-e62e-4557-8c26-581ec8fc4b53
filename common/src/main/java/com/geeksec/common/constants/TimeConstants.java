package com.geeksec.common.constants;

/**
 * 时间相关常量类
 * 包括时区、时间格式等常量
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
public final class TimeConstants {

    private TimeConstants() {
        // 工具类，禁止实例化
    }

    /**
     * 时区常量
     */
    public static final class TimeZone {
        /** 默认时区 */
        public static final String DEFAULT_TIMEZONE = "Asia/Shanghai";
        /** UTC时区 */
        public static final String UTC = "UTC";
        /** GMT时区 */
        public static final String GMT = "GMT";

        private TimeZone() {}
    }

    /**
     * 时间格式常量
     */
    public static final class DateFormat {
        /** 标准日期时间格式 */
        public static final String DATETIME = "yyyy-MM-dd HH:mm:ss";
        /** 日期格式 */
        public static final String DATE = "yyyy-MM-dd";
        /** 时间格式 */
        public static final String TIME = "HH:mm:ss";
        /** 时间戳格式 */
        public static final String TIMESTAMP = "yyyy-MM-dd HH:mm:ss.SSS";
        /** ISO 8601格式 */
        public static final String ISO_8601 = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";

        private DateFormat() {}
    }
}
