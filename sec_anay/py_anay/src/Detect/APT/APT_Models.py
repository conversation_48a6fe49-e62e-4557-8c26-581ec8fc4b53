import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.GkAnayTaskHelper import G_AnayModel
from Detect.APT.APT29.detect_WellMess import detectHttpWellMess
from Detect.APT.APT28.detect_28_Zebrocy import detect_Zebrocy
from Detect.APT.APT29.detect_HTTPS_WellMess import detectWellMessCerts
from Detect.APT.APT_Patchwork.DetectPatchwork import detectPatchworkCert


class APT28_DetectTask(G_AnayModel):
     def run(self,task_id,batch_id):
        detect_Zebrocy(task_id,batch_id)


class Patchwork_DetectTask(G_AnayModel):
     def run(self,task_id,batch_id):
        detectPatchworkCert(task_id,batch_id)

class APT29_DetectTask(G_AnayModel):
     def run(self,task_id,batch_id):
        detectHttpWellMess(task_id,batch_id)
        detectWellMessCerts(task_id,batch_id)


if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    detectHttpWellMess(task_id,batch_id)
    detect_Zebrocy(task_id,batch_id)
    detectWellMessCerts(task_id,batch_id)
    detectPatchworkCert(task_id,batch_id)