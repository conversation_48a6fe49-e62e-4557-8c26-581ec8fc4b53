# NTA 3.0 告警处理建议恢复步骤迁移文档

## 概述

本文档记录了从NTA 2.0到NTA 3.0告警处理建议中"恢复步骤"字段的迁移工作。

## 问题背景

在NTA 2.0的`/Volumes/repository/geeksec/nta_2.0/etl/pb-to-alarm`模块中，告警数据包含`alarm_handle_method`字段，该字段提供了详细的处理建议，包括恢复步骤。例如：

```
"首先断开受感染设备的网络连接，防止恶意行为扩散；然后使用可靠的安全软件进行全面扫描和清除；及时更新操作系统和应用程序到最新版本，修补安全漏洞；加强账户密码，避免使用弱密码；备份重要数据，以防万一；最后，提高对可疑邮件和链接的警觉性，避免点击来源不明的链接或下载不明文件。"
```

在NTA 3.0中，告警处理建议被重构为结构化的`HandlingSuggestions`类，包含：
- `immediateActions` (立即行动)
- `investigationSteps` (调查步骤)  
- `preventionMeasures` (预防措施)
- `priority` (优先级)

但缺少了"恢复步骤"相关的字段。

## 解决方案

### 1. 数据模型修改

在`HandlingSuggestions`类中添加了`recoverySteps`字段：

```java
@JsonProperty("recovery_steps")
private List<String> recoverySteps;
```

### 2. 修改的文件列表

#### 核心模型类
- `flink-jobs/alarm-processor/src/main/java/com/geeksec/alarm/processor/model/Alarm.java`
- `flink-jobs/alarm-notification/src/main/java/com/geeksec/alarm/notification/model/Alarm.java`

#### 告警处理器实现类
- `DefaultAlarmProcessor.java` - 默认告警处理器
- `StandardRemoteControlC2Processor.java` - 标准远程控制C2行为处理器
- `RemoteTrojanProcessor.java` - 远控木马处理器
- `MiningConnectionProcessor.java` - 挖矿连接处理器
- `CovertTunnelProcessor.java` - 隐蔽隧道处理器
- `PenetrationToolProcessor.java` - 渗透工具处理器
- `UnknownRemoteControlProtocolProcessor.java` - 未知远程控制协议处理器
- `CertificateAnomalyProcessor.java` - 证书异常处理器
- `DNSTunnelProcessor.java` - DNS隧道处理器
- `IllegalExternalConnectionProcessor.java` - 违规外联处理器
- `MiningVirusProcessor.java` - 挖矿病毒处理器
- `ScanBehaviorProcessor.java` - 扫描行为处理器
- `WebShellProcessor.java` - WebShell处理器

#### 格式化功能类
- `FormattingFunction.java` - 告警格式化功能

### 3. 恢复步骤示例

不同类型的告警包含了针对性的恢复步骤：

#### 远程控制C2行为
```java
.recoverySteps(Arrays.asList(
    "断开受感染设备的网络连接，防止恶意行为扩散",
    "使用可靠的安全软件进行全面扫描和清除",
    "及时更新操作系统和应用程序到最新版本，修补安全漏洞",
    "加强账户密码，避免使用弱密码",
    "备份重要数据，以防万一",
    "提高对可疑邮件和链接的警觉性，避免点击来源不明的链接或下载不明文件"
))
```

#### 挖矿病毒
```java
.recoverySteps(Arrays.asList(
    "彻底清除挖矿程序和相关恶意文件",
    "恢复系统正常性能和资源使用",
    "重新安装或修复被破坏的系统组件",
    "更新系统补丁和安全软件",
    "恢复正常的网络连接和服务",
    "监控系统确保挖矿程序完全清除"
))
```

#### WebShell
```java
.recoverySteps(Arrays.asList(
    "彻底清除WebShell文件和相关恶意代码",
    "修复被WebShell利用的Web应用漏洞",
    "重新部署干净的Web应用代码",
    "恢复被篡改的系统文件和配置",
    "重新设置Web服务器和应用的安全配置",
    "监控Web应用确保WebShell完全清除"
))
```

## 测试验证

创建了测试文件验证修改的正确性：
- `AlarmHandlingSuggestionsTest.java` - 完整的单元测试
- `HandlingSuggestionsCompileTest.java` - 编译验证测试

## JSON序列化

恢复步骤字段在JSON中的表示：
```json
{
  "handling_suggestions": {
    "immediate_actions": ["立即行动1", "立即行动2"],
    "investigation_steps": ["调查步骤1", "调查步骤2"],
    "prevention_measures": ["预防措施1", "预防措施2"],
    "recovery_steps": ["恢复步骤1", "恢复步骤2"],
    "priority": "高"
  }
}
```

## 兼容性说明

- 新增的`recovery_steps`字段是可选的，不会影响现有的告警数据
- 使用`@JsonIgnoreProperties(ignoreUnknown = true)`确保向后兼容
- 所有现有的告警处理器都已更新以包含恢复步骤

## 总结

通过添加`recoverySteps`字段，NTA 3.0现在完全兼容NTA 2.0中的告警处理建议功能，确保了从NTA 2.0迁移过来的告警数据能够保持完整的处理建议信息，包括重要的恢复步骤。
