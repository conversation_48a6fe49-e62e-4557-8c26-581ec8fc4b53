# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : query_size.py
# 开发工具 : PyCharm
import os
import  os,pymysql,json
base_json = {}
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def get_size(path):
    cmd = "du -sL " + path + " | awk '{print $1}'"
    p = os.popen(cmd)
    list1.append(int(p.read())*1024)
    p.close()
def get_rule_size():
    sql = "select sum(total_sum_bytes) as s from tb_rule";
    res =  s_mysql(sql , cursor)
    if len(res ) > 0 :
        return res[0]["s"]
    else:
        return 0

def get_filter_size():
    db1 = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='push_database',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    cursor1 = db1.cursor()
    sql = "select sum(bps_filter_drop) as s from  tb_system_fifter";
    res =  s_mysql(sql , cursor1)
    if len(res ) > 0 :
        return res[0]["s"]
    else:
        return 0

list1 = []
get_size('/data/pcapfiles/')
#get_size('/data/pcapfiles_his/')
pcap_size = ('%d' % sum(list1))
# if pcap_size == '4096':
#     pcap_size = '0'
list1 = []
get_size('/data/pbfiles/')
get_size('/data/cerfiles/')
log_size=('%d' % sum(list1))
# if log_size == '8192':
#     log_size = '0'
list1 = []
flow_rule_size=get_rule_size()
list1 = []
get_size('/opt/GeekSec/th/bin/plugin/msg_handle/user/')
log_rule_size= get_filter_size()
print('{"pcap":' + pcap_size  + ',"log":' + log_size  + ',"flow_rule":' + str(flow_rule_size)  + ',"log_rule":' + str(log_rule_size) + '}')
