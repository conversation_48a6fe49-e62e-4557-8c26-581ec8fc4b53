package com.geeksec.alarm.processor.pipeline.function;

import org.apache.flink.api.common.functions.FilterFunction;
import org.apache.flink.metrics.Counter;
import org.apache.flink.metrics.MetricGroup;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.suppression.AlarmSuppressionClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 告警抑制规则过滤函数
 * 用于过滤掉满足抑制规则的告警
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
public class SuppressionFilterFunction implements FilterFunction<Alarm> {

    private final AlarmSuppressionClient suppressionClient;

    // 监控指标
    private transient Counter totalAlarms;
    private transient Counter filteredAlarms;
    private transient Counter suppressionHits;
    private transient Counter suppressionMisses;

    public SuppressionFilterFunction(AlarmProcessorConfig config) {
        // 从配置创建抑制规则客户端
        this.suppressionClient = new AlarmSuppressionClient(config);
    }

    /**
     * 初始化监控指标
     */
    public void initMetrics(MetricGroup metricGroup) {
        this.totalAlarms = metricGroup.counter("total_alarms");
        this.filteredAlarms = metricGroup.counter("filtered_alarms");
        this.suppressionHits = metricGroup.counter("suppression_hits");
        this.suppressionMisses = metricGroup.counter("suppression_misses");
    }

    @Override
    public boolean filter(Alarm alarm) throws Exception {
        totalAlarms.inc();

        // 如果抑制规则客户端未初始化，直接通过
        if (suppressionClient == null) {
            log.warn("抑制规则客户端未初始化，告警直接通过: alarmId={}", alarm.getAlarmId());
            return true;
        }

        try {
            // 检查告警是否满足抑制规则
            boolean shouldSuppress = suppressionClient.shouldSuppress(alarm);

            if (shouldSuppress) {
                suppressionHits.inc();
                filteredAlarms.inc();
                log.debug("告警满足抑制规则，已过滤: victim={}, attacker={}, alarmType={}",
                        alarm.getDstIp(), alarm.getSrcIp(), alarm.getAlarmType());
                return false; // 过滤掉
            } else {
                suppressionMisses.inc();
                log.debug("告警不满足抑制规则，继续处理: victim={}, attacker={}, alarmType={}",
                        alarm.getDstIp(), alarm.getSrcIp(), alarm.getAlarmType());
                return true; // 继续处理
            }

        } catch (Exception e) {
            log.error("抑制规则检查失败，告警直接通过: victim={}, attacker={}, alarmType={}",
                    alarm.getDstIp(), alarm.getSrcIp(), alarm.getAlarmType(), e);
            return true; // 出错时直接通过，避免丢失告警
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (suppressionClient != null) {
            suppressionClient.close();
            log.info("抑制规则过滤函数已关闭");
        }
    }
}