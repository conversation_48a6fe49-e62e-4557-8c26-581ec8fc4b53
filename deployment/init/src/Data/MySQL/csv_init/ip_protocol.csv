"pro_id","protocol_type","protocol_remark"
"0","HOPOPT","IPv6逐跳选项"
"1","ICMP","网际控制报文协议"
"10","BBN-RCC-MON","BBN RCC 监控"
"100","GMTP","GMTP"
"101","IFMP","Ipsilon流量管理协议"
"102","PNNI","IP上的PNNI"
"103","PIM","独立于协议的多播"
"104","ARIS","ARIS"
"105","SCPS","SCPS"
"106","QNX","QNX"
"107","A/N","活动网络"
"108","IPComp","IP负载压缩协议"
"109","SNP","Sitara网络协议"
"11","NVP-II","网络语音协议II"
"110","Compaq-Peer","Compaq对等协议"
"111","IPX-in-IP","IP中的IPX"
"112","VRRP","虚拟路由冗余协议"
"113","PGM","PGM可靠传输协议"
"114","","任意0跳协议"
"115","L2TP","第二层隧道协议"
"116","DDX","D-II 数据交换"
"117","IATP","交互式代理传输协议"
"118","STP","计划传输协议"
"119","SRP","SpectraLink无线协议"
"12","PUP","PUP"
"120","UTI","UTI"
"121","SMP","简单消息协议"
"122","SM (deprecated)","简单组播协议"
"123","PTP","性能透明协议"
"124","ISIS over IPv4","ISIS over IPv4"
"125","FIRE","FIRE"
"126","CRTP","Combat无线传输协议"
"127","CRUDP","Combat无线用户数据报"
"128","SSCOPMCE","SSCOPMCE"
"129","IPLT","IPLT"
"13","ARGUS (deprecated)","ARGUS"
"130","SPS","安全数据包防护"
"131","PIPE","IP中的专用IP封装"
"132","SCTP","流控制传输协议"
"133","FC","光纤通道"
"134","RSVP-E2E-IGNORE","RSVP-E2E-IGNORE"
"135","RSVP-E2E-IGNORE","RSVP-E2E-IGNORE"
"136","UDPLite","UDPLite"
"137","MPLS-in-IP","MPLS-in-IP"
"138","manet","MANET协议"
"139","HIP","主机标识协议"
"14","EMCON","EMCON"
"140","Shim6","Shim6 Protocol"
"141","WESP","封装安全载荷协议"
"142","ROHC","鲁棒性头部压缩"
"143","Ethernet","Ethernet"
"144","AGGFRAG","ESP AGGFRAG封装负载"
"145","NSH","网络服务头"
"146","Homa","Homa"
"15","XNET","交叉网络/跨网调试器"
"16","CHAOS","CHAOS"
"17","UDP","用户数据报协议"
"18","MUX","多路复用协议"
"19","DCN-MEAS","DCN测量子系统"
"2","IGMP","网际组管理协议"
"20","HMP","主机监控协议"
"21","PRM","数据包无线测量"
"22","XNS-IDP","XEROX NS IDP"
"23","TRUNK-1","第1主干"
"24","TRUNK-2","第2主干"
"25","LEAF-1","第1叶"
"26","LEAF-2","第2叶"
"27","RDP","可靠数据报文协议"
"28","IRTP","因特网分组传输协议"
"29","ISO-TP4","ISO传输协议第4类"
"3","GGP","网关至网关协议"
"30","NETBLT","批量数据传输协议"
"31","MFE-NSP","MFE网络服务协议"
"32","MERIT-INP","MERIT节点间协议"
"33","DCCP","数据拥塞控制协议"
"34","3PC","第三方连接协议"
"35","IDPR","域间策略路由协议"
"36","XTP","XTP"
"37","DDP","数据报传送协议"
"38","IDPR-CMTP","IDPR控制消息传输协议"
"39","TP++","TP++传输协议"
"4","IPv4","IPv4封装"
"40","IL","IL传输协议"
"41","IPv6","IPv6封装"
"42","SDRP","源要求路由协议"
"43","IPv6-Route","IPv6的路由标头"
"44","IPv6-Frag","IPv6的片段标头"
"45","IDRP","域间路由协议"
"46","RSVP","保留协议"
"47","GRE","通用路由封装协议"
"48","DSR","动态源路由协议"
"49","BNA","BNA"
"5","ST","信元传输协议"
"50","ESP","封装安全载荷"
"51","AH","认证头"
"52","I-NLSP","集成网络层安性TUBA"
"53","SWIPE (deprecated)","加密IP"
"54","NARP","NBMA地址解析协议"
"55","Min-IPv4","最小IPv4"
"56","TLSP","传输层安全协议"
"57","SKIP","SKIP"
"58","IPv6-ICMP","IPv6互联网控制报文协议"
"59","IPv6-NoNxt","IPv6无下一个标头"
"6","TCP","传输控制协议"
"60","IPv6-Opts","IPv6目标选项"
"61","","任意主机内部协议"
"62","CFTP","CFTP"
"63","","任意本地网络协议"
"64","SAT-EXPAK","SATNET与后台EXPAK"
"65","KRYPTOLAN","Kryptolan"
"66","RVD","MIT远程虚拟磁盘协议"
"67","IPPC","Internet Pluribus数据包核心"
"68","","任意分布式文件系统"
"69","SAT-MON","SATNET监视"
"7","CBT","基于核心树的多播协议"
"70","VISA","VISA协议"
"71","IPCV","Internet数据包核心工具"
"72","CPNX","计算机协议网络管理"
"73","CPHB","计算机协议检测信号"
"74","WSN","王安电脑网络"
"75","PVP","数据包视频协议"
"76","BR-SAT-MON","后台SATNET监控"
"77","SUN-ND","SUN ND PROTOCOL-Temporary"
"78","WB-MON","WIDEBAND监控"
"79","WB-EXPAK","WIDEBAND EXPAK"
"8","EGP","外部网关协议"
"80","ISO-IP","ISO互联网协议"
"81","VMTP","VMTP"
"82","SECURE-VMTP","SECURE-VMTP"
"83","VINES","VINES"
"84","IPTM","管理互联网协议流量协议"
"85","NSFNET-IGP","NSFNET-IGP"
"86","DGP","异类网关协议"
"87","TCF","TCF"
"88","EIGRP","加强型网关间选径协议"
"89","OSPFIGP","OSPFIGP"
"9","IGP","内部网关协议"
"90","Sprite-RPC","Sprite RPC协议"
"91","LARP","轨迹地址解析协议"
"92","MTP","多播传输协议"
"93","AX.25","AX.25帧"
"94","IPIP","IP中的IP封装协议"
"95","MICP (deprecated)","移动互联控制协议"
"96","SCC-SP","信号通讯安全协议"
"97","ETHERIP","IP中的以太网封装"
"98","ENCAP","封装头"
"99","","任意专用加密方案"
