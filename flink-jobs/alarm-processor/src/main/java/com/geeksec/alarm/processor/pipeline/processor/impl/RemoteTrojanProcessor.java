package com.geeksec.alarm.processor.pipeline.processor.impl;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.alarm.processor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 远控木马告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class RemoteTrojanProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.REMOTE_TROJAN;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        String trojanFamily = getTrojanFamily(alarm);
        String c2Server = alarm.getDstIp();
        
        // 远控通信检测
        reasons.add(createDetectionReason(
                "远控木马通信",
                String.format("检测到主机与%s远控木马C&C服务器的通信", trojanFamily),
                "正常网络通信",
                String.format("C&C服务器: %s", c2Server),
                "正常业务服务器通信",
                9
        ));
        
        // 通信模式分析
        String communicationPattern = getCommunicationPattern(alarm);
        if (communicationPattern != null) {
            reasons.add(createDetectionReason(
                    "异常通信模式",
                    "检测到符合远控木马特征的通信模式",
                    "正常通信模式",
                    communicationPattern,
                    "规律的业务通信",
                    8
            ));
        }
        
        // 加密通信检测
        if (isEncryptedCommunication(alarm)) {
            reasons.add(createDetectionReason(
                    "加密C&C通信",
                    "检测到使用加密协议的C&C通信",
                    "明文或标准加密通信",
                    "自定义加密协议",
                    "标准TLS/SSL通信",
                    7
            ));
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        String trojanFamily = getTrojanFamily(alarm);
        
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        "立即隔离受感染主机，阻断网络连接",
                        String.format("阻断与C&C服务器%s的所有通信", alarm.getDstIp()),
                        "备份受感染主机的关键数据和日志",
                        "启动应急响应流程"
                ))
                .investigationSteps(Arrays.asList(
                        String.format("分析%s木马的具体变种和功能", trojanFamily),
                        "确认木马的感染时间和传播路径",
                        "检查是否有数据泄露或系统破坏",
                        "分析攻击者的后续行为和意图",
                        "确认网络中其他主机的感染情况"
                ))
                .preventionMeasures(Arrays.asList(
                        "更新防病毒软件和恶意软件检测规则",
                        "加强终端安全防护和行为监控",
                        "实施网络分段，限制横向移动",
                        "加强邮件和下载文件的安全检查",
                        "定期进行安全意识培训"
                ))
                .recoverySteps(Arrays.asList(
                        "使用专业的恶意软件清除工具彻底清理感染",
                        "重新安装操作系统和应用程序（如果感染严重）",
                        "从干净的备份恢复重要数据",
                        "更改所有相关账户的密码",
                        "重新配置网络安全策略",
                        "监控系统一段时间确保完全清除"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        String trojanFamily = getTrojanFamily(alarm);
        return String.format("攻击者通过%s远控木马控制受感染主机，建立持久化的C&C通信通道，" +
                "可以远程执行命令、窃取数据、下载其他恶意软件等。", trojanFamily);
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "通过分析网络流量中的C&C通信特征、通信频率、数据包大小等模式，" +
                "结合已知的木马家族指纹库，识别远控木马的通信行为。";
    }
    
    @Override
    public List<Map<String, String>> getVictimInfo(Alarm alarm) {
        List<Map<String, String>> victims = new ArrayList<>();
        
        // 远控木马中，源IP是受害者
        if (alarm.getSrcIp() != null) {
            Map<String, String> victim = new HashMap<>();
            victim.put("ip", alarm.getSrcIp());
            victim.put("role", "受感染主机");
            victim.put("infection_level", "严重");
            victims.add(victim);
        }
        
        return victims;
    }
    
    @Override
    public List<Map<String, String>> getAttackerInfo(Alarm alarm) {
        List<Map<String, String>> attackers = new ArrayList<>();
        
        // C&C服务器作为攻击者
        if (alarm.getDstIp() != null) {
            Map<String, String> attacker = new HashMap<>();
            attacker.put("ip", alarm.getDstIp());
            attacker.put("role", "C&C服务器");
            attacker.put("threat_level", "高");
            attackers.add(attacker);
        }
        
        return attackers;
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99005";
    }
    
    @Override
    public boolean requiresSpecialHandling(Alarm alarm) {
        return true; // 远控木马需要特殊处理
    }
    
    /**
     * 获取木马家族名称
     */
    private String getTrojanFamily(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object family = alarm.getExtendedProperties().get("trojan_family");
            if (family != null) {
                return family.toString();
            }
        }
        
        if (alarm.getThreatType() != null) {
            return alarm.getThreatType();
        }
        
        return "未知远控木马";
    }
    
    /**
     * 获取通信模式
     */
    private String getCommunicationPattern(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object pattern = alarm.getExtendedProperties().get("communication_pattern");
            if (pattern != null) {
                return pattern.toString();
            }
        }
        return null;
    }
    
    /**
     * 检查是否为加密通信
     */
    private boolean isEncryptedCommunication(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object encrypted = alarm.getExtendedProperties().get("is_encrypted");
            if (encrypted instanceof Boolean) {
                return (Boolean) encrypted;
            }
        }
        return false;
    }
}
