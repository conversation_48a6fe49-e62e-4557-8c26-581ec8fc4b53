##
# @file check_probe.py
# @brief  : 探针状态检查
# <AUTHOR>
# @version 0.1.00
# @date 2021-06-28

#### 
import sys,os,time
import psutil
########## 
def cmd(cmdstr):
    arr = []
    p = os.popen(cmdstr)
    lines = p.readlines()
    for line in lines:
         arr.append(line.strip('\n'))
    return arr
def check():
    fdisk=psutil.disk_usage('/var/ftp/')
    if fdisk.percent > 98.0:
        print("数据盘可用低  请检查数据盘数据 及 磁盘清理 ")
    else:
        print("数据盘正常 ")

    fdisk_root=psutil.disk_usage('/')
    if fdisk.percent > 98.0:
        print("系统盘可用低  请检查系统盘盘数据 及 磁盘清理 ")
    else:
        print("系统盘正常 ")
    if os.path.islink("/data") == False:  ### 数据盘是否为软连接
        print("/data 未做软连接 , 数据可能写入了系统盘")
    else:
        print("/data 目录软连接正常 ")

        
# 查询mysql  表空间 

### 检查MS 和 LogtoOracle 连接是否正常
    cmdstr = " netstat -anp| grep 8787 | grep  LogInfo | wc | awk '{print $1}'"
    res = cmd(cmdstr)
    if len(res) >0  and int(res[0]) == 1:
        print("LogInfoToOra 和 ms_server 连接正常")
    else:
        print("LogInfoToOra 和 ms_server 连接不正常")
#  ### 检测 MS 是否正常启动
    cmdstr = "ps -eaf | grep ms_server | grep -v  grep | wc | awk '{print $1}'" 
    res = cmd(cmdstr)
    if len(res) >0  and int(res[0]) == 3:
        print("ms_server 正常")
    else:
        print("ms_server 不正常, 未启动")
    
## 检测授权是否到期 
    if os.path.exists("/etc/.serialnumber")== False:
        print("系统未授权 ")
    cmdstr = "export LD_LIBRARY_PATH=/opt/GeekSec/th/bin/:/opt/GeekSec/th/sdk/lib/ && export THE_ROOT=/opt/GeekSec/th/ && export PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/bin && /opt/GeekSec/STL/checkseq"
    ###
    res = cmd(cmdstr)
### 检测小数据 或者 单边数据量 大小  数据包总量比较xia
    if os.path.exists(".serialnumber_date.txt"):
        with open("./.serialnumber_date.txt", "r") as f:  # 打开文件
            data = f.read()  # 读取文件
            date = data.replace("\n","",10)
            if int(date) < int(time.time()):
                print("产品授权到期")
            else:
                print("产品授权正常")
    else:
        print("产品授权异常")

check()
