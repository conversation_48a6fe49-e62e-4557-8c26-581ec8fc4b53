{{- if .Values.services.auth-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    # 环境特定配置 - 这些配置会覆盖JAR包内的默认配置

    # 服务器配置
    server:
      port: 8081
      servlet:
        context-path: /auth

    # 应用名称
    spring:
      application:
        name: auth-service

      # 数据库连接配置
      datasource:
        url: jdbc:postgresql://{{ include "nta.postgresqlConfig" . | fromYaml | get "host" }}:{{ include "nta.postgresqlConfig" . | fromYaml | get "port" }}/{{ .Values.services.auth-service.database }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
        driver-class-name: org.postgresql.Driver

      # Redis配置
      redis:
        host: {{ include "nta.redisConfig" . | fromYaml | get "host" }}
        port: {{ include "nta.redisConfig" . | fromYaml | get "port" }}
        database: 0
        timeout: 10000

    # MyBatis-Flex配置
    mybatis-flex:
      type-aliases-package: com.geeksec.auth.entity
      configuration:
        map-underscore-to-camel-case: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
      global-config:
        logic-delete-column: deleted
        version-column: version

    # Sa-Token配置
    sa-token:
      token-name: Authorization
      timeout: 7200
      active-timeout: -1
      is-concurrent: true
      is-share: true
      token-style: uuid
      is-log: false

    # 生产环境特定配置
    knife4j:
      production: {{ eq .Values.global.springProfiles "prod" }}

    # 服务配置
    services:
      graph-service:
        url: http://graph-service:8083
{{- end -}}
