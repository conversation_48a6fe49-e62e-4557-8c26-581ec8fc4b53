# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : rule_syn.py
# 开发工具 : PyCharm
import os,json,pymysql.cursors
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()

def write_file(filename , context):
    os.system("echo \"\">"+filename)
    fnew = open(filename, "w")
    fnew.write(context)
    fnew.close()
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='localhost',port=base_json["db_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
context=""
context_json=""
results = s_mysql("select rule_id,rule_level,rule_name,rule_desc,rule_size,capture_mode,rule_json,created_time,updated_time,rule_state from tb_rule;",cursor)
for row in results:
    fname =row['rule_json']
    context=context + "\n\n"+fname
    ruletagjson = {}
    ruletagjson["RuleId"] = row['rule_id']
    ruletagjson["rule_level"] = row['rule_level']
    ruletagjson["RuleName"] = str(row['rule_name'])
    ruletagjson["Tag"] = []
    ruletagjson["Infor"] = str(row['rule_desc'])
    ruletagjson["rule_size"] = row['rule_size']
    ruletagjson["capture_mode"] = row['capture_mode']
    ruletagjson["created_time"] = str(row['created_time'])
    ruletagjson["updated_time"] = str(row['updated_time'])
    if row["rule_state"]=="生效":
        context_json = context_json + "\n\n" + json.dumps(ruletagjson)
os.system("echo \"\">rule.json")
write_file("rule.json",context)
os.system("echo \"\">ruletag.json")
write_file("ruletag.json",context_json)
# 数据同步
isExists = os.path.exists("/opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/")
if not isExists:
    os.makedirs("/opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/")
os.system ("cp -rf rule.json /opt/GeekSec/th/bin/JsonRule/BasicRule/UserRule/rule.json")
isExists_rule = os.path.exists("/opt/GeekSec/STL/ExportData/bin/script/rule_parse.py")
if isExists_rule:
    os.system("python3 /opt/GeekSec/STL/ExportData/bin/script/rule_parse.py /opt/GeekSec/web/rule_syn/rule.json >/dev/null")
#idu_mysql("delete from roule_id_total_statistic where rule_id not in (select rule_id from tb_rule);",cursor)
#idu_mysql("delete from tb_alarm where tag_id not in (select rule_id from tb_rule_info);",cursor)
db.close()

