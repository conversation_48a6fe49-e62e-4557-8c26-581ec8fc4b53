-- PostgreSQL CDC 设置脚本
-- 为告警数据同步创建复制槽和必要的配置

-- 检查并启用逻辑复制
-- 注意：这需要在postgresql.conf中设置 wal_level = logical

-- 创建告警CDC复制槽
-- 如果复制槽已存在，则跳过创建
DO $$
BEGIN
    -- 检查复制槽是否已存在
    IF NOT EXISTS (
        SELECT 1 FROM pg_replication_slots
        WHERE slot_name = 'alarm_cdc_slot'
    ) THEN
        -- 创建逻辑复制槽
        PERFORM pg_create_logical_replication_slot('alarm_cdc_slot', 'pgoutput');
        RAISE NOTICE '已创建复制槽: alarm_cdc_slot';
    ELSE
        RAISE NOTICE '复制槽 alarm_cdc_slot 已存在，跳过创建';
    END IF;
END $$;

-- 创建会话标签CDC复制槽
-- 如果复制槽已存在，则跳过创建
DO $$
BEGIN
    -- 检查复制槽是否已存在
    IF NOT EXISTS (
        SELECT 1 FROM pg_replication_slots
        WHERE slot_name = 'session_labels_cdc_slot'
    ) THEN
        -- 创建逻辑复制槽
        PERFORM pg_create_logical_replication_slot('session_labels_cdc_slot', 'pgoutput');
        RAISE NOTICE '已创建复制槽: session_labels_cdc_slot';
    ELSE
        RAISE NOTICE '复制槽 session_labels_cdc_slot 已存在，跳过创建';
    END IF;
END $$;

-- 为CDC用户授权（如果需要单独的CDC用户）
-- 注意：这里假设使用现有的数据库用户，如果需要单独的CDC用户，请取消注释并修改

-- 创建CDC专用用户（可选）
-- CREATE USER cdc_user WITH REPLICATION LOGIN PASSWORD 'cdc_password';

-- 授权CDC用户访问数据库
-- GRANT CONNECT ON DATABASE nta TO cdc_user;
-- GRANT USAGE ON SCHEMA public TO cdc_user;
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO cdc_user;
-- GRANT SELECT ON alarm_rules, alarm_records TO cdc_user;

-- 为现有用户添加复制权限（如果需要）
-- ALTER USER your_existing_user WITH REPLICATION;

-- 创建证书标签CDC复制槽
-- 如果复制槽已存在，则跳过创建
DO $$
BEGIN
    -- 检查复制槽是否已存在
    IF NOT EXISTS (
        SELECT 1 FROM pg_replication_slots
        WHERE slot_name = 'cert_labels_cdc_slot'
    ) THEN
        -- 创建逻辑复制槽
        PERFORM pg_create_logical_replication_slot('cert_labels_cdc_slot', 'pgoutput');
        RAISE NOTICE '已创建复制槽: cert_labels_cdc_slot';
    ELSE
        RAISE NOTICE '复制槽 cert_labels_cdc_slot 已存在，跳过创建';
    END IF;
END $$;

-- 验证复制槽状态
SELECT
    slot_name,
    plugin,
    slot_type,
    database,
    active,
    restart_lsn,
    confirmed_flush_lsn
FROM pg_replication_slots
WHERE slot_name IN ('alarm_cdc_slot', 'session_labels_cdc_slot', 'cert_labels_cdc_slot');

-- 显示当前WAL级别
SHOW wal_level;

-- 显示最大复制槽数量
SHOW max_replication_slots;

-- 显示最大WAL发送者数量
SHOW max_wal_senders;
