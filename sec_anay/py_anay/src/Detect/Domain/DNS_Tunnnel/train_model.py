from distutils.util import rfc822_escape
import sys
sys.path.append("./")

import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix
from sklearn.ensemble import RandomForestClassifier
import joblib
from sklearn2pmml import PMMLPipeline, sklearn2pmml
from sklearn2pmml.preprocessing.lightgbm import make_lightgbm_dataframe_mapper
from lightgbm import LGBMClassifier

DNS_TUNNEL_FEATURES = ['NX_percent', 'ans_entropy', 'ans_entropy_mean',
       'ans_session_std', 'distinct_que_type_cnt', 'domain_session_std',
       'pre_cnt', 'pre_entropy', 'pre_entropy_mean', 'que_AAAA_percent',
       'que_A_percent', 'que_CNAME_percent', 'que_MX_percent',
       'que_NSEC_percent', 'que_NS_percent', 'que_PTR_percent',
       'que_TXT_percent', 'ttl_avg']

BlackFile = "Detect/Domain/DNS_Tunnnel/Data/black_dns_servers.csv"
WhiteFile = "Detect/Domain/DNS_Tunnnel/Data/white_dns_servers.csv"

DNS_TUNNEL_MODEL_DEFAULT_PATH = "Detect/Domain/DNS_Tunnnel/Data/rf_dns_tunnel.model"
DNS_TUNNEL_RF_PMML_MODEL_DEFAULT_PATH = "Detect/Domain/DNS_Tunnnel/Data/rf_dns_tunnel.pmml"
DNS_TUNNEL_LGBM_PMML_MODEL_DEFAULT_PATH = "Detect/Domain/DNS_Tunnnel/Data/lgbm_dns_tunnel.pmml"
DNS_TUNNEL_LGBM_TXT_MODEL_DEFAULT_PATH = "Detect/Domain/DNS_Tunnnel/Data/lgbm_dns_tunnel.txt"

def format_features(x):
    x.fillna({"ttl_avg":0},inplace=True)
    return x

def load_data_from_file(filename,label):
    df = pd.read_csv(filename)
    x = df[DNS_TUNNEL_FEATURES]
    y = [label] * x.shape[0]
    return x,y

def load_train_data():
    w_x,w_y = load_data_from_file(WhiteFile,label=0)
    b_x,b_y = load_data_from_file(BlackFile,label=1)
    x = pd.concat([w_x,b_x])
    y = w_y+b_y
    x = format_features(x)
    return x,y


def train_RF_model(x_train,y_train):
    rf = RandomForestClassifier()
    rf.fit(x_train, y_train)
    return rf

def check_model(model,x_test,y_test):
    y_pred = model.predict(x_test)
    m = confusion_matrix(y_test,y_pred)
    fp,tn,tp = m[0][1],m[0][0],m[1][1]
    p = sum(m[1])
    n = sum(m[0])
    print("准确率为百分之:",(tp+tn)/(p+n)*100)
    print("误报率为百分之:",fp/(fp+tp)*100)
    print("召回率为百分之:",tp/p*100)
    print("-"*20)

def save_model(model,filepath):
    joblib.dump(model, filepath)

def load_model(filepath=DNS_TUNNEL_MODEL_DEFAULT_PATH):
    return joblib.load(filepath)

def predict_df(df):
    x = df[DNS_TUNNEL_FEATURES]
    x = format_features(x)
    model = load_model()
    y_preds = model.predict_proba(x)    
    df["y_pred"] = pd.Series([y[1] for y in y_preds])
    return df

def check():
    x,y = load_train_data()
    x_train, x_test, y_train, y_test = train_test_split(x, y, test_size=0.2, random_state=928)    
    rf = train_RF_model(x_train,y_train)
    check_model(rf,x_test,y_test)

def train():
    x,y = load_train_data()
    rf = train_RF_model(x,y)
    save_model(rf,DNS_TUNNEL_MODEL_DEFAULT_PATH)
    
    model = RandomForestClassifier()
    save_RF_pmml_model(model,x,y)

    mapper, categorical_feature = make_lightgbm_dataframe_mapper(x.dtypes, missing_value_aware = True)
    classifier = LGBMClassifier(random_state = 13)
    save_LGBM_pmml_model(mapper, categorical_feature,classifier,x,y)

    LGBM_model = LGBMClassifier(random_state = 13)
    LGBM_model.fit(x,y)
    model_str = LGBM_model.booster_.model_to_string()
    save_to_file(DNS_TUNNEL_LGBM_TXT_MODEL_DEFAULT_PATH,model_str)


def save_RF_pmml_model(model,x_train, y_train):
    pipeline = PMMLPipeline([("classifier",model)])
    pipeline.fit(x_train, y_train)
    sklearn2pmml(pipeline,DNS_TUNNEL_RF_PMML_MODEL_DEFAULT_PATH,with_repr=True)

def save_LGBM_pmml_model(mapper,categorical_feature,classifier,x_train, y_train):
    pipeline = PMMLPipeline([("mapper", mapper),("classifier", classifier)])
    pipeline.fit(x_train, y_train,classifier__categorical_feature = categorical_feature)
    sklearn2pmml(pipeline,DNS_TUNNEL_LGBM_PMML_MODEL_DEFAULT_PATH,with_repr=True)

def save_to_file(file_name, contents):
    fh = open(file_name, 'w')
    fh.write(contents)
    fh.close()

if __name__=='__main__':
    check()
    train()

