apiVersion: v1
kind: ConfigMap
metadata:
  name: flink-alarm-cdc-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  flink-conf.yaml: |
    # Flink CDC 告警同步作业配置
    
    # 基础配置
    jobmanager.rpc.address: flink-jobmanager
    jobmanager.rpc.port: 6123
    jobmanager.memory.process.size: 1600m
    taskmanager.memory.process.size: 1728m
    taskmanager.numberOfTaskSlots: 2
    parallelism.default: 2
    
    # 检查点配置
    state.backend: rocksdb
    state.checkpoints.dir: file:///opt/flink/checkpoints
    state.savepoints.dir: file:///opt/flink/savepoints
    execution.checkpointing.interval: 60000
    execution.checkpointing.min-pause: 30000
    execution.checkpointing.timeout: 600000
    execution.checkpointing.max-concurrent-checkpoints: 1
    
    # 重启策略
    restart-strategy: fixed-delay
    restart-strategy.fixed-delay.attempts: 3
    restart-strategy.fixed-delay.delay: 30s
    
    # 网络配置
    taskmanager.network.memory.fraction: 0.1
    taskmanager.network.memory.min: 64mb
    taskmanager.network.memory.max: 1gb
    
    # 日志配置
    rootLogger.level: INFO
    logger.com.geeksec: DEBUG
    
  application.properties: |
    # PostgreSQL 配置
    postgresql.host={{ include "nta.postgresqlConfig" . | fromYaml | get "host" }}
    postgresql.port={{ include "nta.postgresqlConfig" . | fromYaml | get "port" }}
    postgresql.database={{ include "nta.postgresqlConfig" . | fromYaml | get "database" }}
    postgresql.username=${DB_USERNAME}
    postgresql.password=${DB_PASSWORD}
    
    # Doris 配置
    doris.fe-nodes={{ include "nta.dorisConfig" . | fromYaml | get "feNodes" }}
    doris.username=${DORIS_USERNAME}
    doris.password=${DORIS_PASSWORD}
    doris.database=nta
    doris.table=ods_alarm_log
    
    # CDC 配置
    cdc.slot.name=alarm_cdc_slot
    cdc.snapshot.mode=initial
    cdc.decoding.plugin=pgoutput
    
    # 同步配置
    sync.batch.size=1000
    sync.batch.interval=5000
    sync.parallelism=2
