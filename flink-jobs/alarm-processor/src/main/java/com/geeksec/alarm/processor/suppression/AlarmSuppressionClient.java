package com.geeksec.alarm.processor.suppression;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.alarm.processor.config.AlarmProcessorConfig;
import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.suppression.dto.AlarmSuppressionChangeMessage;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.io.IOException;
import java.time.Duration;
import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 告警抑制规则客户端
 * 负责从告警服务获取抑制规则数据并维护本地缓存
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
@Slf4j
public class AlarmSuppressionClient {

    private final AlarmProcessorConfig config;
    private final OkHttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final String baseUrl;

    /** 本地抑制规则缓存 */
    private final ConcurrentHashMap<String, Boolean> localSuppressionCache = new ConcurrentHashMap<>();

    /** Kafka消费者 */
    private KafkaConsumer<String, String> kafkaConsumer;

    /** 后台线程池 */
    private final ScheduledExecutorService executorService = Executors.newScheduledThreadPool(2);

    /** 初始化状态 */
    private final AtomicBoolean initialized = new AtomicBoolean(false);

    /** JSON媒体类型 */
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    public AlarmSuppressionClient(AlarmProcessorConfig config) {
        this.config = config;
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .build();
        this.objectMapper = new ObjectMapper();
        this.baseUrl = config.getAlarmServiceBaseUrl();
        
        // 启动初始化
        initialize();
    }

    /**
     * 初始化白名单客户端
     */
    private void initialize() {
        executorService.submit(() -> {
            try {
                // 初始加载抑制规则数据
                loadSuppressionFromService();
                
                // 启动Kafka消费者监听抑制规则变更
                startKafkaConsumer();
                
                // 定期刷新抑制规则
                schedulePeriodicRefresh();
                
                initialized.set(true);
                log.info("告警抑制规则客户端初始化完成");
                
            } catch (Exception e) {
                log.error("告警抑制规则客户端初始化失败", e);
            }
        });
    }

    /**
     * 检查告警是否满足抑制规则
     */
    public boolean shouldSuppress(Alarm alarm) {
        try {
            // 如果未初始化完成，等待一段时间
            if (!initialized.get()) {
                log.warn("告警抑制规则尚未初始化完成，等待初始化...");
                // 等待最多3秒
                for (int i = 0; i < 30 && !initialized.get(); i++) {
                    Thread.sleep(100);
                }
                if (!initialized.get()) {
                    log.error("告警抑制规则初始化超时，跳过抑制规则检查");
                    return false;
                }
            }

            // 提取告警信息
            String victim = alarm.getDstIp();
            String attacker = alarm.getSrcIp();
            
            // 检查告警的所有标签是否满足抑制规则
            if (alarm.getLabels() != null && !alarm.getLabels().isEmpty()) {
                for (String label : alarm.getLabels()) {
                    if (isAlarmSuppressed(victim, attacker, label)) {
                        return true;
                    }
                }
            }
            
            // 如果没有标签或标签都不满足抑制规则，使用告警类型作为标签
            return isAlarmSuppressed(victim, attacker, alarm.getAlarmType());
            
        } catch (Exception e) {
            log.error("检查告警抑制规则失败: {}", alarm, e);
            return false;
        }
    }

    /**
     * 检查指定的告警信息是否满足抑制规则
     */
    public boolean isAlarmSuppressed(String victim, String attacker, String label) {
        if (victim == null || attacker == null || label == null) {
            return false;
        }

        // 直接查询本地缓存
        String cacheKey = buildCacheKey(victim, attacker, label);
        Boolean result = localSuppressionCache.get(cacheKey);
        return Boolean.TRUE.equals(result);
    }

    /**
     * 构建缓存键
     */
    private String buildCacheKey(String victim, String attacker, String label) {
        return String.format("%s|%s|%s", victim, attacker, label);
    }

    /**
     * 从告警服务加载抑制规则数据
     */
    private void loadSuppressionFromService() {
        try {
            String url = baseUrl + "/alarm-suppression";
            Request request = new Request.Builder()
                    .url(url)
                    .get()
                    .build();

            try (Response response = httpClient.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    // 解析响应并更新本地缓存
                    updateLocalCache(responseBody);
                    log.info("成功从告警服务加载抑制规则数据，缓存大小: {}", localSuppressionCache.size());
                } else {
                    log.error("从告警服务加载抑制规则失败: HTTP {}", response.code());
                }
            }
        } catch (Exception e) {
            log.error("从告警服务加载抑制规则数据失败", e);
        }
    }

    /**
     * 更新本地缓存
     */
    private void updateLocalCache(String responseBody) {
        try {
            // 这里需要根据实际的API响应格式来解析
            // 假设返回的是抑制规则项的列表
            // 具体实现需要根据AlarmSuppressionController的返回格式调整
            log.debug("更新本地抑制规则缓存: {}", responseBody);
            
            // TODO: 实现具体的解析逻辑
            // 示例代码，需要根据实际API调整
            
        } catch (Exception e) {
            log.error("更新本地抑制规则缓存失败", e);
        }
    }

    /**
     * 启动Kafka消费者监听抑制规则变更
     */
    private void startKafkaConsumer() {
        try {
            Properties props = new Properties();
            props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getKafkaBootstrapServers());
            props.put(ConsumerConfig.GROUP_ID_CONFIG, "alarm-processor-suppression");
            props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
            props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
            props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
            props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, true);

            kafkaConsumer = new KafkaConsumer<>(props);
            kafkaConsumer.subscribe(Collections.singletonList("alarm-suppression-changes"));

            // 启动消费者线程
            executorService.submit(() -> {
                try {
                    while (!Thread.currentThread().isInterrupted()) {
                        ConsumerRecords<String, String> records = kafkaConsumer.poll(Duration.ofMillis(1000));
                        for (ConsumerRecord<String, String> record : records) {
                            handleSuppressionChange(record.value());
                        }
                    }
                } catch (Exception e) {
                    log.error("Kafka消费者异常", e);
                } finally {
                    kafkaConsumer.close();
                }
            });

            log.info("Kafka消费者启动成功，监听抑制规则变更");
        } catch (Exception e) {
            log.error("启动Kafka消费者失败", e);
        }
    }

    /**
     * 处理抑制规则变更消息
     */
    private void handleSuppressionChange(String message) {
        try {
            AlarmSuppressionChangeMessage changeMessage = objectMapper.readValue(message, AlarmSuppressionChangeMessage.class);
            
            String cacheKey = buildCacheKey(
                changeMessage.getVictim(), 
                changeMessage.getAttacker(), 
                changeMessage.getLabel()
            );

            switch (changeMessage.getOperation()) {
                case "ADD":
                    localSuppressionCache.put(cacheKey, true);
                    log.debug("添加抑制规则项到本地缓存: {}", cacheKey);
                    break;
                case "REMOVE":
                    localSuppressionCache.remove(cacheKey);
                    log.debug("从本地缓存移除抑制规则项: {}", cacheKey);
                    break;
                default:
                    log.warn("未知的抑制规则变更操作: {}", changeMessage.getOperation());
            }
        } catch (Exception e) {
            log.error("处理抑制规则变更消息失败: {}", message, e);
        }
    }

    /**
     * 定期刷新抑制规则
     */
    private void schedulePeriodicRefresh() {
        executorService.scheduleWithFixedDelay(
            this::loadSuppressionFromService,
            5, // 初始延迟5分钟
            30, // 每30分钟刷新一次
            TimeUnit.MINUTES
        );
        log.info("定期刷新抑制规则任务已启动，间隔30分钟");
    }

    /**
     * 关闭客户端
     */
    public void close() {
        try {
            if (kafkaConsumer != null) {
                kafkaConsumer.close();
            }
            executorService.shutdown();
            if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
            }
            httpClient.dispatcher().executorService().shutdown();
            httpClient.connectionPool().evictAll();
            log.info("告警抑制规则客户端已关闭");
        } catch (Exception e) {
            log.error("关闭告警抑制规则客户端失败", e);
        }
    }

    /**
     * 获取缓存大小
     */
    public int getCacheSize() {
        return localSuppressionCache.size();
    }

    /**
     * 清空本地缓存
     */
    public void clearCache() {
        localSuppressionCache.clear();
        log.info("本地抑制规则缓存已清空");
    }

    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return initialized.get();
    }
}