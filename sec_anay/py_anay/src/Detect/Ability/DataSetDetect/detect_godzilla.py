import sys
sys.path.append("./")

import copy
import numpy as np
import pandas as pd

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.AlarmHelper as alarm_helper
import PyGksec.GkHelper.TagHelper as tag_helper
from PyGksec.GkUtils import common
from PyGksec.GkHelper.GkLogHelper import gk_logging
from utils.redis_utils.redis_helper import redis_not_alarm
from utils.mysql_utils.mysql_helper import mysql_not_white

PHP_TAG_TEXTS = ["哥斯拉-php"]  # PHP 哥斯拉标签
PHP_TAG_IDS = [tag_helper.Tag_Text_Map[tag_text]["Tag_Id"] for tag_text in PHP_TAG_TEXTS]  

F_DICT = {
    "sPayload":{
        "over_dict" : {"lower_ratio": 0.18, "upper_ratio": 0.12, "max_word_len": 15.0, "mean_word_len": 10.0},
        "less_dcit" : {"space_ratio": 0.07, "num_ratio": 0.35, "lower_ratio": 0.69, "std_word_len": 11.5, "word_cnt": 3.0}
        },
    "dPayload":{
        "over_dict" : {"space_ratio": 0.06, "lower_ratio": 0.15, "max_word_len": 5.0, "mean_word_len": 1, "std_word_len": 1.4, "word_cnt": 3.0},
        "less_dcit" : {"num_ratio": 0.22, "lower_ratio": 0.44, "max_word_len": 18.0, "mean_word_len": 10.0, "std_word_len": 6.24, "word_cnt": 14.0}
        }
    }
def get_godzilla_payload_eature(payload) -> dict:
    """
        负载特征计算
        :param str payload: 负载字符串
        :return dict: 返回计算后的特征
    """
    payload_str = ""
    ch_range_dict = {
        "space" : [32,32],
        "num": [48,57],
        "lower": [97,122],
        "upper": [65,90]
    }
    ch_count_dict = dict((key,0) for key in ch_range_dict)
    for start in range(0,len(payload),2):
        ch_int = int(payload[start:start+2],16)
        ch = chr(ch_int)
        payload_str += ch
        for key,range_list in ch_range_dict.items():
            if ch_int >= range_list[0] and ch_int <= range_list[1]:
                ch_count_dict[key] += 1
    f = copy.deepcopy(ch_count_dict)
    for key,val in ch_count_dict.items():
        f[f"{key}_ratio"] = val / len(payload_str)
    words = payload_str.strip().split(" ")
    lens = [len(w) for w in words]
    f["max_word_len"] = max(lens)
    f["mean_word_len"] = np.mean(lens)
    f["std_word_len"] = np.std(lens)
    f["word_cnt"] = len(lens)
    return f

def detect_godzilla_payload(payloads, f_dict) -> int:
    """
        检测负载,决策是否godzilla并打标
        :param str payloads: 负载字符串
        :param dict f_dict: 决策阈值特征
        :return int: 返回决策结果,值越大越可能
    """
    result = []
    if payloads is None or len(payloads)==0:
        return 0
    
    payloads = list(set(payloads))

    if not isinstance(payloads, list) or len(payloads) == 0:
        return 0

    over_dict = f_dict["over_dict"] 
    less_dict = f_dict["less_dcit"]
            
    for payload in payloads:
        if all(c=='0' for c in payload):
            return 0
        
        f = get_godzilla_payload_eature(payload)

        score = 1
        for key,val in over_dict.items():
            if f[key] < val:
                score = 0
        for key,val in less_dict.items():
            if f[key] > val:
                score = 0
        result.append(score)

    return sum(result) / len(result)

def detect_godzilla_fromES(x_raw) -> pd.Series:
    """
        ES负载检测
        :param pd.Series x_raw: ES会话数据
        :return pd.Series x_raw: 返回添加检测标记后的会话数据
    """
    spayloads = common.get_data_from_dict(x_raw,"pkt/sPayload",[])
    dpayloads = common.get_data_from_dict(x_raw,"pkt/dPayload",[])

    x_raw["sPayload"] = spayloads
    x_raw["dPayload"] = dpayloads

    if (s_score := detect_godzilla_payload(spayloads, F_DICT["sPayload"])) > 0.5 and (d_score := detect_godzilla_payload(dpayloads, F_DICT["dPayload"])) > 0.5:
        if (s_score == 1 and d_score == 1):
            x_raw["detected"] = (s_score, d_score)
    else:
        x_raw["detected"] = 0
    return x_raw

def get_alarm_reason(df:pd.DataFrame, godzilla_type) -> list[dict]:
    """
        告警原因生成
        :param DataFrame df: ES会话数据
        :return list[dict]: 字典组成的列表
    """
    Reason_Map = {
        "PHP": {"key": "1.使用了哥斯拉黑客工具:检测匹配base_decode特征字", "actual_value":"eval%28base64_decode%28strrev%28urldecode%28%27"},
        "OTHER": {"key": "使用了哥斯拉黑客工具:模型预测结果,检测出客户端Cookie"}
    }

    alarm_reason = []
    Reason_Map["OTHER"]["actual_value"] = df.Cookie.drop_duplicates().to_list()

    if godzilla_type == "PHP":
        alarm_reason.append(Reason_Map[godzilla_type])
        Reason_Map["OTHER"]["key"] = "2." + Reason_Map["OTHER"]["key"]

    alarm_reason.append(Reason_Map["OTHER"])
                        
    return alarm_reason
        
def get_related_labels(df:pd.DataFrame, include_tags) -> list[dict]:
    """
        告警关联标签,该告警包含的标签和实际关联标签到标签ID映射转换
        :param DataFrame df: ES会话数据
        :param str include_tags: 告警关联的标签名称
        :return list: 关联标签ID
    """
    label_sets = {label for labels in df["Labels"].to_list() for label in labels}

    if include_tags is None or not include_tags or pd.isna(include_tags):
        return []

    # 关联标签ID转换
    tags = include_tags.split("/")
    tag_id_sets = {tag_helper.Tag_Text_Map[tag_name]["Tag_Id"] for tag_name in tags if tag_name in tag_helper.Tag_Text_Map}

    # 实际关联标签
    return list((label_sets & set(PHP_TAG_IDS)) | (label_sets & tag_id_sets))

def write_godzilla_alarm(df:pd.DataFrame, alarm_name, task_id, batch_id, godzilla_type="OTHER") -> None:
    """
        ICMP隧道告警生成与推送
        :param df: DataFrame格式ES会话数据
        :param alarm_name: 告警名称
        :param int task_id: 任务id
        :param int batch_id: 批次id
        :param godzilla_type: godzilla类型
        :return None: 无返回
    """
    alarm_data = alarm_helper.get_alarm_json_data(alarm_name=alarm_name)
    alarm_data["attack_route"] = []
    alarm_data["victim"] = [{"ip": vip} for vip in df.dIp.drop_duplicates()][:10]
    alarm_data["attacker"] = [{"ip": aip} for aip in df.sIp.drop_duplicates()][:10]
    alarm_data["targets"] = [{"name": aip["ip"], "type":"ip", "labels":[]} for aip in alarm_data["attacker"]][:10]

    alarm_data["alarm_reason"] = get_alarm_reason(df, godzilla_type)
    alarm_data["alarm_principle"] = "客户端使用哥斯拉黑客工具访问服务器,存在被黑客工具操控风险。"
    alarm_data["alarm_handle_method"] = "检查服务器是否被移植黑客工具哥斯拉操控，排查是否存在哥斯拉软件的运行，将该客户端IP过滤。"

    alarm_data["alarm_related_label"] = get_related_labels(df, alarm_helper.ALARM_KNOW_NAME_MAP[alarm_name]["include_tags"])
    alarm_data["alarm_session_list"] = df["SessionId"].drop_duplicates().tolist()[:10]
    df["alarm_related_label"] = df.Labels.map(lambda x: list(set(x) & set(alarm_data["alarm_related_label"])))
    df["alarm_related_label"] = df.alarm_related_label.map(lambda x: x if len(x) > 0 else [alarm_data['alarm_knowledge_id']])
    alarm_data["attack_chain_list"] = list({f"{x['dIp']}_{x['sIp']}_{label}" for _, x in df.iterrows() for label in x['alarm_related_label']})

    if alarm_data["targets"] != [] and alarm_data["victim"] != [] and alarm_data["attacker"] != [] and redis_not_alarm(alarm_data["attack_chain_list"]) and mysql_not_white(alarm_data["attack_chain_list"]):
        alarm_data = alarm_helper.append_alarm_task_info(alarm_data, task_id, batch_id)
        alarm_helper.add_remote_alarm_json(alarm_data)


def load_fit_cookie_session(task_id, batch_id) -> list:
    """
        加载Cookie匹配的会话

        :param int task_id: 任务id,
        :param int batch_id: 批次id
        :return list: 返回匹配的会话ID及Cookie
    """
    table, detail = eh.get_basic_query(es_type="http", task_id=task_id, batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"wildcard": {"Client.Cookie":"*=*;"}})
    detail["aggs"] = eh.make_term_agg({"doc['SessionId'].value":"session","doc['Client.Cookie'].value":"cookie"}, "fit_cookie")

    for es_result in eh.load_agg_data_of_split_time(table, detail, "fit_cookie"):
        session_cookies = [d["key"].split("#") for d in es_result]
        yield session_cookies

def check_godzilla(table, base_detail, godzilla_type, task_id, batch_id):
    """
        检测哥斯拉黑客工具,生成告警并推送
        :param str table: 索引
        :param dict base_detail: 基础查询条件
        :param godzilla_type: godzilla类型
        :param int task_id: 任务id
        :param int batch_id: 批次id
        :return: 无返回
    """
    for session_cookies in load_fit_cookie_session(task_id, batch_id):
        session_lists = [d[0] for d in session_cookies]
        session_cookie_map = {d[0]:d[1] for d in session_cookies}

        detail = copy.deepcopy(base_detail)
        detail["query"]["bool"]["must"].append({"terms": {"SessionId": session_lists}})

        df = eh.load_es_data_to_DF(table, detail, process_func=detect_godzilla_fromES)
        if df is None or df.shape[0] == 0:
            continue

        df["Cookie"] = df.SessionId.map(lambda x: session_cookie_map[x])

        if df is None:
            continue

        df = df[df.detected!=0]
        if df.shape[0] == 0:
            continue
        
        df.groupby(["dIp"], as_index=False).apply(lambda group:write_godzilla_alarm(group, alarm_name="黑客工具", task_id=task_id, batch_id=batch_id, godzilla_type=godzilla_type))
    

def detect_godzilla_other(task_id: int = -1, batch_id: int = -1) -> None:
    """
        其它哥斯拉检测,生成告警并推送
        :param int task_id: 任务id,默认为-1
        :param int batch_id: 批次id,默认为-1
        :return: 无返回
    """
    table, base_detail = eh.get_basic_query(es_type="connect*", task_id=task_id, batch_id=batch_id)
    base_detail["query"]["bool"]["must"].append({"terms": {"AppName":["APP_HTTP", "APP_HTTPS"]}})
    base_detail["query"]["bool"]["must_not"].append({"terms": {"Labels":PHP_TAG_IDS}})

    check_godzilla(table, base_detail, "OTHER", task_id, batch_id)

def detect_godzilla_php(task_id: int = -1, batch_id: int = -1) -> None:
    """
        php哥斯拉检测,基于标签
        :param int task_id: 任务id,默认为-1
        :param int batch_id: 批次id,默认为-1  
    """
    table, base_detail = eh.get_basic_query(es_type="connect*", task_id=task_id, batch_id=batch_id)
    base_detail["query"]["bool"]["must"].append({"terms": {"AppName":["APP_HTTP", "APP_HTTPS"]}})
    base_detail["query"]["bool"]["must"].append({"terms": {"Labels":PHP_TAG_IDS}})

    check_godzilla(table, base_detail, "PHP", task_id, batch_id)

def detect_godzilla_tool(task_id: int = -1, batch_id: int = -1) -> None:
    detect_godzilla_php(task_id=task_id, batch_id=batch_id)
    detect_godzilla_other(task_id=task_id, batch_id=batch_id)


if __name__ == '__main__':
    task_id, batch_id = eh.get_latest_task_id('connectinfo*')
    detect_godzilla_tool(task_id, batch_id)
