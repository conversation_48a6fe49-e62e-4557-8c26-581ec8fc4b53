-- public_suffix_list数据导入脚本（使用COPY机制优化版本）
-- 原始文件: 06-public-suffix-data.sql
-- CSV文件: public_suffix_list.csv
-- 数据行数: 9,817
-- 文件大小: 201,542 字节

-- 清空现有数据
TRUNCATE TABLE public_suffix_list CASCADE;

-- 使用COPY命令从CSV文件导入数据
\COPY public_suffix_list (
        suffix,    is_wildcard
) FROM '/csv/public_suffix_list.csv' WITH (FORMAT csv, HEADER false, NULL '');

-- 更新统计信息以优化查询性能
ANALYZE public_suffix_list;

-- 显示导入结果统计
DO $$
DECLARE
    total_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_count FROM public_suffix_list;
    RAISE NOTICE '=== public_suffix_list数据导入完成 ===';
    RAISE NOTICE '总记录数: %', total_count;
    RAISE NOTICE '================================';
END $$;
