##
# @file pcap_mod.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2022-03-04

import os,sys
file_mod = []
file_num = 0 
file_sum = 0 
dst_file_num = 0

dst_path = "" 
def mod_pcap_cmd():
    global file_num
    global file_mod 
    global file_sum 
    global dst_file_num 
    cmd = ""
    if file_num == 0:
        return
    for i in file_mod:
        if i.endswith("pcap") or i.endswith("cap")  or i.endswith("pcapng"):
            cmd += " " + i
    file_dis_pcap = dst_path + str(dst_file_num)+".pcap"
    print("file_dis_pcap ======== " , file_dis_pcap)
    cmd_ = " mergecap  -F pcap -w "+ file_dis_pcap + " " + cmd
    print(cmd_)
    os.system(cmd_)
    dst_file_num += 1
    file_mod = []
    file_num = 0 
    file_sum = 0
def add_pcap(file_name ,Num):
    global file_sum
    global file_num
    file_mod.append(file_name)
    fsize = os.path.getsize(file_name)
    file_sum += fsize
    file_num +=1
    if file_sum >  Num or file_num > 1000:
        mod_pcap_cmd()

    
def find_pcap_mod(src_path , Num):
    #
    for root, dirs, files in os.walk(src_path, topdown=False):
        for name in files:
            path = os.path.join(root, name)
            print(os.path.join(root, name))
            add_pcap(path,Num)
    mod_pcap_cmd()

if __name__=="__main__":
    if len(sys.argv) ==4 :
       src_path = sys.argv[1]
       dst_path = sys.argv[2]
       Num = int(sys.argv[3] )* 1024*1024
       find_pcap_mod(src_path ,  Num)

    else:
        print("参数错误 ：")
        print("  python  pcap_mod.py  源pcap路经   目标存储路径 合并后文件大小 单位M ")
        sys.exit(1)
        
