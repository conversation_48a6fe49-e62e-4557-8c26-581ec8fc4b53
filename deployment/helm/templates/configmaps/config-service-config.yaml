{{- if .Values.services.config-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: config-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    server:
      port: 8087
      servlet:
        context-path: /config

    spring:
      datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: org.postgresql.Driver
        url: jdbc:postgresql://{{ include "nta.postgresqlConfig" . | fromYaml | get "host" }}:{{ include "nta.postgresqlConfig" . | fromYaml | get "port" }}/{{ .Values.services.config-service.database }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
        druid:
          initial-size: 5
          min-idle: 5
          max-active: 20
          max-wait: 60000
          time-between-eviction-runs-millis: 60000
          min-evictable-idle-time-millis: 300000
          validation-query: SELECT 1
          test-while-idle: true
          test-on-borrow: false
          test-on-return: false
          pool-prepared-statements: true
          max-pool-prepared-statement-per-connection-size: 20
          filters: stat,wall
      redis:
        host: {{ include "nta.redisConfig" . | fromYaml | get "host" }}
        port: {{ include "nta.redisConfig" . | fromYaml | get "port" }}
        database: 0
        timeout: 10000
      application:
        name: config-service
      config:
        import: "configtree:/config/"

    mybatis-flex:
      mapper-locations: classpath:mapper/*.xml
      type-aliases-package: com.geeksec.config.entity
      configuration:
        map-underscore-to-camel-case: true
        log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
      global-config:
        logic-delete-column: deleted
        version-column: version

    knife4j:
      enable: true
      production: false
      basic:
        enable: false
      setting:
        language: zh-CN

    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics
      endpoint:
        health:
          show-details: always
{{- end -}}
