#!/bin/bash
# Last Update:2020-11-10 14:53:08
# @file stop_task.sh  
# @brief: 结束正在运行的任务  参数为正运行的 batch_id 
# <AUTHOR>
# @version 0.1.00
# @date 2020-11-10

#########
ps -eaf | grep import_task.sh | grep -v  grep | awk '{print $2}' | xargs kill -TERM
/etc/init.d/thdd stop
/etc/init.d/edd stop
rm -rf /data/pbfiles/*
rm -rf /data/json_file_send/*
rm -rf /data/json_file_send_done/*
echo "">/opt/GeekSec/th/bin/file_index.txt
echo "">/opt/GeekSec/task/STL/file_index.txt
ps -eaf | grep JsonFile2ES | grep -v  JsonFile2ES_watchdog.sh  | grep -v grep | awk '{print $2}' | xargs kill -9
python3 /opt/GeekSec/web/rule_syn/task_end.py 
sleep 10
python3 /opt/GeekSec/web/rule_syn/del_batch.py  batch $1
# 删除文件


