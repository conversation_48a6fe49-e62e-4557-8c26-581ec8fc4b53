# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/29 16:48
# 文件名称 : cert_config.py
# 开发工具 : PyCharm
import os,json,pymysql.cursors
def write_file(filename , context):
    os.system("echo \"\">"+filename)
    fnew = open(filename, "w")
    fnew.write(context)
    fnew.close()
base_json = {}
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
cursor.execute("select * from tb_cert_config where type = 'CERT_CHECK_CONFIG';")
results = cursor.fetchall()[0]
context = {}
context['blackListLog'] = results['b_log']
context['blackListFlow'] = results['b_flow']
context['whiteListLog'] = results['w_log']
context['whiteListFlow'] = results['w_flow']
os.system("echo \"\">cert_config.json")
write_file("cert_config.json",json.dumps(context))
isExists = os.path.exists("/opt/GeekSec/th/bin/conf/")
if not isExists:
    os.makedirs("/opt/GeekSec/th/bin/conf/")
os.system ("cp -rf cert_config.json /opt/GeekSec/th/bin/conf/cert_config.json")
db.close()

