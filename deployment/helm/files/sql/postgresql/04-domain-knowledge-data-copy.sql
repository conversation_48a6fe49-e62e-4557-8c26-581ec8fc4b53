-- domain_knowledge数据导入脚本（使用COPY机制优化版本）
-- 原始文件: 04-domain-knowledge-data.sql
-- CSV文件: domain_knowledge.csv
-- 数据行数: 277,705
-- 文件大小: 19,645,422 字节

-- 清空现有数据
TRUNCATE TABLE domain_knowledge CASCADE;

-- 使用COPY命令从CSV文件导入数据
\COPY domain_knowledge (
        domain,    tranco_rank,    tranco_list_date,    registrar_name,    contact_email,    whois_server,    name_servers,    created_date,    updated_date,    expires_date,    status,    registrant_name,    registrant_organization,    registrant_country,    registrant_email,    admin_contact_name,    admin_contact_organization,    admin_contact_email,    is_mining_domain,    is_video_domain,    is_malicious_domain,    threat_category,    threat_confidence_score
) FROM '/csv/domain_knowledge.csv' WITH (FORMAT csv, HEADER false, NULL '');

-- 更新统计信息以优化查询性能
ANALYZE domain_knowledge;

-- 显示导入结果统计
DO $$
DECLARE
    total_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_count FROM domain_knowledge;
    RAISE NOTICE '=== domain_knowledge数据导入完成 ===';
    RAISE NOTICE '总记录数: %', total_count;
    RAISE NOTICE '================================';
END $$;
