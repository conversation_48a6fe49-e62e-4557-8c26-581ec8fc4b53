
import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkHelper.ES_Pattern import get_latest_time

BASELINE_TIME_INTERVAL = 86400

def get_es_query(batch_id,match_dict={},es_type="connectinfo"):
    table,detail = eh.get_basic_query(es_type,batch_id=batch_id)    
    for key,val in match_dict.items():
        if type(val)==list:
            detail["query"]["bool"]["must"].append({"terms":{key:val}})    
        else:
            detail["query"]["bool"]["must"].append({"term":{key:val}})    
    return table,detail


def get_build_query(batch_id,match_dict={},es_type="connectinfo"):
    table,detail = get_es_query(batch_id,match_dict,es_type)
    end_time = get_latest_time(table,detail) - BASELINE_TIME_INTERVAL
    detail["query"]["bool"]["must"].append({"range":{"StartTime":{"lte":end_time}}})    
    return table,detail

def get_detect_query(batch_id,match_dict={},es_type="connectinfo"):
    table,detail = get_es_query(batch_id,match_dict,es_type)
    end_time = get_latest_time(table,detail) - BASELINE_TIME_INTERVAL
    detail["query"]["bool"]["must"].append({"range":{"StartTime":{"gt":end_time}}})    
    return table,detail