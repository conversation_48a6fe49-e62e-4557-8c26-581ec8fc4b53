import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh

from Detect.Ability.detect_galaxy import dateset_alarm
from Detect.Ability.detect_sh import sh_label, sh_alarm

from Detect.APT.APT29.detect_WellMess import detectHttpWellMess
from Detect.APT.APT28.detect_28_Zebrocy import detect_Zebrocy
from Detect.APT.APT29.detect_HTTPS_WellMess import detectWellMessCerts
from Detect.APT.APT_Patchwork.DetectPatchwork import detectPatchworkCert
from Detect.hengshui.ClientBaseLine import check_client_baseline
from Detect.hengshui.DetectInner import detect_inner_ip
from Detect.hengshui.DetectHttpWeakPassword import detect_weakPass_Client
from Detect.hengshui.MacCommuBaseline import check_abnormal_commu
from Detect.hengshui.NetRangeBaseline import check_net_range_baseline


def add_apt_label():
    for task_id, batch_id in eh.get_all_tasks("connectinfo*"):
        detectHttpWellMess(task_id, batch_id)
        detect_Zebrocy(task_id, batch_id)
        detectWellMessCerts(task_id, batch_id)
        detectPatchworkCert(task_id, batch_id)


def add_label():
    #add_apt_label()
    for task_id, batch_id in eh.get_all_tasks("ssl*"):
        sh_label(task_id, batch_id)


def add_alarm():
    for task_id, batch_id in eh.get_all_tasks("connectinfo*"):
        dateset_alarm(task_id, batch_id)
        sh_alarm(task_id, batch_id)


def add_hengshui_label():
    for task_id, batch_id in eh.get_all_tasks("connectinfo*"):
        check_client_baseline(task_id,batch_id)
        # detect_inner_ip(task_id,batch_id)
        check_abnormal_commu(task_id,batch_id)
        check_net_range_baseline(task_id,batch_id)
        # detect_weakPass_Client(task_id,batch_id)

if __name__ == "__main__":
    add_label()
    add_alarm()
