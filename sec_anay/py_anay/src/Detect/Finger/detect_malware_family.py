import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper

from PyGksec.GkHelper.KnowledgeHelper import GKFinger_DF
from PyGksec.GkUtils import common

import copy

family2tag = {
    "Ares": "Ares控制会话",
    "Covenant": "Covenant控制会话",
    "DeimosC2": "DeimosC2控制会话",
    "Emotet": "Emotet控制会话",
    "Emp3r0r": "Emp3r0r控制会话",
    "Octopus": "Octopus控制会话",
    "PoshC2": "PoshC2控制会话",
    "Slackor": "Slackor控制会话",
    "Sliver": "Sliver控制会话",
    "ToRat": "ToRat控制会话",
    "Trickbot": "Trickbot控制会话",
    "GoProxy": "GoProxy代理隧道",
    "Qakbot": "Qakbot控制会话",
    "Quasar": "Quasar控制会话"
}

def checkFamily(task_id,batch_id,family_name,tag_text):
    table,base_detail = eh.get_basic_query("ssl",batch_id=batch_id)
    base_detail["query"]["bool"]["must_not"].append({"terms": {"dIp":common.InnerNet}})
    if tag_text not in tag_helper.Tag_Text_Map:# 查标签内容存不存在
        return
    df = GKFinger_DF[GKFinger_DF.finger_type==family_name]#指纹的family字段属不属于知识库
    if df.shape[0]==0:
        return
    detail = copy.deepcopy(base_detail)
    for query_key in ["sSSLFinger","dSSLFinger"]:
        detail["query"]["bool"]["must_not"].append({"term": {query_key:"0"}})#查询的时候指纹字段不能是0
        fingers = list(df[df.es_type==query_key].finger_es)#根据fingertype和指纹类型（是sSSL还是dSSL指纹）得到finger_es,ES的检索号
        if len(fingers) > 0:
            detail["query"]["bool"]["must"].append({"terms": {query_key:fingers}})#增加terms字段，内容为ES检索号
    result = eh.load_es_data_to_DF(table,detail)# 去ES里根据terms查询,指纹内容不能是0，必须是对应的指纹ES号，不能是dip列表中的那些IP
    # print(result)
    if result is not None and result.shape[0]>0:
        for finger in result.sSSLFinger.drop_duplicates():
            tag_helper.add_tag_for_target_toNebula(tag_text="恶意客户端程序",target=finger,analysis_by="挖矿威胁情报检测模型")
            # print(finger)
        # print(family_name)
        result.apply(lambda x : tag_helper.add_tag_for_session(session=x,tag_text=tag_text),axis=1)# 给每一个会话打上标签
        # result.SessionId.map(\
            # lambda x : tag_helper.add_tag_for_target(target=x,tag_text="恶意加密连接",task_id=task_id))

def detect_finger_of_batch(task_id,batch_id):
    for family_name,tag_text in family2tag.items():
        checkFamily(task_id,batch_id,family_name,tag_text)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detect_finger_of_batch(task_id,batch_id)

