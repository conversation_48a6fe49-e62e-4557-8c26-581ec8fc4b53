{{- if and .Values.infrastructure.doris.enabled .Values.infrastructure.doris.operator.enabled .Values.initialization.doris.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ .Release.Name }}-doris-init
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: doris-init
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "0"
    "helm.sh/hook-delete-policy": hook-succeeded
spec:
  template:
    metadata:
      labels:
        app.kubernetes.io/name: doris-init
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      restartPolicy: OnFailure
      # 等待 Doris FE 服务就绪
      initContainers:
        - name: wait-for-doris-fe
          image: mysql:8.0
          command:
            - /bin/bash
            - -c
            - |
              echo "等待 Doris FE 服务就绪..."
              until mysql -h {{ .Values.infrastructure.doris.cluster.name }}-fe.{{ .Release.Namespace }}.svc -P {{ .Values.infrastructure.doris.fe.queryPort }} -u {{ .Values.infrastructure.doris.credentials.username }} -p{{ .Values.infrastructure.doris.credentials.password.value }} -e "SELECT 1" > /dev/null 2>&1; do
                echo "Doris FE 尚未就绪，继续等待..."
                sleep 10
              done
              echo "Doris FE 服务已就绪，可以开始初始化"
          env:
            - name: TZ
              value: "Asia/Shanghai"
      volumes:
        - name: sql-scripts
          configMap:
            name: doris-init-scripts
      containers:
        - name: doris-init
          image: mysql:8.0
          command:
            - /bin/bash
            - -c
            - |
              echo "开始执行 Doris 初始化脚本..."
              # 按文件名排序执行所有SQL脚本
              for sql_file in $(find /sql-scripts -name "[0-9]*.sql" | sort); do
                if [ -f "$sql_file" ]; then
                  echo "正在执行 $sql_file..."
                  mysql -h {{ .Values.infrastructure.doris.cluster.name }}-fe.{{ .Release.Namespace }}.svc -P {{ .Values.infrastructure.doris.fe.queryPort }} -u {{ .Values.infrastructure.doris.credentials.username }} -p{{ .Values.infrastructure.doris.credentials.password.value }} < "$sql_file"
                  if [ $? -eq 0 ]; then
                    echo "✓ $sql_file 执行成功"
                  else
                    echo "✗ $sql_file 执行失败"
                    exit 1
                  fi
                fi
              done
              echo "Doris 初始化完成"
          env:
            - name: TZ
              value: "Asia/Shanghai"
          volumeMounts:
            - name: sql-scripts
              mountPath: /sql-scripts
          resources:
            {{- toYaml .Values.initialization.resources | nindent 12 }}
      {{- with .Values.initialization.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
