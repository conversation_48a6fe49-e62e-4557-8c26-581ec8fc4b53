#!/bin/bash

# 总物料生成打包脚本
rm -rf bin_build
rm -rf package
mkdir -p package
cp -rf Pb2MsgAll-1.0-SNAPSHOT.jar package/
cp -rf pubconfig package/
cp -rf pubconfig_docker package/
cp -rf web package/
cp -rf PrivateRule_Install.*.tar.gz package/
cp -rf model package/
cp -rf lmdb package/

tar -zcvf ./package.tar.gz install.sh package/*
rm -rf package

mkdir -p bin_build

cp -rf ./bin_install.sh  bin_build/
cp -rf ./package.tar.gz bin_build/
rm -rf ./package.tar.gz
# shellcheck disable=SC2164
cd ./bin_build
# 将bin_install合并到同一个bin文件中
cat bin_install.sh package.tar.gz > platform_service_install.bin
mv platform_service_install.bin ./../
# shellcheck disable=SC2103
cd ..
rm -rf ./bin_build
