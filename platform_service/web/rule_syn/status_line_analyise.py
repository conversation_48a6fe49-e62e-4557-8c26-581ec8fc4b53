# Last Update:2021-09-01 14:07:56
##
# @file status_line_analyise.py
# @brief
# <AUTHOR>
# @version 0.1.00
# @date 2020-04-16
import json,os,sys
import pymysql

def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
passwd = "root"
if os.path.exists("/opt/GeekSec/pubconfig/.mysql"):
    with open("/opt/GeekSec/pubconfig/.mysql") as f:
        passwd = f.read().replace("\n","",10)
if len(sys.argv) != 3:
    pass
else :
    base_json = {}
    task_id = sys.argv[1]
    with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
        base_json = json.load(load_f)
    db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
    cursor = db.cursor()
    if sys.argv[2] == 'ON':
        sql = "update tb_line_analyze set type = 0 where task_id = " +task_id 
    else:
        sql = "update tb_line_analyze set type = 1 where task_id = " + task_id 
    idu_mysql(sql,cursor ,db)


