import sys

sys.path.append("./")


import PyGksec.GkHelper.ESHelper as eh
from PyGksec.GkUtils import common 
from PyGksec.GkConfig import env
from PyGksec.GkHelper.ES_Pattern import get_direction_ip_list

import Detect.BaseLine.BaseLineHelper as bh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.AlarmHelper as alarm_helper

from Detect.Finger.cluster_fingers import load_sip_to_fingers
from Detect.Finger.DNN_Finger_Embedding import GK_Finger_Embedding,EMBEDDING_DIM

import copy
import numpy as np
import pandas as pd

pd.set_option("display.max_colwidth",140)

def load_finger_data(table,base_detail,finger_key):
    detail = copy.deepcopy(base_detail)
    detail["query"]["bool"]["must_not"].append({"term":{finger_key:"0"}})    
    detail["aggs"] = eh.make_term_agg(finger_key,"cfinger")
    data_gen = eh.load_agg_data_of_split_time(table,detail,data_key="cfinger")
    result = []
    for data in data_gen:
        result+=data
    return result    


def check_cfingers(base_list,check_list,gk_embedding):
    shold = EMBEDDING_DIM**0.5/5
    if len(base_list)==0 or len(check_list)==0:
        return []
    check_set = set(x["key"] for x in check_list)
    known_set = set(x["key"] for x in base_list)
    alarm_set = set()
    for check_finger in check_set - known_set:
        min_dis = gk_embedding.get_shortest_dis(check_finger,known_set)
        if min_dis > shold:
            alarm_set.add(check_finger)
    return list(alarm_set)

def check_access_abnormal_fingers(batch_id,match_dict,finger_key,es_type,gk_embedding):
    base_table,base_detail = bh.get_build_query(batch_id,match_dict=match_dict,es_type=es_type)
    base_list = load_finger_data(base_table,base_detail,finger_key=finger_key)
    base_table,base_detail = bh.get_detect_query(batch_id,match_dict=match_dict,es_type=es_type)
    check_list = load_finger_data(base_table,base_detail,finger_key=finger_key)
    r = check_cfingers(base_list,check_list,gk_embedding)
    return r


def write_finger_alarm(df,finger_key,task_id):
    if df is None or df.shape[0]==0:
        return
    if finger_key=="Client.User-Agent":
        df["finger_key"]= df.Client.map(lambda x : x["User-Agent"])
    elif finger_key=="cSSLFinger.keyword":
        df["finger_key"]= df.cSSLFinger
    else:
        df["finger_key"]="unk"
    df["finger_key"].apply(lambda finger:\
        tag_helper.add_tag_for_target_toNebula("基线异常使用指纹",finger,task_id))
    df.apply(lambda session: \
        tag_helper.add_tag_for_session(tag_text="指纹基线异常会话",session=session),axis=1)
    df.groupby(["sIp","dIp","finger_key"],as_index=False).apply(lambda df:
        alarm_helper.add_alarm_of_sessions("指纹基线异常",df,["sIp","dIp","finger_key"]))

def check_finger_of_conf(task_id,batch_id,ip,conf,gk_embedding):
    r = check_access_abnormal_fingers(batch_id,match_dict={"sIp":ip},finger_key=conf["finger_key"],es_type=conf["es_type"],gk_embedding=gk_embedding)
    if len(r)==0:
        return
    new_match_dict = {"sIp":ip,conf["finger_key"]:r}
    table,detail = bh.get_detect_query(batch_id,match_dict=new_match_dict,es_type=conf["es_type"])
    df = eh.load_es_data_to_DF(table,detail)
    write_finger_alarm(df,conf["finger_key"],task_id)

def check_fingers(task_id,batch_id,conf):
    table,base_detail = bh.get_detect_query(batch_id,match_dict={})
    base_detail["query"]["bool"]["must"].append({"terms": {"AppName.keyword":["APP_SSL","APP_HTTP"]}})
    ips = get_direction_ip_list(table,base_detail,ip_net=common.InnerNet,direction="sIp")

    finger_df = load_sip_to_fingers(task_id,conf["es_type"],conf["finger_key"])
    # finger_df.to_csv("tmp/finger_ip.csv")
    gk_embedding = GK_Finger_Embedding(df=finger_df)
    for ip in ips:        
        check_finger_of_conf(task_id,batch_id,ip,conf,gk_embedding)

def check_finger_baseline(task_id,batch_id):
    check_dict = [
        {"finger_key":"cSSLFinger.keyword","es_type":"ssl"},
        {"finger_key":"Client.User-Agent.keyword","es_type":"http"}
    ]
    for conf in check_dict:
        check_fingers(task_id,batch_id,conf)
        

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("connect*")
    check_finger_baseline(task_id,batch_id)
    