{{- if .Values.flink.jobs.sessionLabelsCdcSync.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: flink-session-labels-cdc-sync
  namespace: {{ .Values.namespace }}
  labels:
    app: flink-session-labels-cdc
    component: job
    version: {{ .Chart.AppVersion }}
spec:
  template:
    metadata:
      labels:
        app: flink-session-labels-cdc
        component: job
    spec:
      restartPolicy: OnFailure
      containers:
      - name: flink-session-labels-cdc
        image: {{ .Values.flink.jobs.sessionLabelsCdcSync.image.repository }}:{{ .Values.flink.jobs.sessionLabelsCdcSync.image.tag | default .Chart.AppVersion }}
        imagePullPolicy: {{ .Values.flink.jobs.sessionLabelsCdcSync.image.pullPolicy | default "IfNotPresent" }}
        
        command:
        - /opt/flink/bin/flink
        - run
        - --detached
        - --jobmanager
        - flink-jobmanager:8081
        - /opt/flink/usrlib/session-labels-cdc-sync.jar
        
        env:
        - name: POSTGRESQL_USERNAME
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: username
        - name: POSTGRESQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: password
        - name: DORIS_USERNAME
          valueFrom:
            secretKeyRef:
              name: doris-credentials
              key: username
        - name: DORIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: doris-credentials
              key: password
        - name: FLINK_PROPERTIES
          value: |
            jobmanager.rpc.address: flink-jobmanager
            jobmanager.rpc.port: 6123
            
        volumeMounts:
        - name: config-volume
          mountPath: /opt/flink/conf/application.yaml
          subPath: application.yaml
        - name: config-volume
          mountPath: /opt/flink/conf/log4j2.xml
          subPath: log4j2.xml
        - name: checkpoints
          mountPath: /opt/flink/checkpoints
        - name: savepoints
          mountPath: /opt/flink/savepoints
          
        resources:
          requests:
            memory: {{ .Values.flink.jobs.sessionLabelsCdcSync.resources.requests.memory | default "512Mi" }}
            cpu: {{ .Values.flink.jobs.sessionLabelsCdcSync.resources.requests.cpu | default "200m" }}
          limits:
            memory: {{ .Values.flink.jobs.sessionLabelsCdcSync.resources.limits.memory | default "1Gi" }}
            cpu: {{ .Values.flink.jobs.sessionLabelsCdcSync.resources.limits.cpu | default "500m" }}
            
        livenessProbe:
          httpGet:
            path: /jobs
            port: 8081
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
          
        readinessProbe:
          httpGet:
            path: /jobs
            port: 8081
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
          
      volumes:
      - name: config-volume
        configMap:
          name: flink-session-labels-cdc-config
      - name: checkpoints
        persistentVolumeClaim:
          claimName: flink-checkpoints-pvc
      - name: savepoints
        persistentVolumeClaim:
          claimName: flink-savepoints-pvc
          
      nodeSelector:
        {{- toYaml .Values.flink.jobs.sessionLabelsCdcSync.nodeSelector | nindent 8 }}
        
      tolerations:
        {{- toYaml .Values.flink.jobs.sessionLabelsCdcSync.tolerations | nindent 8 }}
        
      affinity:
        {{- toYaml .Values.flink.jobs.sessionLabelsCdcSync.affinity | nindent 8 }}
{{- end }}
