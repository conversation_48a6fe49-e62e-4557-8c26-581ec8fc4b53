import sys
sys.path.append("./")

from PyGksec.GkHelper.GkAnayTaskHelper import G_AnayModel
from PyGksec.GkHelper.KnowledgeHelper import Benign_DNS_Servers
from PyGksec.GkUtils import common

import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.ESHelper as eh

import pandas as pd

AbnormalDNS_File = "tmp/abnormal_dns_server.csv"


def check_dns_server(task_id,batch_id,dns_server):
    """
        确认DNS解析服务器是否异常
    """
    table,detail = eh.get_basic_query(es_type="connectinfo",batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"term": {"dIp":dns_server}})  
    detail["query"]["bool"]["must"].append({"term": {"AppName.keyword":"APP_DNS"}})  
    sbyte_agg = eh.make_stats_agg("pkt.sBytes","sbytes")
    detail["aggs"] = sbyte_agg
    r = eh.load_agg_data(table,detail,"sbytes",agg_type="stat")
    if r["sum"] and r["sum"] > 5 * common.MB:
        print(dns_server)
        tag_helper.add_tag_for_target_toNebula(tag_text="异常DNS服务器",target=dns_server,task_id=task_id)



def get_abnormal_dns_servers(dns_data_gen):
    """
        筛选时间范围异常的解析服务器地址
    """
    result = []
    for data in dns_data_gen:
        result += data
    if len(result)==0:
        return
    df = pd.DataFrame(result)
    dns_server_cnt = df[["key","agg_time"]].groupby("key",as_index=False).count()
    abnormal_servers = list(dns_server_cnt[dns_server_cnt.agg_time<=2].key)
    return abnormal_servers
    

def detect_abnormal_dns_server(task_id,batch_id):
    """
        检测异常的DNS解析服务器
    """
    agg_key="dns_server"
    benign_servers = list(Benign_DNS_Servers)
    table,detail = eh.get_basic_query(es_type="dns",batch_id=batch_id,actual_time=False)
    detail["query"]["bool"]["must_not"].append({"terms": {"sIp":benign_servers}})  
    detail["query"]["bool"]["must_not"].append({"terms": {"dIp":benign_servers}})  
    dns_agg = eh.make_term_agg("dIp",agg_key)
    dns_agg = eh.filter_doc_count(dns_agg,agg_key,min_doc=100)
    detail["aggs"] = dns_agg

    data_gen = eh.load_agg_data_of_split_time(table,detail,agg_key,time_stack=common.DAY_SECOND)
    abnormal_servers = get_abnormal_dns_servers(data_gen)
    if abnormal_servers is None:
        return
    for dns_server in abnormal_servers:
        check_dns_server(task_id,batch_id,dns_server)
    


class DNS_ServerDetectTask(G_AnayModel):
     def run(self,task_id,batch_id):
        detect_abnormal_dns_server(task_id,batch_id)
    


if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("dns*")
    detect_abnormal_dns_server(task_id,batch_id)