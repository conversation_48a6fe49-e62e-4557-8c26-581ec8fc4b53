# knowledge-base-updater-cronjob.yaml
# 知识库自动更新CronJob
# 定期从外部数据源更新知识库数据文件

{{- if and .Values.infrastructure.flink.enabled .Values.knowledgeBase.autoUpdate.enabled }}
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "nta.fullname" . }}-knowledge-base-updater
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: knowledge-base-updater
spec:
  # 每天夜里23点执行更新（确保当天数据已发布）
  schedule: "{{ .Values.knowledgeBase.autoUpdate.schedule | default "0 23 * * *" }}"
  # 保留最近3次执行记录
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  # 并发策略：禁止并发执行
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      # 作业超时时间：2小时
      activeDeadlineSeconds: 7200
      template:
        metadata:
          labels:
            {{- include "nta.selectorLabels" . | nindent 12 }}
            app.kubernetes.io/component: knowledge-base-updater
        spec:
          restartPolicy: OnFailure
          serviceAccountName: {{ include "nta.fullname" . }}-knowledge-base-updater
          containers:
          - name: knowledge-base-updater
            image: "{{ .Values.global.registry }}/nta/knowledge-base-updater:{{ .Values.global.tag }}"
            imagePullPolicy: IfNotPresent
            env:
            - name: NAMESPACE
              value: {{ .Release.Namespace }}
            - name: CONFIGMAP_PREFIX
              value: {{ include "nta.fullname" . }}
            - name: UPDATE_SOURCES
              value: "{{ join "," .Values.knowledgeBase.autoUpdate.sources }}"
            # MinIO配置 - 用于存储更新的知识库文件
            - name: MINIO_ENDPOINT
              value: "{{ .Values.global.minio.endpoint }}"
            - name: MINIO_BUCKET
              value: "{{ .Values.global.minio.defaultBucket }}"
            - name: MINIO_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.fullname" . }}-minio-credentials
                  key: access-key
            - name: MINIO_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "nta.fullname" . }}-minio-credentials
                  key: secret-key
            # 外部数据源配置
            {{- if .Values.knowledgeBase.dataSources.tranco.enabled }}
            - name: TRANCO_LIST_ID
              value: "{{ .Values.knowledgeBase.dataSources.tranco.listId }}"
            {{- end }}
            # GeoIP数据源URL
            {{- if .Values.knowledgeBase.dataSources.geoip.enabled }}
            - name: GEOIP_CITY_URL
              value: "https://git.io/GeoLite2-City.mmdb"
            - name: GEOIP_ASN_URL
              value: "https://git.io/GeoLite2-ASN.mmdb"
            {{- end }}
            volumeMounts:
            - name: temp-storage
              mountPath: /tmp/knowledge-base
            resources:
              requests:
                memory: "256Mi"
                cpu: "100m"
              limits:
                memory: "512Mi"
                cpu: "500m"
          volumes:
          - name: temp-storage
            emptyDir:
              sizeLimit: "2Gi"

---
# ServiceAccount for knowledge base updater
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "nta.fullname" . }}-knowledge-base-updater
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: knowledge-base-updater

---
# ClusterRole for knowledge base updater
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "nta.fullname" . }}-knowledge-base-updater
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: knowledge-base-updater
rules:
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "create", "update", "patch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["create", "delete", "get", "list"]
- apiGroups: [""]
  resources: ["pods/exec"]
  verbs: ["create"]
- apiGroups: [""]
  resources: ["persistentvolumeclaims"]
  verbs: ["get", "list"]
- apiGroups: ["apps"]
  resources: ["deployments"]
  verbs: ["get", "list", "patch"]
- apiGroups: ["flink.apache.org"]
  resources: ["flinkdeployments"]
  verbs: ["get", "list", "patch"]

---
# ClusterRoleBinding for knowledge base updater
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "nta.fullname" . }}-knowledge-base-updater
  labels:
    {{- include "nta.labels" . | nindent 4 }}
    app.kubernetes.io/component: knowledge-base-updater
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "nta.fullname" . }}-knowledge-base-updater
subjects:
- kind: ServiceAccount
  name: {{ include "nta.fullname" . }}-knowledge-base-updater
  namespace: {{ .Release.Namespace }}

{{- end }}
