{{- if and .Values.infrastructure.doris.enabled .Values.infrastructure.doris.operator.enabled .Values.initialization.doris.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: doris-init-scripts
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: doris-init-scripts
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
data:
  {{- range $path, $_ := .Files.Glob (printf "%s/sql/doris/[0-9]*.sql" .Values.global.helmFilesPath) }}
  {{ base $path }}: |-
{{ $.Files.Get $path | indent 4 }}
  {{- end }}
{{- end }}
