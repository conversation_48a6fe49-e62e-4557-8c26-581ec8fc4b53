{{- if and .Values.infrastructure.elasticsearch.enabled .Values.infrastructure.elasticsearch.operator.enabled .Values.initialization.enabled .Values.initialization.elasticsearch.enabled -}}
{{- $jobConfig := dict "name" "cert-system-data-import-job" -}}
{{- $_ := set $jobConfig "namespace" .Values.global.namespace -}}
{{- $_ := set $jobConfig "registry" .Values.global.registry -}}
{{- $_ := set $jobConfig "fullImage" (printf "%s/python:3.8-alpine" .Values.global.registry) -}}
{{- $_ := set $jobConfig "resources" .Values.initialization.resources -}}
{{- $_ := set $jobConfig "command" (list "/bin/sh") -}}
{{- $_ := set $jobConfig "args" (list "-c" "pip install elasticsearch==7.17.14 && python /app/config/cert_system_import.py") -}}
{{- $_ := set $jobConfig "configMapName" "cert-system-init-config" -}}
{{- $_ := set $jobConfig "annotations" (dict "helm.sh/hook" "post-install,post-upgrade" "helm.sh/hook-weight" "0" "helm.sh/hook-delete-policy" "before-hook-creation,hook-succeeded") -}}
{{- $_ := set $jobConfig "configMountPath" "/app/config" -}}
{{- $_ := set $jobConfig "backoffLimit" 5 -}}
{{- $_ := set $jobConfig "activeDeadlineSeconds" 1200 -}}

{{- $esHost := .Values.infrastructure.elasticsearch.host -}}
{{- $esPort := .Values.infrastructure.elasticsearch.port -}}

{{- $dependencies := list (dict "name" "elasticsearch" "host" $esHost "port" $esPort) -}}
{{- $_ := set $jobConfig "dependencies" $dependencies -}}

{{- $env := dict -}}
{{- $_ := set $env "ES_HOST" $esHost -}}
{{- $_ := set $env "ES_PORT" $esPort -}}
{{- $_ := set $env "ES_USER" .Values.infrastructure.elasticsearch.credentials.username -}}
{{- $_ := set $env "ES_PASSWORD" .Values.infrastructure.elasticsearch.credentials.password.value -}}
{{- $_ := set $jobConfig "env" $env -}}

{{- include "nta.initJob" $jobConfig -}}
{{- end -}}
