import sys,os,pymysql.cursors,xml.etree.ElementTree as  ET,json,time
def write_file(filename , context):
    os.system("echo \"\">"+filename)
    fnew = open(filename, "w")
    fnew.write(context)
    fnew.close()
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
def insert_batch():
    sql = "insert ignore into tb_task_batch (batch_id,task_id , batch_remark , fullflow_state , flowlog_state , data_type , begin_time ,end_time ,data_begin_time , \
            data_end_time ,batch_bytes , batch_session ,batch_alarm,importrarnt_target ,batch_dir ,report_path) select task_id ,task_id ,\"\", \
    \"ON\",\"ON\",3,unix_timestamp(now()),unix_timestamp(now()),unix_timestamp(now()),unix_timestamp(now()),0,0,0,0,\"\",\"\" from tb_task_analysis where task_state =1"
    idu_mysql(sql,cursor,db)

insert_batch()
