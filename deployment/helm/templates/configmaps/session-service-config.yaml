{{- if .Values.services.session-service.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: session-service-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  application.yml: |
    # 会话服务配置

    # 服务器配置
    server:
      port: 8096
      servlet:
        context-path: /session

    # 应用名称
    spring:
      application:
        name: session-service

      # 数据库连接配置 - 会话服务主要查询网络会话数据
      datasource:
        # 主数据源：Doris - 查询网络流量会话数据
        primary:
          url: jdbc:mysql://{{ include "nta.dorisConfig" . | fromYaml | get "feNodes" | split "," | first | split ":" | first }}:9030/nta?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
          username: ${DORIS_USERNAME}
          password: ${DORIS_PASSWORD}
          driver-class-name: com.mysql.cj.jdbc.Driver
        # 辅助数据源：PostgreSQL - 查询标签定义等元数据
        secondary:
          url: jdbc:postgresql://{{ include "nta.postgresqlConfig" . | fromYaml | get "host" }}:{{ include "nta.postgresqlConfig" . | fromYaml | get "port" }}/{{ include "nta.postgresqlConfig" . | fromYaml | get "database" }}?useUnicode=true&characterEncoding=utf-8&useSSL=false&serverTimezone=Asia/Shanghai
          username: ${DB_USERNAME}
          password: ${DB_PASSWORD}
          driver-class-name: org.postgresql.Driver

      # Redis配置
      redis:
        host: ${REDIS_HOST}
        port: ${REDIS_PORT}
        password: ${REDIS_PASSWORD}
        database: 1  # 使用数据库1存储会话信息
        timeout: 10000
        lettuce:
          pool:
            max-active: 20
            max-idle: 10
            min-idle: 5

      # Session配置
      session:
        store-type: redis
        redis:
          namespace: "nta:session"
          flush-mode: on_save

    # MyBatis类型别名包配置
    mybatis-plus:
      type-aliases-package: com.geeksec.session.entity

    # 数据存储配置
    storage:
      # Doris配置 - 网络流量会话数据
      doris:
        ods-sessions-table: ods_single_session_logs
        dwd-sessions-table: dwd_session_logs
        query-timeout: 30000
      # PostgreSQL配置 - 标签定义和会话标签关联
      postgresql:
        labels-table: labels                    # 统一标签表
        session-labels-table: session_labels   # 会话标签关联表
        batch-size: 100

    # 网络会话服务特定配置
    session:
      # 查询配置
      query:
        # 默认查询时间范围（小时）
        default-time-range: 24
        # 最大查询时间范围（天）
        max-time-range: 30
        # 默认分页大小
        default-page-size: 100
        # 最大分页大小
        max-page-size: 10000
      # 分析配置
      analysis:
        # 是否启用实时分析
        real-time-analysis: true
        # 是否启用协议识别
        protocol-detection: true
        # 是否启用指纹分析
        fingerprint-analysis: true
        # 是否启用异常检测
        anomaly-detection: true
      # 标签配置
      labels:
        # 会话标签的目标类型
        target-type: "SESSION"
        # 是否启用标签功能
        enabled: true
        # 标签查询配置
        query:
          # 是否只显示激活的标签
          active-only: true
          # 默认排序方式
          default-sort: "sort_order, display_name"
      # 缓存配置
      cache:
        # 是否启用查询缓存
        enabled: true
        # 缓存过期时间（秒）
        ttl: 300  # 5分钟
        # 最大缓存条目数
        max-entries: 10000
      # 导出配置
      export:
        # 支持的导出格式
        formats: ["csv", "json", "pcap"]
        # 最大导出记录数
        max-records: 1000000
        # 导出文件保留时间（小时）
        file-retention-hours: 24

    # 生产环境特定配置
    knife4j:
      production: {{ eq .Values.global.springProfiles "prod" }}

    # 服务配置
    services:
      auth-service:
        url: http://auth-service:8081
{{- end -}}
