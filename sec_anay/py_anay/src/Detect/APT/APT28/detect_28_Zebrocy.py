import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper
import PyGksec.GkHelper.AlarmHelper as alarm_helper

import pandas as pd


def addAPT28LabelByCert(cert,task_id,batch_id):
    table,detail = eh.get_basic_query(es_type="ssl",batch_id=batch_id)
    detail["query"]["bool"]["must"].append({"term": {"sCertHash.keyword":cert}})
    result = eh.load_es_data_to_DF(table,detail=detail,outfile="tmp/apt28.csv")
    if result is None or result.shape[0]==0:
        return
    result.apply(lambda x : tag_helper.add_tag_for_session(session=x,tag_text="APT28 Zebrocy控制会话"),axis=1)
    result.apply(lambda x : tag_helper.add_tag_for_session(session=x,tag_text="APT28 Zebrocy控制会话"),axis=1)
    #result.dIp.map(lambda x : tag_helper.add_tag_for_target_toNebula(target=x ,tag_text="命令与控制服务器",task_id=task_id))
    # result.groupby(["sIp","dIp"],as_index=False).\
    #     filter(lambda df : alarm_helper.addAlarmOfSessions\
    #         ("APT29特种木马",df,cols=["dIp","SessionId"],batch_id=batch_id))
            
def detect_Zebrocy(task_id,batch_id):
    tags = ["免费证书","新签发应用证书","冷门顶级域名证书"]
    tag_ids = [tag_helper.Tag_Text_Map[tag_text]["Tag_Id"] for tag_text in tags]
    certs = get_cert_from_ES(table="cert_user",tag_ids=tag_ids)
    for cert in (certs):
        addAPT28LabelByCert(cert,task_id,batch_id)

def get_cert_from_ES(table,tag_ids):
    detail = eh.get_default_query()
    detail["query"]["bool"]["must"].append({"terms": {"Labels":tag_ids}})
    eh.describe(table,detail)
    df = eh.load_es_data_to_DF(table,detail,ignore_file=True)
    if df is None:
        return []
    return list(set(df["ASN1SHA1"].tolist()))

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    detect_Zebrocy(task_id,batch_id)