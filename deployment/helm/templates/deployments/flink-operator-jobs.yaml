{{- if .Values.infrastructure.flink.enabled -}}
{{- if and .Values.infrastructure.flink.operator.enabled -}}
{{- $flinkOperatorDep := (index (dict "name" "flink-kubernetes-operator") "name") -}}
{{- range .Chart.Dependencies -}}
  {{- if eq .Name $flinkOperatorDep -}}
    {{- $flinkOperatorVersion := .Version -}}
    {{- $minVersion := "1.11.0" -}}
    {{- if semverCompare "< $minVersion" $flinkOperatorVersion -}}
      {{- fail (printf "Flink Kubernetes Operator version %s is too old. Minimum required version is %s" $flinkOperatorVersion $minVersion) -}}
    {{- end -}}
  {{- end -}}
{{- end -}}
{{- end -}}

{{- if .Values.infrastructure.flink.jobs.session-threat-detector.enabled -}}
---
{{- $deploymentConfig := dict "name" "flink-session-threat-detector" -}}
{{- $_ := set $deploymentConfig "namespace" .Values.global.namespace -}}
{{- $_ := set $deploymentConfig "image" .Values.infrastructure.flink.jobs.session-threat-detector.image -}}
{{- $_ := set $deploymentConfig "registry" .Values.global.registry -}}
{{- $_ := set $deploymentConfig "tag" .Values.infrastructure.flink.jobs.session-threat-detector.image.tag | default .Values.global.tag -}}
{{- $_ := set $deploymentConfig "className" .Values.infrastructure.flink.jobs.session-threat-detector.className -}}
{{- $_ := set $deploymentConfig "parallelism" .Values.infrastructure.flink.jobs.session-threat-detector.parallelism -}}
{{- $_ := set $deploymentConfig "resources" .Values.infrastructure.flink.jobs.session-threat-detector.resources -}}
{{- include "nta.flinkDeployment" $deploymentConfig -}}
{{- end -}}


{{- if .Values.infrastructure.flink.jobs.certificate-analyzer.enabled -}}
---
{{- $deploymentConfig := dict "name" "flink-certificate-analyzer" -}}
{{- $_ := set $deploymentConfig "namespace" .Values.global.namespace -}}
{{- $_ := set $deploymentConfig "image" .Values.infrastructure.flink.jobs.certificate-analyzer.image -}}
{{- $_ := set $deploymentConfig "registry" .Values.global.registry -}}
{{- $_ := set $deploymentConfig "tag" .Values.infrastructure.flink.jobs.certificate-analyzer.image.tag | default .Values.global.tag -}}
{{- $_ := set $deploymentConfig "className" .Values.infrastructure.flink.jobs.certificate-analyzer.className -}}
{{- $_ := set $deploymentConfig "parallelism" .Values.infrastructure.flink.jobs.certificate-analyzer.parallelism -}}
{{- $_ := set $deploymentConfig "resources" .Values.infrastructure.flink.jobs.certificate-analyzer.resources -}}
{{- include "nta.flinkDeployment" $deploymentConfig -}}
{{- end -}}

{{- if .Values.infrastructure.flink.jobs.traffic-etl-processor.enabled -}}
---
{{- $deploymentConfig := dict "name" "flink-traffic-etl-processor" -}}
{{- $_ := set $deploymentConfig "namespace" .Values.global.namespace -}}
{{- $_ := set $deploymentConfig "image" .Values.infrastructure.flink.jobs.traffic-etl-processor.image -}}
{{- $_ := set $deploymentConfig "registry" .Values.global.registry -}}
{{- $_ := set $deploymentConfig "tag" .Values.infrastructure.flink.jobs.traffic-etl-processor.image.tag | default .Values.global.tag -}}
{{- $_ := set $deploymentConfig "className" .Values.infrastructure.flink.jobs.traffic-etl-processor.className -}}
{{- $_ := set $deploymentConfig "parallelism" .Values.infrastructure.flink.jobs.traffic-etl-processor.parallelism -}}
{{- $_ := set $deploymentConfig "resources" .Values.infrastructure.flink.jobs.traffic-etl-processor.resources -}}
{{- include "nta.flinkDeployment" $deploymentConfig -}}
{{- end -}}

{{- if .Values.infrastructure.flink.jobs.alarm-processor.enabled -}}
---
{{- $deploymentConfig := dict "name" "flink-alarm-processor" -}}
{{- $_ := set $deploymentConfig "namespace" .Values.global.namespace -}}
{{- $_ := set $deploymentConfig "image" .Values.infrastructure.flink.jobs.alarm-processor.image -}}
{{- $_ := set $deploymentConfig "registry" .Values.global.registry -}}
{{- $_ := set $deploymentConfig "tag" .Values.infrastructure.flink.jobs.alarm-processor.image.tag | default .Values.global.tag -}}
{{- $_ := set $deploymentConfig "className" .Values.infrastructure.flink.jobs.alarm-processor.className -}}
{{- $_ := set $deploymentConfig "parallelism" .Values.infrastructure.flink.jobs.alarm-processor.parallelism -}}
{{- $_ := set $deploymentConfig "resources" .Values.infrastructure.flink.jobs.alarm-processor.resources -}}
{{- include "nta.flinkDeployment" $deploymentConfig -}}
{{- end -}}

{{- end -}}
