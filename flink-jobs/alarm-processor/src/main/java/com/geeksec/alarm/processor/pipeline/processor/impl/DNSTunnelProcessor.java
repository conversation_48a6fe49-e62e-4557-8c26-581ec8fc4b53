package com.geeksec.alarm.processor.pipeline.processor.impl;

import com.geeksec.alarm.processor.model.Alarm;
import com.geeksec.alarm.processor.model.AlarmType;
import com.geeksec.alarm.processor.pipeline.processor.AbstractAlarmTypeProcessor;
import com.geeksec.common.knowledge.KnowledgeBaseClient;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * DNS隧道告警处理器
 * 
 * <AUTHOR>
 * @since 3.0.0
 */
@Slf4j
public class DNSTunnelProcessor extends AbstractAlarmTypeProcessor {
    
    private static final long serialVersionUID = 1L;
    
    @Override
    public AlarmType getSupportedType() {
        return AlarmType.DNS_TUNNEL;
    }
    
    @Override
    public List<Alarm.DetectionReason> generateReasonAnalysis(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Alarm.DetectionReason> reasons = new ArrayList<>();
        
        String suspiciousDomain = getSuspiciousDomain(alarm);
        String queryType = getDnsQueryType(alarm);
        
        // DNS隧道通信检测
        reasons.add(createDetectionReason(
                "DNS隧道通信",
                "检测到通过DNS协议进行的隐蔽数据传输",
                "正常DNS查询",
                String.format("可疑域名: %s", suspiciousDomain),
                "标准域名查询",
                8
        ));
        
        // 异常DNS查询模式
        if (hasAbnormalQueryPattern(alarm)) {
            reasons.add(createDetectionReason(
                    "异常DNS查询模式",
                    String.format("检测到异常的%s查询模式", queryType),
                    "正常DNS查询频率",
                    String.format("查询频率: %s", getQueryFrequency(alarm)),
                    "低频域名查询",
                    7
            ));
        }
        
        // 长域名检测
        if (hasLongDomainName(alarm)) {
            reasons.add(createDetectionReason(
                    "异常长域名",
                    "检测到异常长的域名，可能用于数据编码传输",
                    "正常长度域名",
                    String.format("域名长度: %d", getDomainLength(alarm)),
                    "标准域名长度(<50字符)",
                    6
            ));
        }
        
        // 编码数据检测
        if (hasEncodedData(alarm)) {
            reasons.add(createDetectionReason(
                    "编码数据传输",
                    "域名中包含疑似编码的数据",
                    "正常域名格式",
                    "Base64/Hex编码特征",
                    "标准域名字符",
                    8
            ));
        }
        
        return reasons;
    }
    
    @Override
    public Alarm.HandlingSuggestions generateHandlingSuggestions(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        String suspiciousDomain = getSuspiciousDomain(alarm);
        
        return Alarm.HandlingSuggestions.builder()
                .immediateActions(Arrays.asList(
                        String.format("立即阻断对%s域名的DNS查询", suspiciousDomain),
                        "检查DNS服务器配置和日志",
                        "分析DNS查询的数据内容",
                        "监控相关主机的其他网络活动"
                ))
                .investigationSteps(Arrays.asList(
                        "分析DNS查询中编码的数据内容",
                        "确认DNS隧道的建立时间和持续时间",
                        "检查是否有敏感数据通过DNS泄露",
                        "分析DNS隧道工具的类型和版本",
                        "确认隧道使用者的身份和目的"
                ))
                .preventionMeasures(Arrays.asList(
                        "加强DNS流量监控和异常检测",
                        "限制DNS查询的频率和大小",
                        "实施DNS域名白名单策略",
                        "部署专门的DNS隧道检测工具",
                        "定期审查DNS服务器配置"
                ))
                .recoverySteps(Arrays.asList(
                        "清理DNS缓存和恶意域名记录",
                        "重新配置DNS服务器安全策略",
                        "修复被DNS隧道利用的系统漏洞",
                        "恢复正常的DNS解析服务",
                        "清除DNS隧道相关的恶意软件",
                        "重新建立安全的DNS通信机制"
                ))
                .build();
    }
    
    @Override
    public String getAlarmPrinciple(Alarm alarm) {
        return "攻击者利用DNS协议的特性，将数据编码到DNS查询和响应中，" +
                "绕过传统的网络安全设备，实现隐蔽的数据传输或C&C通信。";
    }
    
    @Override
    public String getDetectionPrinciple(Alarm alarm) {
        return "通过分析DNS查询的频率、域名长度、字符分布、查询类型等特征，" +
                "结合机器学习算法识别异常的DNS流量模式。";
    }
    
    @Override
    public List<Map<String, Object>> generateAttackRoute(Alarm alarm, KnowledgeBaseClient knowledgeBaseClient) {
        List<Map<String, Object>> attackRoute = new ArrayList<>();
        
        // DNS隧道建立
        Map<String, Object> step1 = new HashMap<>();
        step1.put("step", 1);
        step1.put("action", "DNS隧道建立");
        step1.put("description", "通过特制DNS查询建立隐蔽通信通道");
        step1.put("domain", getSuspiciousDomain(alarm));
        attackRoute.add(step1);
        
        // 数据编码传输
        Map<String, Object> step2 = new HashMap<>();
        step2.put("step", 2);
        step2.put("action", "数据编码传输");
        step2.put("description", "将数据编码到DNS查询中进行传输");
        step2.put("encoding", getEncodingType(alarm));
        attackRoute.add(step2);
        
        return attackRoute;
    }
    
    @Override
    public String getModelId(Alarm alarm) {
        return "99091";
    }
    
    @Override
    public boolean requiresSpecialHandling(Alarm alarm) {
        return true; // DNS隧道需要特殊处理
    }
    
    /**
     * 获取可疑域名
     */
    private String getSuspiciousDomain(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object domain = alarm.getExtendedProperties().get("suspicious_domain");
            if (domain != null) {
                return domain.toString();
            }
        }
        return "未知域名";
    }
    
    /**
     * 获取DNS查询类型
     */
    private String getDnsQueryType(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object queryType = alarm.getExtendedProperties().get("dns_query_type");
            if (queryType != null) {
                return queryType.toString();
            }
        }
        return "A";
    }
    
    /**
     * 检查是否有异常查询模式
     */
    private boolean hasAbnormalQueryPattern(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object abnormal = alarm.getExtendedProperties().get("has_abnormal_pattern");
            if (abnormal instanceof Boolean) {
                return (Boolean) abnormal;
            }
        }
        return false;
    }
    
    /**
     * 获取查询频率
     */
    private String getQueryFrequency(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object frequency = alarm.getExtendedProperties().get("query_frequency");
            if (frequency != null) {
                return frequency.toString();
            }
        }
        return "未知";
    }
    
    /**
     * 检查是否有长域名
     */
    private boolean hasLongDomainName(Alarm alarm) {
        String domain = getSuspiciousDomain(alarm);
        return domain.length() > 50;
    }
    
    /**
     * 获取域名长度
     */
    private int getDomainLength(Alarm alarm) {
        String domain = getSuspiciousDomain(alarm);
        return domain.length();
    }
    
    /**
     * 检查是否有编码数据
     */
    private boolean hasEncodedData(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object encoded = alarm.getExtendedProperties().get("has_encoded_data");
            if (encoded instanceof Boolean) {
                return (Boolean) encoded;
            }
        }
        return false;
    }
    
    /**
     * 获取编码类型
     */
    private String getEncodingType(Alarm alarm) {
        if (alarm.getExtendedProperties() != null) {
            Object encoding = alarm.getExtendedProperties().get("encoding_type");
            if (encoding != null) {
                return encoding.toString();
            }
        }
        return "未知编码";
    }
}
