#!/bin/bash
task_id=$1
batch_id=$2
cd /opt/GeekSec/web/rule_syn/
#echo "thdd stop : `date`" >> /opt/GeekSec/web/thdd.log
python3 /opt/GeekSec/web/rule_syn/th_restart/th_stop.py $task_id
#echo "rule_syn.py begin : `date`" >> /opt/GeekSec/web/thdd.log
python3 /opt/GeekSec/web/rule_syn/rule_syn.py  $task_id
#echo "rule_syn.py end : `date`" >> /opt/GeekSec/web/thdd.log
python3 /opt/GeekSec/web/rule_syn/th_restart/th_start.py $task_id
#echo "thdd start : `date`" >> /opt/GeekSec/web/thdd.log
