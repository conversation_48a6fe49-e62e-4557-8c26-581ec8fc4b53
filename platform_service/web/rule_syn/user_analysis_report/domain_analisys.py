# Last Update:2020-04-23 14:10:00
##
# @file domain_analisys.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2020-02-09


from elasticsearch import Elasticsearch
import pymysql 
import json
import time 
import sys,os
import re
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.pagesizes import letter
import matplotlib as mpl
mpl.use('Agg')
import matplotlib.pyplot as plt
import numpy as np


from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak, Table, TableStyle
from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
from reportlab.lib.units import mm
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics

from analisys_common_tools import list_to_pdftable,list_to_pdflist,drew_heatmap_png,drew_barplot_png,time_to_str,time_to_hour_str,time_to_all_str

mpl.rcParams['font.sans-serif']=['SimHei']  #设置为黑体字

# 注册字体
pdfmetrics.registerFont(TTFont('msyh', '/opt/GeekSec/web/rule_syn/Fonts/msyh.ttc'))
pdfmetrics.registerFont(TTFont('msyhbd', '/opt/GeekSec/web/rule_syn/Fonts/msyhbd.ttc'))
pdfmetrics.registerFont(TTFont('msyhl', '/opt/GeekSec/web/rule_syn/Fonts/msyhl.ttc'))

max_time  = 1507992307
def get_max_time():
    global max_time
    max_time = int(time.time())
os.system("mkdir  -p png/")
# 两个值的柱状图
def show_two_value_bar(name, y1 ,y2 ,label,png_file_name):
    x = np.arange(len(name))
    width = 0.25
    label_len = len(label)
    print(x,label_len)
    #plt = plt()
    plt.bar(x, y1,  width=width, label=label[0],color='darkorange')
    if (label_len == 2):
        plt.bar(x + width, y2, width=width, label=label[1], color='deepskyblue', tick_label=name)

    # 显示在图形上的值
    for a, b in zip(x,y1):
        plt.text(a, b, b, ha='center', va='bottom')
    if (label_len == 2):
        for a,b in zip(x,y2):
            plt.text(a+width, b, b, ha='center', va='bottom')
    plt.xticks()
    #plt.legend(loc="upper left")  # 防止label和图像重合显示不出来
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.ylabel('value')
    plt.xlabel('line')
    plt.rcParams['savefig.dpi'] = 100  # 图片像素
    plt.rcParams['figure.dpi'] = 20  # 分辨率
    plt.rcParams['figure.figsize'] = (15.0, 15.0)  # 尺寸
    plt.title("title")
    plt.savefig(png_file_name)
    plt.show()
    plt.clf()


base_json = {}

file_name = "" #+ "ip_analisys_" + str(task_id) + "_" + ip +"_"+str(time.time()) +".pdf"
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password='root',db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
cursors = cursor
es_ip = base_json["es_es"]
es = Elasticsearch([es_ip])
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()

argv_json = {}
relsert_json = {}
report_path = "/var/ftp/task_report/"
 

domain_base_info_list = []
domain_attr_info_list = []
s_domain_corr_task_list = []
domain_bother_info_list = []
domain_aralm_info_list = []
domain_corr_domain = []
domain_corr_task_list =[] 
domain_parse_server_list = []
# dns 
relsert_parse_domain = []
relsert_parse_ip = []
domain_pasre_to_domain_list = []
dns_alarm_session = []
# http 
http_task_defona_list = []
http_domain_parse_server_list = []
http_alarm_session = []
http_domain_corr_finger = []


# ssl 
ssl_task_defona_list = []
ssl_parse_server_list = []
cert_parse_server_list = []
ssl_alarm_session=[]
certlist_parse_server_list = []
ssl_domain_corr_finger = []

class Graphs:
    def __init__(self):
        pass
    # 绘制标题
    @staticmethod
    def draw_title(name_title, fontName, fontSize, leading, alignment):
        style = getSampleStyleSheet()
        ct = style['Normal']
        ct.fontName = fontName
        ct.fontSize = fontSize
        #  设置行距
        ct.leading = leading
        #  颜色
        ct.textColor = colors.black
        #  居中
        ct.alignment = alignment
        #  添加标题并居中
        title = Paragraph(name_title, ct)
        return title
def argv_parse(argv) :
    global file_name
    global argv_json
    print(len(argv))
    if len(argv) !=3:
        print("参数错误")
        sys.exit(1)
    str1 = argv[1]
    file_name = argv[2]
    print(str1)
    argv_json= json.loads(str(str1))
    value = argv_json['target_value'] 
    argv_json['value_type'] = 1 # 单IP
    vaulelist = value.split(",")
    if len(vaulelist) > 1 :
        argv_json['value_type'] =  2  #  列举
    else :
        vaulelist = value.split("--")
        if len(vaulelist) == 1 :
            argv_json['value_type'] = 3  # IP 区域 
    if 'task_id' not in  argv_json:
        argv_json['task_id'] = 0
client_hot = "" 
def client_hot_dst(n_client_hot):
    client_hot =1
    return  client_hot  
def client_hot_dst_t(n_client_hot):
    client_hot =1
    return  client_hot  

father_domain = ""
    # 关联任务
def corr_task_ssl(task_id,domain):
    # 关联的 HTTPS
    body_ar ={
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Hello_c_ServerName": domain
                                    }
                                }
                            }
                        }
                    }
                }
            }
    if task_id != 0:
        index_name= "ssl_"+str(task_id) + "*"
    else: 
        index_name = "ssl_*"
    result = es.search(index=index_name,body=body_ar)
    print("======ip_server_port ======")
    print("======ip_server_port end======")
    return  result['aggregations']['age_terms']['buckets']['china']['doc_count']
def corr_task_cert(task_id,domain):
    body_ar = {"aggs":{"age_terms":""}}


def corr_task_http(task_id , domain):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Host": domain
                                    }
                                }
                            }
                        }
                    }
                }
            }
    #index_name = "http_*"
    if task_id != 0:
        index_name= "http_"+str(task_id)+"*"
    else: 
        index_name = "http_*"
    result = es.search(index=index_name,body=body_ar)
    print("======ip_server_port ======")
    print("======ip_server_port end======")
    return  result['aggregations']['age_terms']['buckets']['china']['doc_count']
def corr_task_dns(domain,task_id ):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Answer.name": domain
                                    }
                                }
                            }
                        }
                    }
                }
            }
    #index_name = "http_*"
    if task_id != 0:
        index_name= "dns_"+str(task_id)+"*"
    else: 
        index_name = "dns_*"
    result = es.search(index=index_name,body=body_ar)
    print("======ip_server_port ======")
    print("======ip_server_port end======")
    return result['aggregations']['age_terms']['buckets']['china']['doc_count']
def domain_base_info(doname ):
    global father_domain 
    father_domain = ""
    whois = ""
    alex = 0
    remark = ""
    b_domail_doname =  "未知"
    # children 兄弟 域名数量 
    sql1 = "select a.remark , a.type as do_type , b.domain, a.alex , a.whois ,b.client_hot  from tb_domain_info a  , tb_domain_attribute b   where  a.n_domain = b.n_domain and a.n_domain  = \'"+doname+"\'";

    relset = s_mysql(sql1,cursor)
    for row in relset:
        if father_domain =="":
            father_domain = row['domain']
        if alex == 0 :
            alex = row['alex']
        if whois == "":
            whois = row['whois']
        if remark == "":
            remark = row['remark']
        # 是否为专用域名
        if  row['do_type'] == 2:
            b_domail_doname = "是"
        elif  row['do_type'] == 1:
            b_domail_doname = "否"

        n_client_hot =row['client_hot']
    # 危险等级 ， 百名单权重
    sql2 = "select black_list , white_list    from  tb_domain_info where n_domain = \'"+doname+"\'"
    relset1 = s_mysql(sql2,cursor)
    if len(relset1) == 0:
        black_list = 50
        white_list = 50
        sys.exit(1)
    else :
        relset2 = relset1[0]
        if relset2['black_list'] is None:
            black_list = 50
        else:
            black_list  = relset2['black_list']
        if  relset2['white_list'] is None:
            white_list =50
        else:
            white_list = relset2['white_list']
    # 

    sql3 = "select b.tag_text   from tb_domain_tag  a , ((SELECT post.tag_text as tag_text, post.tag_family AS tag_family,post.tag_id AS tag_id FROM `tb_tag_info` AS `post`)UNION(SELECT reply.rule_name as tag_text,reply.rule_family AS tag_family,reply.rule_id AS tag_id FROM `tb_rule_info` AS `reply`)) as b where a.tag_id = b.tag_id and  a.domain =  \'"+domain+"\'"
    relset3 = s_mysql(sql3,cursor)  
    tag_name_list = ""
    for row in relset3:
        tag_name += row['tag_text']

    #  域名指向的IP 
    sql2 = "select  count(*) as count  from  tb_passive_dns where n_domain = \'"+doname+"\'"
    if argv_json['task_id'] != 0:
        sql2 = sql2 + " and task_id  = " +str(argv_json['task_id'] )

    relset2 = s_mysql(sql2,cursor)[0]
    ip_num = relset2['count']

    # 解析服务器数量 

    # 兄弟域名的数量
    sql3 = "select count(*) as count from  tb_domain_attribute where domain  = \'"+father_domain+"\'"
    if argv_json['task_id'] != 0:
        sql3 = sql3 + " and task_id  = " +str(argv_json['task_id'] )
    relset3 = s_mysql(sql3,cursor)[0]  
    clildren_domain = relset3['count']
    #-- 兄弟域名指向的IP 数量 
    sql3 = "select count(ip) as count from  tb_passive_dns where domain  = \'"+father_domain+"\'"
    if argv_json['task_id'] != 0:
        sql3 = sql3 + " and task_id  = " +str(argv_json['task_id'] )
    relset3 = s_mysql(sql3,cursor)[0]  
    clildren_domain_ip = relset3['count']

    # 证书数量 
    sql3 = "select count(cert_list_key) as count from  tb_passive_cert where n_domain  = \'"+domain+"\'"
    if argv_json['task_id'] != 0:
        sql3 = sql3 + " and task_id  = " +str(argv_json['task_id'] )
    relset3 = s_mysql(sql3,cursor)[0]  
    cert_num = relset3['count']

    # 首末次数量 
    sql3 = "select max(last_time) as last_time , min(first_time) as first_time from tb_domain_attribute   where n_domain  = \'"+domain+"\'"
    if argv_json['task_id'] != 0:
        sql3 = sql3 + " and task_id  = " +str(argv_json['task_id'] )
    relset3 = s_mysql(sql3,cursor)[0]  
    last_time  = relset3['last_time']
    first_time = relset3['first_time']
    if last_time ==  None:
        last_time = 0
    if  first_time == None:
        first_time = 0
    # 标签数量
    sql3 = "select count(tag_id) as count  from tb_domain_tag where domain =  \'"+domain+"\'"
    relset3 = s_mysql(sql3,cursor)[0]  
    tag_num = relset3['count']

    # dns-query
    # DNS 解析失败次数
    sql3 = "select count(*) as count  from tb_client_dns where n_domain =  \'"+domain+"\' and success =  0"
    if argv_json['task_id'] != 0:
        sql3 = sql3 + " and task_id  = " +str(argv_json['task_id'] )
    relset3 = s_mysql(sql3,cursor)[0]  
    fail_num = relset3['count']
    
    # DNS 解析成功次数
    sql3 = "select count(*) as count  from tb_client_dns where n_domain =  \'"+domain+"\' and success =  1"
    if argv_json['task_id'] != 0:
        sql3 = sql3 + " and task_id  = " +str(argv_json['task_id'] )
    relset3 = s_mysql(sql3,cursor)[0]  
    relset3 = s_mysql(sql3,cursor)[0]  
    sucesss_num = relset3['count']
    # 关联 HTTP
    http_num = corr_task_http(0,domain)
    https_num = corr_task_ssl(0,domain)
    # 关联的回话
    session_num = http_num + https_num

    domain_base_info_list.append(("域名",domain))
    domain_base_info_list.append(("父域名",father_domain))
    domain_base_info_list.append(("末次发现时间",time_to_all_str(first_time)))
    domain_base_info_list.append(("末次发现时间",time_to_all_str(last_time)))
    domain_base_info_list.append(("危险等级",black_list))
    domain_base_info_list.append(("白名单权重",white_list))
    domain_base_info_list.append(("告警列表",list_to_pdflist(tag_name_list)))
    domain_base_info_list.append(("标签",tag_num))
    domain_base_info_list.append(("备注",remark))

    # 目标属性
    domain_attr_info_list.append(("Alex排名",alex))
    domain_attr_info_list.append(("WhoIS",whois))
    domain_attr_info_list.append((b_domail_doname,whois))  
    domain_attr_info_list.append(("DNS-query",fail_num + sucesss_num ))
    domain_attr_info_list.append(("解析成功",sucesss_num ))
    domain_attr_info_list.append(("解析失败",fail_num ))
    domain_attr_info_list.append(("关联HTTP", http_num))
    domain_attr_info_list.append(("关联HTTPS", https_num))
    domain_attr_info_list.append(("关联证书", cert_num))
    domain_attr_info_list.append(("兄弟域名指向IP数量",clildren_domain_ip))
    domain_attr_info_list.append(("客户端热度",client_hot))
    domain_attr_info_list.append(("指向IP数量",ip_num))
    domain_attr_info_list.append(("解析服务器数量",0))
    domain_attr_info_list.append(("兄弟域名数量",clildren_domain))


def domain_aralm_info(domain):
    domain_aralm_info_list.append(("序号","告警","告警类型","任务","危险权重","说明","首次告警时间","末次告警时间"))
    sql ="select  b.tag_text , b.tag_family ,c.task_id  , b.black_list , a.defense_info , max(time) as last_time  ,min(time) as first_time from  tb_alarm  a ,tb_task_batch c , ((SELECT post.tag_id  as tag_id , post.tag_text as tag_text, post.tag_family AS tag_family,post.black_list AS black_list  FROM `tb_tag_info` AS `post`)UNION(SELECT reply.rule_id as tag_id , reply.rule_name as tag_text,reply.rule_family AS tag_family,reply.rule_level  AS black_list  FROM `tb_rule_info` AS `reply`)) as b where a.tag_id = b.tag_id and  a.target_type = 3  and a.batch_id = c.batch_id  and  a.target_name = \'"+domain+"\' group by  c.task_id,b.tag_id"
    result = s_mysql(sql,cursors)
    tid  = 1
    for row in result:
        if row['tag_text'] == None:
            continue
        domain_aralm_info_list.append((tid,row['tag_text'],row['tag_family'],row['task_id'],row['black_list'],row['defense_info'],time_to_str(row['first_time']),time_to_str(row['last_time'])))
        tid = tid + 1

# 关联分析 ---  关联域名
def sel_domain_info(domain,cor_type,tid):
    sql = "select IFNULL(b.black_list,0) as  black_list ,c.tag_num ,IFNULL(a.alex,0) as alex  , IFNULL(a.whois,\"\")  as whois,b.times ,b.first_time , b.last_time from tb_domain_info a  ,(select  max(black_list) as black_list, min(first_time) as first_time , max(last_time) as last_time ,sum(times) as times  from tb_domain_attribute where n_domain = '"+domain+"') b , (select count(tag_id)  as tag_num from tb_domain_tag where domain = '"+domain+"') c where a.n_domain = '"+domain+"'"
    result = s_mysql(sql , cursors)
    row = result[0]
    return (tid , domain ,cor_type,row['black_list'],row['tag_num'],row['alex'],row['times'],row['whois'],time_to_str(row['first_time']),time_to_str(row['last_time']))
    


def corr_ip_ssl(ip_list,task_id,tid):
    body_ar = {
            "query": {
                "terms": {
                    "dIp":ip_list

                    }
                }
            }
    if task_id == 0:
        index_name = "ssl_*"
    else:
        index_name = "ssl_"+str(task_id)+"*"
    result = es.search(index=index_name,body=body_ar)
    print("======ip_server_port ======")
    dns_domain_str={}
    for row in  result["hits"]["hits"]:
        doname_s = row['_source']['Hello_c_ServerName']
        if doname_s not in  dns_domain_str:
            dns_domain_str[doname_s] =  1 
    for doname_s in dns_domain_str :
        domain_corr_domain.append(sel_domain_info(doname_s,"ssl",tid))
        tid = tid + 1
    return tid 

def corr_ip_http(ip_list,task_id,tid):
    body_ar = {
            "query": {
                "terms": {
                    "dIp":ip_list

                    }
                }
            }
    if task_id == 0:
        index_name = "http_*"
    else:
        index_name = "http_"+str(task_id)+"*"
    result = es.search(index=index_name,body=body_ar)
    print("======ip_server_port ======")
    dns_domain_str={}
    for row in  result["hits"]["hits"]:
        doname_s = row['_source']['Host']
        if doname_s not in  dns_domain_str:
            dns_domain_str[doname_s] =  1 
    for doname_s in dns_domain_str :
        domain_corr_domain.append(sel_domain_info(doname_s,"http",tid))
        tid =  tid + 1 
    return tid 


def corr_domain_dns(domain,task_id,tid):
    body_ar = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "Answer.value": domain
                                }
                            }
                        ]
                    }
                }
            }
    if task_id == 0:
        index_name = "dns_*"
    else:
        index_name = "dns_"+str(task_id)+"*"
    result = es.search(index=index_name,body=body_ar)
    print("======ip_server_port ======")
    print(result)
    print("======ip_server_port end======")
    dns_domain_str={}
    for row in  result["hits"]["hits"]:
        for Answer in row['_source']['Answer']:
            if Answer['value'] == domain :
                if Answer['name'] not in  dns_domain_str :
                    dns_domain_str[Answer['name']] = 1
    #
    tid = 0
    for domain_tl in  dns_domain_str:
        domain_corr_domain.append(sel_domain_info(domain_tl,"dns",tid))
        tid = tid + 1
    return tid
server_ip_list = [] # 域名的服务器IP 列表
def domain_corr_domain_func(domain):
    sql = "select distinct ip  from tb_passive_dns where  (answer = 1 or answer =28)  and  n_domain  = \'" +domain+ "\'"
    relset = s_mysql(sql,cursors)
    for row in relset:
        server_ip_list.append(row['ip'])
    tid = 0
    domain_corr_domain.append(("序号","域名","关联协议","威胁等级","标签","Alex排名","WhoIs","Query热度","首次发现时间","末次发现时间"))
    tid  = corr_domain_dns(domain,0,tid )
    tid  = corr_ip_http(server_ip_list,0,tid)
    tid  = corr_ip_ssl(server_ip_list,0,tid)
    #  兄弟域名 
def brother_domain(domain_name):
    global father_domain 
    sql = "select a.n_domain ,  a.type , a.ip , a.times  from  tb_passive_dns a   where domain  = \'" +father_domain+"\'"
    tid = 0
    relset = s_mysql(sql,cursors)
    if len(relset) > 0:
        domain_bother_info_list.append(("序号","域名","关联协议","威胁等级","标签","Alex排名","WhoIs","Query热度","首次发现时间","末次发现时间"))
    for row in relset :
        domain_bother_info_list.append(sel_domain_info(row['n_domain'],"brother_domain",tid))
        tid  = tid +1

#  4.1.a 会话分析   DNS协议 a)  客户端  iself.任务分布
def corr_task_info(domain_info):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Domain": domain_info
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "task": {
                            "terms": {
                                "field": "TaskId"
                                },
                            "aggs": {
                                "sIp": {
                                    "terms": {
                                        "field": "sIp.keyword"
                                        },
                                    "aggs": {
                                        "max_price": {
                                            "max": {
                                                "field": "StartTime"
                                                }
                                            },
                                        "aggs": {
                                            "min": {
                                                "field": "StartTime"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "dns_*"
    if argv_json['task_id'] != 0:
        index_name = "dns_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)

    if "aggregations" not in  result :
        return ;
    tid = 1
    sip_list = []
    task_info_t = {}
    domain_corr_task_list.append(("序号","任务","客户端热度","客户端IP数","咨询次数","解析成功次数","解析失败次数","首次出现时间","末次出现时间"))
    for row in  result["aggregations"]['age_terms']['buckets']['china']['task']['buckets']:
        task_id = row['key']
        last_time =  0;
        first_time = 2999999999;
        connect_num = row['doc_count']
        for sIp in row['sIp']['buckets']:
            if last_time < sIp['max_price']['value']:
                last_time = sIp['max_price']['value']
            if  first_time > sIp['aggs']['value']:
                first_time = sIp['aggs']['value']
            sip_list.append(sIp['key'])
        task_info={}
        task_info['id'] = tid 
        task_info['task_id'] = task_id 
        task_info['client_hot'] = client_hot_dst(sip_list)
        task_info['client_num'] = len(sip_list)
        task_info['dns-qurey'] = connect_num 
        task_info ['ans_num'] = connect_num
        task_info ['ans_num'] = 0
        task_info['first_time']= first_time
        task_info['last_time']= last_time

        domain_corr_task_list.append((task_info['id'],task_info['task_id'],task_info['client_hot'],task_info['client_num'],task_info['dns-qurey'],task_info ['ans_num'],task_info ['ans_num'],time_to_str(task_info['first_time']),time_to_str(task_info['last_time'])))
        tid = tid+1


# DNS 时序分布
def dns_time_histogram(domain):
    #def  
    min_time = max_time - 3600*24
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Domain": domain
                                    }
                                }

                            }
                        },
                    "aggs": {
                        "task": {
                            "terms": {
                                "field": "Ans"
                                },
                            "aggs": {
                                "prices": {
                                    "histogram": {
                                        "field": "StartTime",
                                        "interval": 3600,
                                        "extended_bounds":{
                                            "min":min_time,
                                            "max":max_time
                                            },
                                        "order": {
                                            "_key": "desc"
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "dns_*"
    if argv_json['task_id'] != 0:
        index_name = "dns_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)

    if "aggregations" not in  result :
        return ;
    tid = 1
    bar_row = {}
    times_l = []
    success_l = []
    fail_l = []
    # pyEchart 柱状图
    for row in result['aggregations']['age_terms']['buckets']['china']['task']['buckets']:
        success = "success"
        if row['key'] == 0:
            success = "fail"
        for brirow in row['prices']['buckets']:
            c_time = brirow['key']
            if c_time < min_time:
                continue
            times = brirow['doc_count']
            if  c_time not in bar_row:
                bar_row[c_time]  ={}
                bar_row[c_time]["success"] = 0
                bar_row[c_time]["fail"] = 0
            if success not in bar_row[c_time]:
                bqr_row[c_time][success] = 0
            bar_row[c_time][success] = bar_row[c_time][success] + brirow['doc_count']
            for key  in bar_row :
                times_l.append(time_to_hour_str(key))
                success_l.append(bar_row[key]["success"])
                fail_l.append(bar_row[key]["fail"])
    #print(bar_row)
    file_png = "png/"+domain+"_" +str(int(time.time()))+"_dns_histogram.png"
    show_two_value_bar(times_l, success_l  ,fail_l ,["success","fail"],file_png)
    return file_png 



def sel_ip_info(ip):
    sql = "select IFNULL(a.black_list , 0) as  black_list , b.tag_num   from tb_ip_info a ,(select ip,count(tag_id) as tag_num from tb_ip_tag where ip = '"+ip+"') b  where a.ip = '"+ip+"' limit 0,1"
    result = s_mysql(sql,cursors)[0]
    return result 
    
#    解析服务器 
def domain_parse_server(domain):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Domain": domain
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "task": {
                            "terms": {
                                "field": "dIp.keyword"
                                },
                            "aggs": {
                                "max_price": {
                                    "max": {
                                        "field": "StartTime"
                                        }
                                    },
                                "aggs": {
                                    "min": {
                                        "field": "StartTime"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    index_name = "dns_*"
    if argv_json['task_id'] != 0:
        index_name = "dns_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)

    if "aggregations" not in  result :
        return ;
    tid = 1
    domain_parse_server_list.append(("序号","解析服务器","危险权重","标签","解析次数","首次出现时间","末次出现时间"))
    for row in  result["aggregations"]['age_terms']['buckets']['china']['task']['buckets']:
        dIp = row['key']
        count = row['doc_count']
        last_time = row['max_price']['value']
        first_time = row['aggs']['value']
        ipre = sel_ip_info(dIp) 
        domain_parse_server_list.append((tid,dIp,ipre['black_list'],ipre['tag_num'],count,time_to_all_str(first_time),time_to_all_str(last_time)))
        tid = tid +1

def isIP(str):
    p = re.compile('^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$')
    if p.match(str):
        return True
    else:
        return False
# 解析结果   域名解析  指向的IP 
def relsert_parse_domain_func(domain):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Answer.name": domain
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "task": {
                            "terms": {
                                "field": "Answer.value.keyword"
                                },
                            "aggs": {
                                "parse_type": {
                                    "terms": {
                                        "field": "Answer.value.keyword"
                                        },
                                    "aggs": {
                                        "max_price": {
                                            "max": {
                                                "field": "StartTime"
                                                }
                                            },
                                        "aggs": {
                                            "min": {
                                                "field": "StartTime"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
    index_name = "dns_*"
    if argv_json['task_id'] != 0:
        index_name = "dns_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)

    if "aggregations" not in  result :
        return ;
    tid = 1
    relsert_parse_domain.append(("序号","类型","解析值","次数","比例","首次出现时间","末次出现时间"))
    relsert_parse_ip.append(("序号","类型","解析值","次数","比例","首次出现时间","末次出现时间"))
    reslt_t= []
    sum_tm_dom = 0
    sum_tm_ip = 0
    for row in  result["aggregations"]['age_terms']['buckets']['china']['task']['buckets']:
        domain_pars = row['key']
        print(row)
        for wrow in row['parse_type']['buckets']:
            doc_count = wrow['doc_count']
            pasre_type = wrow['key'] 
            last_time = wrow['max_price']['value']
            first_time = wrow['aggs']['value']
            td={}
            td['id'] = tid 
            td['type'] = pasre_type 
            td['domain_pars'] = domain_pars 
            td['count'] = doc_count 
            td['bf'] = 0 
            td['first_time'] = first_time 
            td['last_time'] = last_time
            if  isIP(domain_pars):
                td['ip'] = True 
                sum_tm_ip = sum_tm_ip + doc_count
            else :
                td['ip'] =  False
                sum_tm_dom = sum_tm_dom +  doc_count
        reslt_t.append(td)
        tid = tid +1
    for row in reslt_t:
        if row['ip']:
            bf = int(row['count'] / sum_tm_ip *100)
            relsert_parse_ip.append((row['id'],row['type'],row['domain_pars'] ,row['count'],bf,time_to_all_str(row['first_time']),time_to_all_str(row['last_time'])))
        else:
            bf = int(row['count'] / sum_tm_dom *100)
            relsert_parse_domain.append((row['id'],row['type'],row['domain_pars'] ,row['count'],bf,time_to_str(row['first_time']),time_to_str(row['last_time'])))
#  指向该域名  s_
def domain_pasre_to_domain_func(domain):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Answer.value": domain
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "task": {
                            "terms": {
                                "field": "Answer.name.keyword"
                                },
                            "aggs": {
                                "parse_type": {
                                    "terms": {
                                        "field": "Answer.type"
                                        },
                                    "aggs": {
                                        "max_price": {
                                            "max": {
                                                "field": "StartTime"
                                                }
                                            },
                                        "aggs": {
                                            "min": {
                                                "field": "StartTime"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "dns_*"
    if argv_json['task_id'] != 0:
        index_name = "dns_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    tm = 0 
    td_s = []
    tid = 0 
    domain_pasre_to_domain_list.append(("序号","域名","解析类型","次数","比例","首次解析时间","末次解析时间"))
    for row in  result["aggregations"]['age_terms']['buckets']['china']['task']['buckets']:
        domain_s = row['key']
        for wrow in row['parse_type']['buckets']:
            pasre_type = wrow['key']
            tm = tm + wrow['doc_count']
            doc_count = wrow['doc_count']
            pasre_type = wrow['key'] 
            last_time = wrow['max_price']['value']
            first_time = wrow['aggs']['value']
            td={}
            td['id'] = tid 
            td['type'] = pasre_type 
            #td['domain_pars'] = domain_pars 
            td['count'] = doc_count 
            td['bf'] = 0 
            td['first_time'] = first_time 
            td['last_time'] = last_time
            td['domain_s'] = domain_s
            td_s.append(td)
            tid = tid +1
    for row in td_s :
        bf = int (td['count']/tm * 100)
        domain_pasre_to_domain_list.append((row['id'],row['domain_s'] , row['type'] , row['count'] , bf , time_to_all_str(row['first_time']),time_to_all_str(row['last_time'])))

# 告警回话  --- 先放下 


# 2 http协议  客户端 
def http_task_defona_func(domain):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Host": domain 
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "task": {
                            "terms": {
                                "field": "TaskId"
                                },
                            "aggs": {
                                "ClientIP": {
                                    "terms": {
                                        "field": "sIp.keyword"
                                        },
                                    "aggs": {
                                        "max_price": {
                                            "max": {
                                                "field": "StartTime"
                                                }
                                            },
                                        "aggs": {
                                            "min": {
                                                "field": "StartTime"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "http_*"
    if argv_json['task_id'] != 0:
        index_name = "http_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    tm = 0 
    td_s = []
    tid = 0 
    http_task_defona_list.append(("序号","任务","客户端热度","客户端IP数","会话数量","首次解析时间","末次解析时间"))
    for row in  result["aggregations"]['age_terms']['buckets']['china']['task']['buckets']:
        task_id = row['key']
        client_ip_list  = []
        first_time = 2999999999
        last_time = 0
        connelt_num = 0
        for wrow in row['ClientIP']['buckets']:
            client_ip_list.append(wrow['key'])
            connelt_num =wrow['doc_count'] + connelt_num
            if first_time > wrow['aggs']['value']:
                first_time =  wrow['aggs']['value']
            if last_time < wrow['max_price']['value']:
                last_time = wrow['max_price']['value']
        client_hot = client_hot_dst(client_ip_list)
        http_task_defona_list.append((tid,task_id , client_hot ,len(client_ip_list),connelt_num , time_to_all_str(first_time), time_to_all_str(last_time) ))
# http  iiself.时序分布  :
def http_domain_histogram(domain):
    min_time = max_time - 3600*24
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Host": domain
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "prices": {
                            "histogram": {
                                "field": "StartTime",
                                "extended_bounds":{
                                    "min":min_time,
                                    "max":max_time
                                    },
                                "interval": 3600,
                                "order": {
                                    "_key": "desc"
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "http_*"
    if argv_json['task_id'] != 0:
        index_name = "http_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    times_l = []
    data = []
    for row in  result["aggregations"]['age_terms']['buckets']['china']['prices']['buckets']:
        if row['key'] < min_time:
            continue
        times_l.append(time_to_hour_str(row['key']))
        data.append(row['doc_count'])
    png_file_name =  "png/"+ domain +str(int(time.time()))+ "_http_histogram.png"
    #show_two_value_bar(times_l, success_l  ,fail_l ,["success","fail"],domain+"dns_histogram.png")
    show_two_value_bar(times_l, data ,[] ,["http"],png_file_name)
    return  png_file_name
# b)    服务器
def ip_corr_domain(ip,ltype):
    sql = "select distinct n_domain from tb_passive_dns where  type & ltype >  0 and ip = \'" + ip +"\'" ;
    relset = s_mysql(sql ,cursors)
    domain_str = ""
    for row in result:
        domain_str = domain_str +" " +row['n_domain']
    return domain_str

def domain_server_info_func(domain):
    http_server_ip_list =[]
    http_domain_parse_server_list.append(("序号","服务器","危险等级","标签","http端口","开发端口","客户端热度","关联域名","首次出现时间","末次出现时间"))
    sql = "select distinct ip  from tb_passive_dns where type & 2 > 0 and n_domain  = \'" +domain+ "\'"
    relset = s_mysql(sql,cursors)
    for row in relset:
        http_server_ip_list.append(row['ip'])
    body_ar = {
            "query": {
                "terms": {
                    "dIp": http_server_ip_list
                    }
                },
            "aggs": {
                "task": {
                    "terms": {
                        "field": "dIp.keyword"
                        },
                    "aggs": {
                        "Port": {
                            "terms": {
                                "field": "dPort"
                                },
                            "aggs": {
                                "sIp": {
                                    "terms": {
                                        "field": "sIp.keyword"
                                        },
                                    "aggs": {
                                        "max_price": {
                                            "max": {
                                                "field": "StartTime"
                                                }
                                            },
                                        "aggs": {
                                            "min": {
                                                "field": "StartTime"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0:
        index_name = "http_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    tid = 1
    ip_list_t = {}

    for row in  result["aggregations"]['task']['buckets']:
        dIp = row['key']
        count = row['doc_cout'] 
        port_str = ""
        http_port_str = ""
        first_time = 29999999999
        last_time = 0 
        for app_row in row['app']['buckets']:
            app_id  = app_row['key']
            for port_row in   app_row['Port']['buckets']:
                if app_id == 10637:
                    http_port_str = http_port_str +" "+str(port_row['key'])
                else:
                    port_str = port_str +" "+str(port_row['key'])
                for sIp_row in port_row['sIp']['buckets']:
                    sip = sIp_row['key'] 
                    if sip not in ip_list_t :
                        ip_list_t[sip] = 1
                    if last_time < sIp_row['max_price']['value']:
                        last_time = sIp_row['max_price']['value']
                    if first_time >  sIp_row['aggs']['value']:
                        first_time = sIp_row['aggs']['value']
        ip_re = sel_ip_info(dIp)
        domain_str = ip_corr_domain(dIp,1)

        client_hot_l = client_hot_dst_t(ip_list_t)
        # 危险等级  标签  关联域名 
        http_domain_parse_server_list.append((tid,dIp,ip_re['black_list'],ip_re['tag_num'] , http_port_str,port_str , client_hot_l ,first_time ,last_time ))
        tid = tid + 1
# 关联指纹
def domain_corr_finger(domain ,task_id  , finger_type):
    #base_json =  relsert_json['base'] 
    if  finger_type == 2 :
        table_str = "tb_http_tcp_finger e "
        where_str = " e.useragent =  a.finger and e.domain = '"+str(domain)+", "
    elif  finger_type ==  3 :
        table_str = " tb_ssl_tcp_finger  e "
        where_str = " e.sslfinger  =  a.finger and e.sni  = '"+str(domain)+", "
    sql = "select a.finger_sha1 , a.type , a.black_list , c.tag_id , b.ip , a.os ,b.times , b.first_time ,b.last_time   from tb_finger_infor a , tb_server_finger b ,tb_finger_tag c  , "+ table_str+" where a.finger  = b.finger and c.finger = a.finger  and "+ where_str +" " +"' and   ( b.task_id = "+str(task_id) + " or b.task_id = 0 )"
    print(sql)
    relset = s_mysql(sql , cursor)
    finger_list = {}
    corr_finger = []
    corr_finger.append(("序号","指纹","指纹类型","威胁等级","标签","关联IP热度","属性","会话数量","首次发现时间","末次发现时间"))
    for row in relset :
        finger = row['finger'] 
        if  finger not in finger_list:
            finger_t = {}
            finger_t['finger'] = finger
            finger_t['type'] = finge_type[row['type']]
            finger_t['black_list'] = row['black_list']
            finger_t['tag_id'] = [] 
            finger_t['tag_id'].append(row['tag_id'])
            finger_t['os'] = row['os']
            finger_t['ip'] = []
            finger_t['ip'].append(row['ip'])
            finger_t['times'] = row['times']
            finger_t['first_time'] = row['first_time']
            finger_t['last_time'] = row['last_time']
            finger_list[finger] = finger_t
        else:
            finger_t =  finger_list[finger]
            finger_t['tag_id'].append(row['tag_id'])
            finger_t['ip'].append(row['ip'])
            finger_t['times'] = row['times'] + finger_t['times']
            if finger_t['first_time'] > row['first_time']:
                finger_t['first_time'] = row['first_time']
            if  finger_t['last_time'] < row['last_time']:
                finger_t['last_time'] = row['last_time']
    tid = 0
    for row in finger_list:
       corr_finger.append((tid,longstr_to_pdf(row['finger']),row['type'],row['black_list'],list_to_pdftable(row['tag_id'],client_ip_hot(row['ip']),row['os'],row['times'],time_to_str(row['first_time'],time_to_str(row['last_time'])))))
    return  corr_finger
# 告警



#  SSL 
def ssl_task_defona_func(domain):
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Hello_c_ServerName": domain 
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "task": {
                            "terms": {
                                "field": "TaskId"
                                },
                            "aggs": {
                                "ClientIP": {
                                    "terms": {
                                        "field": "sIp.keyword"
                                        },
                                    "aggs": {
                                        "max_price": {
                                            "max": {
                                                "field": "StartTime"
                                                }
                                            },
                                        "aggs": {
                                            "min": {
                                                "field": "StartTime"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "ssl_*"
    if argv_json['task_id'] != 0:
        index_name = "ssl_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    tm = 0 
    td_s = []
    tid = 0 
    ssl_task_defona_list.append(("序号","任务","客户端热度","客户端IP数","会话数量","首次解析时间","末次解析时间"))
    for row in  result["aggregations"]['age_terms']['buckets']['china']['task']['buckets']:
        task_id = row['key']
        client_ip_list  = []
        first_time = 299999999
        last_time = 0
        connelt_num = 0
        for wrow in row['ClientIP']['buckets']:
            client_ip_list.append(wrow['key'])
            connelt_num =wrow['doc_count'] + connelt_num
            if first_time > wrow['aggs']['value']:
                first_time =  wrow['aggs']['value']
            if last_time < wrow['max_price']['value']:
                last_time = wrow['max_price']['value']
        client_hot = client_hot_dst(client_ip_list)
        ssl_task_defona_list.append((tid,task_id , client_hot ,len(client_ip_list),connelt_num , first_time , last_time ))
# ssl  iiself.时序分布  :
def ssl_domain_histogram(domain):
    min_time = max_time - 3600*24
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Hello_c_ServerName": domain
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "prices": {
                            "histogram": {
                                "field": "StartTime",
                                "extended_bounds":{
                                    "min":min_time,
                                    "max":max_time
                                    },
                                "interval": 3600,
                                "order": {
                                    "_key": "desc"
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "ssl_*"
    if argv_json['task_id'] != 0:
        index_name = "ssl_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    times_l = []
    data = []
    for row in  result["aggregations"]['age_terms']['buckets']['china']['prices']['buckets']:
        times_l.append(time_to_hour_str(row['key']))
        data.append(row['doc_count'])
    png_file_name =  "png/"+domain +"_"+str(int(time.time())) + "_ssl_histogram.png"
    show_two_value_bar(times_l, data ,[] ,["ssl"],png_file_name)
    return png_file_name

# b)    服务器

def domain_ssl_server_info_func(domain):
    ssl_server_ip_list =[]
    sql = "select distinct ip  from tb_passive_dns where type & 8 > 0 and n_domain  = \'" +domain+ "\'"
    relset = s_mysql(sql,cursors)
    for row in relset:
        ssl_server_ip_list.append(row['ip'])
    body_ar = {
            "query": {
                "terms": {
                    "dIp": ssl_server_ip_list
                    }
                },
            "aggs": {
                "task": {
                    "terms": {
                        "field": "dIp.keyword"
                        },
                    "aggs": {
                        "Port": {
                            "terms": {
                                "field": "dPort"
                                },
                            "aggs": {
                                "sIp": {
                                    "terms": {
                                        "field": "sIp.keyword"
                                        },
                                    "aggs": {
                                        "max_price": {
                                            "max": {
                                                "field": "StartTime"
                                                }
                                            },
                                        "aggs": {
                                            "min": {
                                                "field": "StartTime"
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    index_name = "connectinfo_*"
    if argv_json['task_id'] != 0:
        index_name = "connectinfo_"+str(argv_json['task_id'] )+"_*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    tid = 1
    ip_list_t = {}

    ssl_parse_server_list.append(("序号","服务器","危险等级","标签","ssl端口","开发端口","客户端热度","关联域名","首次出现时间","末次出现时间"))
    for row in  result["aggregations"]['task']['buckets']:
        dIp = row['key']
        count = row['doc_cout'] 
        port_str = ""
        http_port_str = ""
        first_time = 2999999999
        last_time = 0 
        for app_row in row['app']['buckets']:
            app_id  = app_row['key']
            for port_row in   app_row['Port']['buckets']:
                if app_id == 10637:
                    http_port_str = http_port_str +" "+str(port_row['key'])
                else:
                    port_str = port_str +" "+str(port_row['key'])
                for sIp_row in port_row['sIp']['buckets']:
                    sip = sIp_row['key'] 
                    if sip not in ip_list_t :
                        ip_list_t[sip] = 1
                    if last_time < sIp_row['max_price']['value']:
                        last_time = sIp_row['max_price']['value']
                    if first_time >  sIp_row['aggs']['value']:
                        first_time = sIp_row['aggs']['value']
        ip_re = sel_ip_info(dIp)
        domain_str = ip_corr_domain(dIp,1)

        client_hot_l = client_hot_dst_t(ip_list_t)
        # 危险等级  标签  关联域名 
        ssl_parse_server_list.append((tid,dIp,ip_re['black_list'],ip_re['tag_num'] , http_port_str,port_str , client_hot_l ,first_time ,last_time ))
        tid = tid + 1
# 关联指纹



# 关联的证书  / 证书链

def domain_corr_cert(domain):
    body_ar={
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    "Hello_c_ServerName": domain
                                    }
                                }
                            }
                        },
                    "aggs": {
                        "task": {
                            "terms": {
                                "field": "Cert_s_Hash_str.keyword"
                                },
                            "aggs": {
                                "max_price": {
                                    "max": {
                                        "field": "StartTime"
                                        }
                                    },
                                "aggs": {
                                    "min": {
                                        "field": "StartTime"
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

    # 告警
    index_name = "ssl_*"
    result = es.search(index=index_name,body=body_ar)
    if "aggregations" not in  result :
        return ;
    tid = 1
    cert_tid = 1
    ip_list_t = {}

    cert_parse_server_list.append(("序号","证书","威胁等级","标签","证书指纹","所有者","签发机构","授权域名","签发时间","失效时间"))
    certlist_parse_server_list.append(("序号","证书链","威胁等级","标签","关联IP","关联域名","回话数量","首次发现时间","末次出现时间"))
    for row in  result["aggregations"]['age_terms']['buckets']['china']['task']['buckets']:
        cert = row['key']
        count = row['doc_count']
        last_time = row['max_price']['value']
        first_time = row['aggs']['value']
        #  查询 证书关联  tb_passive_cert
        sql = "select IFNULL( c.black_list ,0) as  black_list ,b.tag_num , c.cert_json  from tb_passive_cert a , (select IFNULL(sum(tag_id),0)  as  tag_num , cert_sha1 from tb_cert_tag where cert_sha1 = '"+cert+"') b   , tb_cert_info c where a.cert_sha1 = '"+cert+"' and b.cert_sha1 = a.cert_sha1 and  c.cert_sha1 = a.cert_sha1 limit 0,1"
        relset = s_mysql(sql , cursors)
        print("*****" , result)
        if len(relset)> 0:
            row  = relset[0]
            black_list = row['black_list']
            tag_num = row['tag_num']
            cjson = row['cert_json']
            own =  ""
            Issuer = ""
            NotBefore = ""
            NotAfter = ""
            cert_domain = ""
            if "Issuer" in cjson and "CN" in cjson["Issuer"]:
                certIssue = cjson["Issuer"]["CN"]
            # 获取证书使用者
            if "Subject" in cjson and "CN" in cjson["Subject"]:
                certOwner = cjson["Subject"]["CN"]
            # 获取证书生效时间
            if "NotBefore" in cjson:
                certNotBefore = cjson["NotBefore"]
            # 获取证书失效时间
            if "NotAfter" in cjson:
                certNotAfter = cjson["NotAfter"]
            if 'Extension' in cjson and 'subjectAltName' in cjson['Extension']:
                cert_domain = cjson['Extension']['subjectAltName'].replace("DNS:","",10000)
            # 证书关联的指纹
            cert_parse_server_list.append((tid,cert,black_list,tag_num,"",own,Issuer,cert_domain,NotBefore,NotAfter))
            tid = tid + 1
            
            # 解析证书链rt_listcertlist_parse_server_list.append(("序号","证书链","威胁等级","标签","关联IP","关联域名","回话数量","首次发现时间","末次出现时间"))
            sql = "select a.task_id ,  c.cert_list , a.ip , a.n_domain  , a.times , a.first_time , a.last_time from tb_passive_cert a  , tb_cert_list c where  a.cert_list_key = c.tkey  and  a.cert_sha1 = \'"+cert+"\' "
            relset = s_mysql(sql , cursors)
            cert_list_info  = {}
            for row in relset :
                if row['cert_list'] not in cert_list_info :
                    
                    cert_list_info[row['cert_list']] = row
                    cert_list_t = cert_list_info[row['cert_list']]
                    cert_list_t['ip_list'] = []
                    cert_list_t['ip_list'].append(row['ip'])
                    cert_list_t['domain_list'] = []
                    cert_list_t['domain_list'].append(row['n_domain'])

                else:
                    cert_list_t = cert_list_info[row['cert_list']]
                    cert_list_t['times'] = cert_list_t['times'] + row['times']
                    if row['first_time'] < cert_list_t['first_time']:
                        cert_list_t['first_time'] = row['first_time'] 
                    if  row['last_time'] > cert_list_t['last_time']:
                        cert_list_t['last_time'] = row['last_time']
                    if  row['ip'] not in cert_list_t['ip_list']:
                        cert_list_t['ip_list'].append(row['ip'])
                    if row['n_domain'] not  in cert_list_t['domain_list'] :
                        cert_list_t['domain_list'].append(row['n_domain'])
            for row in cert_list_info :
                certlist_parse_server_list.append((cert_tid,row['cert_list'],black_list,tag_num,json.dumps(row['ip_list']),json.dumps(row['domain_list']),row['times'],row['first_time'],row['last_time']))



#   告警会话 

select_session_t ={}
def session_tag_info():
    # 取出告警等级
    sql = "select session_id , black_list from  tb_session_id where   black_list > 80 "
    relset = s_mysql(sql , cursors)
    for row in relset:
        select_session_t[row['session_id']] = row['black_list']

def domain_corr_trim_info(doname,index_name ,field_name):
    # 查出所有的回话 
    ararm_table_tab =[]
    ararm_table_tab.append(("序号","会话ID","源IP","源端口","目的IP","目的端口","协议号","危险等级","标签","上行数据量","下行数据量","起始时间","结束时间"))
    body_ar = {
            "aggs": {
                "age_terms": {
                    "filters": {
                        "filters": {
                            "china": {
                                "match": {
                                    field_name : domain
                                    }
                                }
                            }
                        }
                    }
                }
            }
    result = es.search(index=index_name,body=body_ar)
    tid = 1
    cert_tid = 1
    session_id_t = {}
    for row in result['hits']['hits']:
        if row['_source']['SessionId'] not in  session_id_t:
            session_id_t[row['_source']['SessionId']] =  1
    session_t = list(session_id_t.keys() & select_session_t.keys())

    body_ar =  {
            "query": {
                "terms": {
                    "SessionId": session_t
                    }
                }
            }
    index_name = "connectinfo_*"
    result = es.search(index=index_name,body=body_ar)
    tid = 1
    for row in  result['hits']['hits']:
        # 
         ararm_table_tab.append((tid,row["SessionId"],row['sIp'],row['sPort'],row['dIp'],row['dPort'],row['AppName'],select_session_t[row["SessionId"]],len(row['Labels']),row['pkt']['pkt_spayloadbytes'],row['pkt']['pkt_dpayloadbytes'],row['StartTime'],row['EndTime']))

         tid = tid + 1
    return  ararm_table_tab











dns_img = ""
http_img = ""
ssl_img = ""


def topdf():
    global domain_base_info_list
    global domain_attr_info_list
    global domain_corr_task_list
    global domain_bother_info_list
    global domain_aralm_info_list
    global domain_corr_domain
    global s_domain_corr_task_list
    global domain_parse_server_list
    global relsert_parse_domain
    global relsert_parse_ip 
    global domain_pasre_to_domain_list 
    global dns_alarm_session 
    global http_task_defona_list 
    global http_domain_parse_server_list
    global dns_alarm_session
    global ssl_task_defona_list
    global ssl_parse_server_list
    global  dns_img 
    global  http_img 
    global  ssl_img
    global http_domain_corr_finger
    global ssl_domain_corr_finger
    content = list()
    # %添加标题
    content.append(Graphs.draw_title(domain , 'msyhbd', 18, 50, 1)) 
    content.append(Graphs.draw_title("分析报告" , 'msyhbd', 18, 50, 1)) 

    content.append(Graphs.draw_title("一 基本信息", 'msyh', 14, 30, 0))
    content.append(Graphs.draw_title(" 1 分析结果", 'msyh', 12, 30, 0))
    content.append(Table(domain_base_info_list, colWidths=225,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 目标属性
    content.append(Graphs.draw_title(" 2 目标属性", 'msyh', 12, 30, 0))
    content.append(Table(domain_attr_info_list, colWidths=225,
                         style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (0, -1), '#d5dae6'),
                                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 告警信息  
    content.append(Graphs.draw_title(" 3 告警信息", 'msyh', 12, 30, 0))
    #print(domain_aralm_info_list)
    content.append(Table(domain_aralm_info_list, colWidths=[30,30,50,30,50,50,85,85],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
     # 二 任务归属
    content.append(Graphs.draw_title("二 任务归属", 'msyh', 14, 30, 0))
    content.append(Graphs.draw_title(" 1 回话统计", 'msyh', 12, 30, 0))
    #content.append(Table(s_domain_corr_task_list, colWidths=[30,30,55,55,65,65,65,85,85],
    #        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
    #            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
    #            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 三  关联分析
    content.append(Graphs.draw_title("三 关联分析", 'msyh', 14, 30, 0))
    # 1 联域名
    content.append(Graphs.draw_title(" 1 关联域名", 'msyh', 12, 30, 0))
    content.append(Table(domain_corr_domain, colWidths=[30,70,50,45,30,50,50,55,85,85],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 2 兄弟域名
    content.append(Graphs.draw_title(" 2 兄弟域名", 'msyh', 12, 30, 0))
    #content.append(Table(domain_corr_domain, colWidths=50,
    #        style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
    #            ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
    #            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    # 三  回话分析
    content.append(Graphs.draw_title("三 会话分析", 'msyh', 14, 30, 0))
    # 1 客户端
    content.append(Graphs.draw_title(" 1 客户端", 'msyh', 12, 30, 0))
    # 1.1 
    content.append(Graphs.draw_title("  i 任务分布", 'msyh', 12, 30, 0))
    print(domain_corr_task_list)
    #content.append(Table(domain_corr_task_list, colWidths=60,
    content.append(Table(domain_corr_task_list, colWidths=[30,30,55,55,60,60,60,85,85],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
     #  1.2 时序分布
    content.append(Graphs.draw_title("  ii 时序分布", 'msyh', 12, 30, 0))
    content.append(Spacer(1, 20 * mm))
    img = Image(dns_img)
    img.drawHeight = 100 * mm
    img.drawWidth = 100 * mm
    img.hAlign = TA_LEFT
    content.append(img)
    #3   解析结果
    content.append(Graphs.draw_title(" 3  解析结果", 'msyh', 14, 30, 0))
    #  2    解析服务器
    content.append(Graphs.draw_title(" 2 解析服务器", 'msyh', 14, 30, 0))
    content.append(Table(domain_parse_server_list, colWidths=[30,65,50,30,40,100,100],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    #3   解析结果
    content.append(Graphs.draw_title(" 3  解析结果", 'msyh', 14, 30, 0))
    # 3.1 
    content.append(Graphs.draw_title("  i 域名解析", 'msyh', 12, 30, 0))
    content.append(Table(relsert_parse_domain, colWidths=[30,60,50,30,50,85,85],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # 3.2
    content.append(Graphs.draw_title("  ii 指向盖域名", 'msyh', 12, 30, 0))
    content.append(Table(domain_pasre_to_domain_list, colWidths=[30,80,50,30,30,85,85],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    # 3.3 指向IP
    content.append(Graphs.draw_title("  iii 指向IP", 'msyh', 12, 30, 0))
    content.append(Table(relsert_parse_ip, colWidths=[30,75,75,30,30,100,100],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    #3.4 d) 告警会话 
    content.append(Graphs.draw_title("  iiii  告警会话", 'msyh', 12, 30, 0))
    content.append(Table(dns_alarm_session, colWidths=[30,30,50,30,30,100,100],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    # 4 2self.HTTP协议
    content.append(Graphs.draw_title(" 2 HTTP协议", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title(" a) 客户端", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("   i  任务分布", 'msyh', 12, 30, 0))
    ##  
    content.append(Table(http_task_defona_list, colWidths=[30,30,50,50,50,100,100],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    #  
    content.append(Graphs.draw_title("   ii  时序分布", 'msyh', 12, 30, 0))
    content.append(Spacer(1, 20 * mm))
    img = Image(http_img)
    img.drawHeight = 100 * mm
    img.drawWidth = 100 * mm
    img.hAlign = TA_LEFT
    content.append(img)
    content.append(Graphs.draw_title(" b) 服务器", 'msyh', 12, 30, 0))

    content.append(Table(http_domain_parse_server_list, colWidths=[30,70,40,30,40,40,50,45,100,100],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # c) 关联指纹 
    content.append(Graphs.draw_title(" c) 关联指纹", 'msyh', 12, 30, 0))
    content.append(Table(http_domain_corr_finger, colWidths=[30,65,30,30,50,60,50,50,55,55],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    # d) 告警会话
    content.append(Graphs.draw_title(" d) 告警会话", 'msyh', 12, 30, 0))
    if len(http_alarm_session) > 0:
        content.append(Table(http_alarm_session, colWidths=[30,30,50,30,30,85,85],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))


    # SSL /TLS
    content.append(Graphs.draw_title(" 2 SSL/TLS 协议", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title(" a) 客户端", 'msyh', 12, 30, 0))
    content.append(Graphs.draw_title("   i  任务分布", 'msyh', 12, 30, 0))
    content.append(Table(ssl_task_defona_list, colWidths=[30,30,55,55,70,85,85],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    #  
    content.append(Graphs.draw_title("   ii  时序分布", 'msyh', 12, 30, 0))
    content.append(Spacer(1, 20 * mm))
    img = Image(ssl_img)
    img.drawHeight = 100 * mm
    img.drawWidth = 100 * mm
    img.hAlign = TA_LEFT
    content.append(img)
    content.append(Graphs.draw_title(" b) 服务器", 'msyh', 12, 30, 0))

    content.append(Table(ssl_parse_server_list, colWidths=[30,70,40,30,40,40,50,45,100,100],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))
    # c) 关联指纹 
    content.append(Graphs.draw_title(" c) 关联指纹", 'msyh', 12, 30, 0))
    content.append(Table(ssl_domain_corr_finger, colWidths=[30,65,30,30,50,60,50,50,55,55],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    # d) 告警会话
    content.append(Graphs.draw_title(" d) 告警会话", 'msyh', 12, 30, 0))
    content.append(Table(ssl_alarm_session, colWidths=[25,30,75,40,75,40,30,40,20,60,60,85,85],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))


    
    # e) 告警会话
    content.append(Graphs.draw_title(" e) 关联证书", 'msyh', 12, 30, 0))
    content.append(Table(cert_parse_server_list, colWidths=[30,85,50,30,50,40,50,50,85,85],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))


    # f) Copyright © 2020 关联证书. All Rights Reserved.
    content.append(Graphs.draw_title(" e) 关联证书链", 'msyh', 12, 30, 0))
    content.append(Table(certlist_parse_server_list, colWidths=[20,100,50,20,70,70,50,85,85],
            style=[('FONTNAME', (0, 0), (-1, -1), 'msyh'), ('BACKGROUND', (0, 0), (-1, 0), '#d5dae6'),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'), ('VALIGN', (-1, 0), (-2, 0), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 0.5, colors.grey)]))

    # 





    doc = SimpleDocTemplate(file_name, pagesize=letter)
    doc.build(content)
### 
def OneDomainPasre(domain):
    global domain_base_info_list
    global domain_attr_info_list
    global s_domain_corr_task_list
    global domain_bother_info_list
    global domain_aralm_info_list
    global domain_corr_domain
    global domain_corr_task_list
    global domain_parse_server_list
    global relsert_parse_domain
    global relsert_parse_ip 
    global domain_pasre_to_domain_list 
    global dns_alarm_session 
    global http_task_defona_list 
    global http_domain_parse_server_list
    global ssl_task_defona_list
    global ssl_parse_server_list
    global cert_parse_server_list 
    global ssl_alarm_session 
    global certlist_parse_server_list
    global  dns_img 
    global  http_img 
    global  ssl_img
    global http_domain_corr_finger
    global ssl_domain_corr_finger
    dns_img = ""
    http_img = ""
    ssl_img = ""
    domain_base_info_list = []
    domain_attr_info_list = []
    s_domain_corr_task_list = []
    domain_bother_info_list = []
    domain_aralm_info_list = []
    domain_corr_domain = []
    domain_corr_task_list =[] 
    domain_parse_server_list = []
    # dns 
    relsert_parse_domain = []
    relsert_parse_ip = []
    domain_pasre_to_domain_list = []
    dns_alarm_session = []
    # http 
    http_task_defona_list = []
    http_domain_parse_server_list = []
    http_alarm_session = []


    # ssl 
    ssl_task_defona_list = []
    ssl_parse_server_list = []
    cert_parse_server_list = []
    ssl_alarm_session=[]
    certlist_parse_server_list = []


    domain_base_info(domain)
    domain_aralm_info(domain)
    domain_corr_domain_func(domain)
    brother_domain(domain)
    corr_task_info(domain)
    # dns
    dns_img = dns_time_histogram(domain)
    domain_parse_server(domain)
    relsert_parse_domain_func(domain)
    domain_pasre_to_domain_func(domain)
    # http 
    http_task_defona_func(domain)
    http_img = http_domain_histogram(domain)
    domain_server_info_func(domain)
    http_domain_corr_finger = domain_corr_finger(domain ,argv_json['task_id'],2)
    # ssl 
    ssl_task_defona_func(domain)
    ssl_img = ssl_domain_histogram(domain)
    domain_ssl_server_info_func(domain)
    domain_corr_cert(domain)
    ssl_domain_corr_finger = domain_corr_finger(domain,argv_json['task_id'] ,3)

    dns_alarm_session = domain_corr_trim_info(domain,"dns_*","Answer.name")
    http_alarm_session = domain_corr_trim_info(domain,"http_*","Host")
    ssl_alarm_session = domain_corr_trim_info(domain,"ssl_*","Hello_c_ServerName")
    topdf()
if __name__ == '__main__':
    argv_parse(sys.argv) 
    get_max_time()
    domain = argv_json['target_value']
    OneDomainPasre(domain)
    #os.system("rm -rf png")

