
import sys
sys.path.append("./")

import pandas as pd

import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.AlarmHelper as alarm_helper
import PyGksec.GkHelper.TagHelper as tag_helper
from utils.redis_utils.redis_helper import redis_not_alarm
from utils.mysql_utils.mysql_helper import mysql_not_white


FILTER_TAG_TEXTS = ["ICMP白负载"]
FILTER_TAG_IDS = [tag_helper.Tag_Text_Map[tag_text]["Tag_Id"] for tag_text in FILTER_TAG_TEXTS]  # ICMP白负载

def get_alarm_reason(df:pd.DataFrame) -> list[dict]:
    """
        告警原因生成
        :param DataFrame df: ES会话数据
        :return list[dict]: 字典组成的列表
    """
    alarm_reason = []
    speed = max([r["TotalPacketNum"]/r["Duration"] if r["Duration"] != 0 else r["TotalPacketNum"] for _,r in df.iterrows()])
    alarm_reason.append({"key":"1. 检测发现高频率ICMP数据包", "actual_value":f"{speed}次/s"})
    # [int(r["TotalPacketNum"])/int(r["Duration"]) if int(r["Duration"]) != 0 else int(r["TotalPacketNum"]) for _,r in df.iterrows()]
    alarm_reason.append({"key":"2. 检测发现包长异常的ICMP包", "actual_value":max([r["pkt"]["sMaxLen"] for _,r in df.iterrows()] + [r["pkt"]["dMaxLen"] for _,r in df.iterrows()])})

    return alarm_reason
        
def get_related_labels(df:pd.DataFrame, include_tags) -> list[dict]:
    """
        告警关联标签,该告警包含的标签和实际关联标签到标签ID映射转换
        :param DataFrame df: ES会话数据
        :param str include_tags: 告警关联的标签名称
        :return list: 关联标签ID
    """
    label_sets = {label for labels in df["Labels"].to_list() for label in labels}

    if include_tags is None or not include_tags or pd.isna(include_tags):
        return []

    # 关联标签ID转换
    tags = include_tags.split("/")
    tag_id_sets = {tag_helper.Tag_Text_Map[tag_name]["Tag_Id"] for tag_name in tags if tag_name in tag_helper.Tag_Text_Map}

    # 实际关联标签
    return list(label_sets & tag_id_sets)

def write_icmp_alarm(df:pd.DataFrame, alarm_name, task_id, batch_id) -> None:
    """
        ICMP隧道告警生成与推送
        :param df: DataFrame格式ES会话数据
        :param alarm_name: 告警名称
        :param task_id: 任务id
        :param batch_id: 批次id
        :return None: 无返回
    """
    alarm_data = alarm_helper.get_alarm_json_data(alarm_name=alarm_name)
    alarm_data["attack_route"] = []
    alarm_data["victim"] = [{"ip": vip} for vip in df.sIp.drop_duplicates()][:10]
    alarm_data["attacker"] = [{"ip": aip} for aip in df.dIp.drop_duplicates()][:10]
    alarm_data["targets"] = [{"name": aip["ip"], "type":"ip", "labels":[]} for aip in alarm_data["attacker"]][:10]

    alarm_data["alarm_reason"] = get_alarm_reason(df)
    alarm_data["alarm_principle"] = '客户端使用ICMP隐蔽隧道访问服务器，存在被远控木马操控风险。'
    alarm_data["alarm_handle_method"] = "检查服务器是否被ICMP隐蔽隧道操控，排查是否存在ICMP隐蔽隧道软件的运行，将该客户端IP过滤。"

    alarm_data["alarm_related_label"] = get_related_labels(df, alarm_helper.ALARM_KNOW_NAME_MAP[alarm_name]["include_tags"])
    alarm_data["alarm_session_list"] = df["SessionId"].drop_duplicates().tolist()[:10]
    df["alarm_related_label"] = df.Labels.map(lambda x: list(set(x) & set(alarm_data["alarm_related_label"])))
    df["alarm_related_label"] = df.alarm_related_label.map(lambda x: x if len(x) > 0 else [alarm_data['alarm_knowledge_id']])
    alarm_data["attack_chain_list"] = list({f"{x['sIp']}_{x['dIp']}_{label}" for _, x in df.iterrows() for label in x['alarm_related_label']})

    if alarm_data["targets"] != [] and alarm_data["victim"] != [] and alarm_data["attacker"] != [] and redis_not_alarm(alarm_data["attack_chain_list"]) and mysql_not_white(alarm_data["attack_chain_list"]):
        alarm_data = alarm_helper.append_alarm_task_info(alarm_data, task_id, batch_id)
        alarm_helper.add_remote_alarm_json(alarm_data)

def detect_icmp_tunnel(task_id: int = -1, batch_id: int = -1, pkt_speed_threshold: int = 20, pkt_len_threshold: int = 100) -> None:
    """
        检测ICMP隧道流量,生成告警并推送
        :param task_id: 任务id,默认为-1
        :param batch_id: 批次id,默认为-1
        :param pkt_speed_threshold: 包速率阈值,默认为20
        :param pkt_len_threshold: 包长度阈值,默认为100
        :return: 无返回
    """
    table, detail = eh.get_basic_query(es_type="connect*", task_id=task_id, batch_id=batch_id)
    
    detail["query"]["bool"]["must"].append({"term": {"AppName":"APP_ICMP_v4"}})
    detail["query"]["bool"]["must"].append({"range": {"TotalPacketNum":{"gte":pkt_speed_threshold}}}) # 排除Duration为0时,使用0.0001作为时间则包频率大,同时限定查询范围
    detail["query"]["bool"]["must"].append({"script": {"script": {"source": "doc['TotalPacketNum'].value / (doc['Duration'].value+0.0001) > params.threshold","params": {"threshold": pkt_speed_threshold}}}})  # pkt频率计算,加0.0001是为避免除数为0的无效情况
    
    es_data = eh.load_data(table, detail)
    if not es_data:
        return
    
    data_list = []
    for data in es_data:
        if "pkt" not in data:
            continue
        
        if any(label in data["Labels"] for label in FILTER_TAG_IDS):
            continue
        
        if ("sMaxLen" in data["pkt"]  and data["pkt"]["sMaxLen"] > pkt_len_threshold) or ("dMaxLen" in data["pkt"]  and data["pkt"]["dMaxLen"] > pkt_len_threshold):
            data_list.append(data)

    if len(data_list) == 0:
        return
    
    df = pd.DataFrame(data_list)
    df.groupby(["sIp"], as_index=False).apply(lambda group:write_icmp_alarm(group, alarm_name="加密隐蔽隧道通信", task_id=task_id, batch_id=batch_id))

if __name__ == '__main__':
    task_id, batch_id = eh.get_latest_task_id('connectinfo*')
    detect_icmp_tunnel(task_id=task_id, batch_id=batch_id)


