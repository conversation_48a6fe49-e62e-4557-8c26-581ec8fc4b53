##
# @file change_ifconf.py
# @brief  : 修改网卡的绑定
# <AUTHOR>
# @version 0.1.00
# @date 2022-03-28

import sys,os,pymysql.cursors,xml.etree.ElementTree as ET ,json,time
import json
base_json = {}
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
    db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()

# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()

def is_json(myjson):
 try:
  json_object = json.loads(myjson)
 except ValueError :

    return False
 return True
###  
json_path= "/opt/GeekSec/th/bin/conf_pub/ifconf.json"
tsql= "select id,pcie_id , flow_name  from tb_network_flow  "
relset_2 = s_mysql(tsql,cursor)
flow_id_to_id = {}
flow_id_to_l_id = {}
flow_id_to_name = {}
conf_path = "/opt/GeekSec/th/bin/conf_pub/ifconf.json"
FIleJosn = False
if  os.path.exists(conf_path):
      f = open(conf_path)
      context = f.read()
      if is_json(context):
           config = json.loads(context)
           FIleJosn  = True
if not FIleJosn:
     config = {
    "mbuf": 4194303,                                #缓存数据包数
    "max_task": 2,                                  #并行任务数量：主从共计两个
    "max_thread": 16,                               #所有任务共计包处理核心数量
    "numa": 0,                                      #所有捕包网卡所在NUMA节点
    "if": [                                         #捕包口信息数组
        {"pci": "0000:01:00.0", "name": "1"},       #捕包口PCI地址，以及命名
        {"pci": "0000:01:00.1", "name": "2"}
    ],
    "0": [0,1],                                     #主任务捕包口数组
    "1": [],                                        #从任务捕包口数组
    "suspend": []                                   #挂起任务数组
}
if_t= []
numid = 0
for row in relset_2:
    #flow_name_to_id[row["flow_name"]] = row["pcie_id"]
    flow_id_to_name[str(row["id"])] = row["flow_name"]
    flow_id_to_id[str(row["id"])] = row["pcie_id"]
    flow_id_to_l_id[str(row["id"])] =  numid
    if_t.append({"pci":row["pcie_id"],"name":row["flow_name"]})
    numid += 1
config["if"] = if_t
config["suspend"] = []
sql = "select task_id , netflow ,task_state  from tb_task_analysis where (task_id = 0 or task_id = 1 )  and (task_state = 1 or task_state = 2 or task_state = 0 )"
relset = s_mysql(sql ,  cursor);
for row in relset :
     task_id   = row["task_id"]
     onetflow = json.loads(row["netflow"])
     task_state = row["task_state"]
     #######
     config[str(task_id)] =  []
     print(onetflow)
     for ii in onetflow:
        num = 0
        i = flow_id_to_name[str(ii)]
        iid = flow_id_to_l_id[str(ii)]
        print("iid  === " , iid)
        if iid != None:
            config[str(task_id)].append(iid)
            num += 1

     if task_state == 2 or task_state == 0 :
        config["suspend"].append(str(task_id))
     



json.dump(config,open(json_path , "w+"),ensure_ascii=False)
os.system("/opt/GeekSec/th/bin/thd.all.restart")
     #### 
    
     
    





