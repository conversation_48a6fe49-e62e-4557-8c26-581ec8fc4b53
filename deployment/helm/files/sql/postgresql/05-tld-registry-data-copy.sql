-- tld_registry_info数据导入脚本（使用COPY机制优化版本）
-- 原始文件: 05-tld-registry-data.sql
-- CSV文件: tld_registry_info.csv
-- 数据行数: 1,406
-- 文件大小: 160,920 字节

-- 清空现有数据
TRUNCATE TABLE tld_registry_info CASCADE;

-- 使用COPY命令从CSV文件导入数据
\COPY tld_registry_info (
        tld,    is_new_gtld,    registry_operator,    application_id,    contract_signature_date,    delegation_date,    status,    u_label,    description,    data_source,    last_updated
) FROM '/csv/tld_registry_info.csv' WITH (FORMAT csv, HEADER false, NULL '');

-- 更新统计信息以优化查询性能
ANALYZE tld_registry_info;

-- 显示导入结果统计
DO $$
DECLARE
    total_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO total_count FROM tld_registry_info;
    RAISE NOTICE '=== tld_registry_info数据导入完成 ===';
    RAISE NOTICE '总记录数: %', total_count;
    RAISE NOTICE '================================';
END $$;
