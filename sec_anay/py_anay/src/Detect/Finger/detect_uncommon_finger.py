import sys
sys.path.append("./")

import PyGksec.GkHelper.ESHelper as eh
from Detect.Finger.cluster_fingers import load_sip_to_fingers,load_finger_to_cert
from Detect.Finger.DNN_Finger_Embedding import GK_Finger_Embedding,EMBEDDING_DIM
from PyGksec.GkHelper.KnowledgeHelper import GK<PERSON>inger_DF



def trans_http_finger(finger):
    re_finger = " ".join(list(x.split("/")[0] for x in finger.split(" ")))
    return re_finger

def check_abnormal_ssl_fingers(task_id):
    df = load_sip_to_fingers(task_id,"ssl","sSSLFinger")
    q = df[["finger","sip"]].drop_duplicates().groupby("finger",as_index=False).count()
    check_fingers = list(q[q.sip==1].finger.drop_duplicates())
    if len(check_fingers)==0:
        return
    fcert_df = load_finger_to_cert(task_id,"ssl","sSSLFinger")
    know_white = set(GKFinger_DF[GKFinger_DF.finger_type.map(lambda x : "Benign" in x)].finger_es)
    know_white = know_white & set(int(x) for x in fcert_df.finger)
    gk_embedding = GK_Finger_Embedding(df=fcert_df,other_key="dcert")
    gk_embedding.exprt_embeddings("tmp/a.csv")
    for finger in check_fingers:
        min_dis = gk_embedding.get_shortest_dis(finger,know_white)
        print(finger,min_dis)



def run(task_id,batch_id):
    check_abnormal_ssl_fingers(task_id)

if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("ssl*")
    run(task_id,batch_id)
