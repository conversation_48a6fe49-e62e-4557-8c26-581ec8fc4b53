# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/4/15 10:53
# 文件名称 : line_syn.py
# 开发工具 : PyCharm
import os
import pymysql.cursors
import xml.etree.ElementTree as ET
import json
from loadMysqlPasswd import mysql_passwd
passwd =  mysql_passwd()
# 杀死探针
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
cursor.execute("update tb_line_analyze set text ='' ")
db.commit()
os.system("rm -rf /opt/GeekSec/th/bin/SegmentInfor.txt")

cursor.execute("select type_name,text,type from tb_line_analyze;")
results = cursor.fetchall()
cursor.execute("select * from tb_line_analyze_param where type = 'LINE';")
results_param = cursor.fetchall()
db.close()
# 启动线路分析、启动网络探针
tree_line_net = ET.parse('/opt/GeekSec/th/bin/conf/th_engine_conf.xml')
root_line_net = tree_line_net.getroot()
print(results_param)
if results[0]['type'] == 0:
    # 启动线路分析
    for elem in root_line_net.iter('b_segment_analyse'):
        elem.text = 'true'
    tree_line_net.write('/opt/GeekSec/th/bin/conf/th_engine_conf.xml')
    fread = open('/opt/GeekSec/th/bin/Config/Config.txt')
    config_json = json.loads(fread.readline())
    fread.close()
    config_json['MaxSegmentNum'] = results_param[0]['max_segment_num']
    config_json['MaxMacNum'] = results_param[0]['max_mac_num']
    os.system("echo \"\">/opt/GeekSec/th/bin/Config/Config.txt")
    fwrite = open('/opt/GeekSec/th/bin/Config/Config.txt','w')
    fwrite.write(json.dumps(config_json))
    fwrite.close()
    os.system("rm -rf /opt/GeekSec/th/bin/task_pcap_read.conf")
    #os.system("cd /opt/GeekSec/th/bin/ && nohup ./thd -1 >/dev/null &")
    os.system("/etc/init.d/thdd restart_analyse")
if results[0]['type'] == 1:
    # 启动网络探针
    os.system("rm -f /opt/GeekSec/th/bin/SegmentInfor.txt")
    for elem in root_line_net.iter('b_segment_analyse'):
        elem.text = 'false'
    tree_line_net.write('/opt/GeekSec/th/bin/conf/th_engine_conf.xml')
    #os.system("cd /opt/GeekSec/th/bin/ && nohup ./thd >/dev/null &")
    os.system("/etc/init.d/thdd restart")

