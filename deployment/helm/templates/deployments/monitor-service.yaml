{{- if .Values.services.monitor-service.enabled -}}
{{- $serviceValues := .Values.services.monitor-service -}}
{{- $serviceConfig := dict "name" $serviceValues.name -}}
{{- $_ := set $serviceConfig "namespace" .Values.global.namespace -}}
{{- $_ := set $serviceConfig "replicas" $serviceValues.replicas -}}
{{- $_ := set $serviceConfig "image" $serviceValues.image -}}
{{- $_ := set $serviceConfig "registry" .Values.global.registry -}}
{{- $_ := set $serviceConfig "tag" .Values.global.tag -}}
{{- $_ := set $serviceConfig "port" $serviceValues.port -}}
{{- $_ := set $serviceConfig "springProfiles" .Values.global.springProfiles -}}
{{- $_ := set $serviceConfig "postgresql" .Values.infrastructure.postgresql -}}
{{- $_ := set $serviceConfig "database" $serviceValues.database -}}
{{- $_ := set $serviceConfig "javaOpts" .Values.global.javaOpts -}}
{{- $_ := set $serviceConfig "resources" (default .Values.global.resources $serviceValues.resources) -}}
{{- if $serviceValues.autoscaling -}}
{{- $_ := set $serviceConfig "autoscaling" $serviceValues.autoscaling -}}
{{- end -}}
{{- if $serviceValues.extraEnv -}}
{{- $_ := set $serviceConfig "extraEnv" $serviceValues.extraEnv -}}
{{- end -}}

{{- include "nta.deployment" $serviceConfig -}}
{{- end -}}
