# _*_ coding : UTF-8 _*_
# 开发团队 : GeekSec
# 开发人员 : MCC
# 开发时间 : 2019/6/24 23:42
# 文件名称 : domain_ip_certDomain.py
# 开发工具 : PyCharm
import json,pymysql.cursors,sys,re,hashlib,socket,os
# 将ASC转String
def ascToStr(str):
    strObj = ''
    strArr = str.split(",")
    for row in strArr:
        strObj = strObj + chr(int(row))
    return strObj
# 将String计算Hash
def strToHash(str):
    hhaasshh = hashlib.sha1()
    hhaasshh.update(str.encode("utf8"))
    strHash = hhaasshh.hexdigest()
    return strHash
# mysql查询
def s_mysql(sql,cur):
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def domain_ip(obj,objIp,objLinkIp):
    if obj != '' and not obj is None:
        if isIP(obj):
            objIp["IP"] = obj
            ipLinkCert = []
            ipToCert = s_mysql("select DISTINCT Cert_Sha1 from DATA_PASSICECERT where IP = '" + obj + "';", tidb_cursor)
            if len(ipToCert) > 0:
                for row in ipToCert:
                    if row['Cert_Sha1'] != '' and not row['Cert_Sha1'] is None:
                        ipLinkCert.append(row['Cert_Sha1'])
            objIp["ipLinkCert"] = ipLinkCert
            ipLinkDomain = []
            ipToDomain = s_mysql("select DISTINCT DANAME from PassiveDNS where ip = '" + obj + "' and DANAME <> '" + paramValue + "';", tidb_cursor)
            if len(ipToDomain) > 0:
                for row in ipToDomain:
                    if row['DANAME'] != '' and not row['DANAME'] is None:
                        ipLinkDomain.append(row['DANAME'])
                        domain_domain(row['DANAME'],ipLinkDomain)
            objIp["ipLinkDomain"] = ipLinkDomain
            objLinkIp.append(objIp)
        else:
            domainToIp = s_mysql("select DISTINCT ip from PassiveDNS where DANAME = '" + obj + "' and DANAME <> '" + paramValue + "';", tidb_cursor)
            if len(domainToIp) > 0:
                for row in domainToIp:
                    objIp_ = {}
                    domain_ip(row['ip'],objIp_,objLinkIp)
def domain_domain(d,dList):
    # if len(dList) > 0:
    #     aaaaa = dList
    #     aaaaa.append(paramValue)
    #     aaaaa = dist(aaaaa)
    #     bbbbb = ''
    #     for row in aaaaa:
    #         bbbbb = bbbbb + ",'" + row + "'"
    #     print(bbbbb[1:])
    #     domainToDomain = s_mysql("select DISTINCT DANAME from PassiveDNS where ip = '" + d + "' and DANAME not in (" + bbbbb[1:] + ");",cursor)
    # else:
    domainToDomain = s_mysql("select DISTINCT DANAME from PassiveDNS where ip = '" + d + "' and DANAME not in ('" + paramValue + "');", tidb_cursor)
    if len(domainToDomain) > 0:
        for row in domainToDomain:
            if row['DANAME'] != '' and not row['DANAME'] is None:
                dList.append(row['DANAME'])
                #dList = dist(dList)
                domain_domain(row['DANAME'], dList)

def dist(list):
    list1 = []  # 创建一个新的数组来存储无重复元素的数组
    for element in list:
        if (element not in list1):
            list1.append(element)
    return list1

def isIP(str):
    #fl = re.compile('^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$')
    #if fl.match(str):
    if is_ipv4(str) or is_ipv6(str):
        return True
    else:
        return False

def is_ipv4(str):
    try:
        socket.inet_pton(socket.AF_INET, str)
    except AttributeError:  # no inet_pton here, sorry
        try:
            socket.inet_aton(str)
        except socket.error:
            return False
        return str.count('.') == 3
    except socket.error:  # not a valid ip
        return False
    return True

def is_ipv6(str):
    try:
        socket.inet_pton(socket.AF_INET6, str)
    except socket.error:  # not a valid ip
        return False
    return True

base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
# 获取参数
param = sys.argv[1]
paramStr = ascToStr(param)
#paramStr = '{"type":"Domain_Analysis_Link","domainName":"photo.china.com.cn"}'
paramHash = strToHash(paramStr)
paramValue = json.loads(paramStr)["domainName"]
tidb = pymysql.connect(host='localhost',port=base_json["tidb_port"],user='root',password='root',db='statistical_information_base',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
tidb_cursor = tidb.cursor()
#{"domain":"www.baidu.com","linkIp":[{"IP":"**************","ipLinkCert":["d6aaf8cfa0e0236547fc2a894f895ec94724a60d"],"ipLinkDomain":["www.a.shifen.com","sp0.baidu.com","sp1.baidu.com","sp2.baidu.com","sp3.baidu.com","top.baidu.com"]}]}
jsonObject = {}
linkIp = []
jsonObject["domain"] = paramValue
sql_ip = s_mysql("select DISTINCT ip from PassiveDNS where DANAME = '" + paramValue + "';", tidb_cursor)
if len(sql_ip) > 0 :
    for row in sql_ip:
        ipObject = {}
        domain_ip(row['ip'],ipObject,linkIp)
jsonObject["linkIp"] = linkIp
flag = s_mysql("select count(*) as num from tb_cache where hash = '" + paramHash + "';", tidb_cursor)
if flag[0]['num'] > 0:
    idu_mysql("update tb_cache set text = '" + json.dumps(jsonObject,ensure_ascii=False) + "',param = '" + paramStr + "' where hash = '" + paramHash + "';", tidb_cursor, tidb)
else:
    idu_mysql("insert into tb_cache (text,param,hash) values ('" + json.dumps(jsonObject,ensure_ascii=False) + "','" + paramStr + "','" + paramHash + "');", tidb_cursor, tidb)
tidb.close()

