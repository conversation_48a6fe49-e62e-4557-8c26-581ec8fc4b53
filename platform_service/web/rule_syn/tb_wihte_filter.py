# Last Update:2021-08-03 16:44:04
##
# @file tb_wihte_list_filter.py
# @brief 
# <AUTHOR>
# @version 0.1.00
# @date 2021-01-12

import sys ,os 
import json,pymysql
base_json = {}
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
# mysql查询
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()
# mysql增删改
def idu_mysql(sql,cur,x_db):
    cur.execute(sql)
    x_db.commit()
def read_white_list(task_id):
    sql = "select server_ip , port ,app_id , domain   from  tb_white_list where state = 1 and task_id = "+ str(task_id);
    results = s_mysql(sql , cursor)
    dist_rule = {}
    print(results)
    for row in results :
        tkey = []
        key = ""
        value = ""
        server_ip = row["server_ip"]
        port = row["port"]
        domain = row["domain"]
        app_id = row["app_id"]
        if server_ip != "":
            key +="server_ip"
            value += server_ip+"-"
            tkey.append("dst_ip")
        if port != -1:
            key +="dst_port"
            value += str(port)+"-"
            tkey.append("dst_port")
        if  domain  != "":
            key +="domain"
            value += domain+"-"
            tkey.append("domain")
        if app_id !=  -1:
            key +="app_id"
            value += str(app_id)+"-"
            tkey.append("app_id")
        if key not in  dist_rule:
            dist_rule[key] = {"key":tkey , "value":[value]}
        else :
            dist_rule[key]["value"].append(value)
    config = {"filed":[]}
    print(dist_rule)
    for r  in dist_rule:
        config["filed"].append(dist_rule[r])
    print(config)
    json.dump(config , open("filter_write.json","w+"))
    os.system("\cp -rf filter_write.json  /opt/GeekSec/STL/ExportData/conf/")
#read_white_list(1)

       


