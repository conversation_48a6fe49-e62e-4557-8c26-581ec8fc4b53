package com.geeksec.nta.alarm.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.geeksec.nta.alarm.entity.AlarmSuppression;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 告警抑制规则通知服务接口
 * 
 * <AUTHOR> 3.0 Team
 * @since 3.0.0
 */
public interface AlarmSuppressionNotificationService {
    
    private final KafkaTemplate<String, String> kafkaTemplate;
    private final ObjectMapper objectMapper;
    
    /** Kafka主题名称 */
    private static final String KAFKA_TOPIC = "alarm-whitelist-changes";
    
    /**
     * 通知抑制规则添加
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     */
    void notifySuppressionAdded(String victim, String attacker, String label);
    
    /**
     * 通知抑制规则移除
     * 
     * @param victim 受害者IP
     * @param attacker 攻击者IP
     * @param label 告警标签
     */
    void notifySuppressionRemoved(String victim, String attacker, String label);
    
    /**
     * 通知批量抑制规则添加
     * 
     * @param suppressionItems 抑制规则项列表
     */
    void notifyBatchSuppressionAdded(List<AlarmSuppression> suppressionItems);
    
    /**
     * 通知批量抑制规则移除
     * 
     * @param victim 受害者IP（可选）
     * @param attacker 攻击者IP（可选）
     * @param label 告警标签（可选）
     * @param count 移除数量
     */
    void notifyBatchSuppressionRemoved(String victim, String attacker, String label, int count);
        try {
            AlarmWhitelistChangeMessage message = AlarmWhitelistChangeMessage.createAddMessage(victim, attacker, label);
            sendMessage(message);
            log.debug("发送白名单添加通知: victim={}, attacker={}, label={}", victim, attacker, label);
        } catch (Exception e) {
            log.error("发送白名单添加通知失败: victim={}, attacker={}, label={}", victim, attacker, label, e);
        }
    }
    
    /**
     * 发送删除白名单通知
     */
    public void notifyWhitelistRemoved(String victim, String attacker, String label) {
        try {
            AlarmWhitelistChangeMessage message = AlarmWhitelistChangeMessage.createRemoveMessage(victim, attacker, label);
            sendMessage(message);
            log.debug("发送白名单删除通知: victim={}, attacker={}, label={}", victim, attacker, label);
        } catch (Exception e) {
            log.error("发送白名单删除通知失败: victim={}, attacker={}, label={}", victim, attacker, label, e);
        }
    }
    
    /**
     * 发送全量刷新通知
     */
    public void notifyRefreshAll(List<AlarmWhitelist> allWhitelists) {
        try {
            List<AlarmWhitelistChangeMessage.AlarmWhitelistItem> items = allWhitelists.stream()
                    .map(w -> new AlarmWhitelistChangeMessage.AlarmWhitelistItem(w.getVictim(), w.getAttacker(), w.getLabel()))
                    .collect(Collectors.toList());
            
            AlarmWhitelistChangeMessage message = AlarmWhitelistChangeMessage.createRefreshAllMessage(items);
            sendMessage(message);
            log.info("发送白名单全量刷新通知，数量: {}", items.size());
        } catch (Exception e) {
            log.error("发送白名单全量刷新通知失败", e);
        }
    }
    
    /**
     * 发送消息到Kafka
     */
    private void sendMessage(AlarmWhitelistChangeMessage message) throws Exception {
        String messageJson = objectMapper.writeValueAsString(message);
        kafkaTemplate.send(KAFKA_TOPIC, message.getMessageId(), messageJson);
    }
    
    /**
     * 告警白名单变更消息
     */
    public static class AlarmWhitelistChangeMessage {
        
        private String messageId;
        private OperationType operationType;
        private AlarmWhitelistItem item;
        private List<AlarmWhitelistItem> allItems;
        private LocalDateTime timestamp;
        
        // Getters and Setters
        public String getMessageId() { return messageId; }
        public void setMessageId(String messageId) { this.messageId = messageId; }
        
        public OperationType getOperationType() { return operationType; }
        public void setOperationType(OperationType operationType) { this.operationType = operationType; }
        
        public AlarmWhitelistItem getItem() { return item; }
        public void setItem(AlarmWhitelistItem item) { this.item = item; }
        
        public List<AlarmWhitelistItem> getAllItems() { return allItems; }
        public void setAllItems(List<AlarmWhitelistItem> allItems) { this.allItems = allItems; }
        
        public LocalDateTime getTimestamp() { return timestamp; }
        public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
        
        public enum OperationType {
            ADD, REMOVE, REFRESH_ALL
        }
        
        public static class AlarmWhitelistItem {
            private String victim;
            private String attacker;
            private String label;
            
            public AlarmWhitelistItem() {}
            
            public AlarmWhitelistItem(String victim, String attacker, String label) {
                this.victim = victim;
                this.attacker = attacker;
                this.label = label;
            }
            
            // Getters and Setters
            public String getVictim() { return victim; }
            public void setVictim(String victim) { this.victim = victim; }
            
            public String getAttacker() { return attacker; }
            public void setAttacker(String attacker) { this.attacker = attacker; }
            
            public String getLabel() { return label; }
            public void setLabel(String label) { this.label = label; }
        }
        
        public static AlarmWhitelistChangeMessage createAddMessage(String victim, String attacker, String label) {
            AlarmWhitelistChangeMessage message = new AlarmWhitelistChangeMessage();
            message.setMessageId(generateMessageId());
            message.setOperationType(OperationType.ADD);
            message.setItem(new AlarmWhitelistItem(victim, attacker, label));
            message.setTimestamp(LocalDateTime.now());
            return message;
        }
        
        public static AlarmWhitelistChangeMessage createRemoveMessage(String victim, String attacker, String label) {
            AlarmWhitelistChangeMessage message = new AlarmWhitelistChangeMessage();
            message.setMessageId(generateMessageId());
            message.setOperationType(OperationType.REMOVE);
            message.setItem(new AlarmWhitelistItem(victim, attacker, label));
            message.setTimestamp(LocalDateTime.now());
            return message;
        }
        
        public static AlarmWhitelistChangeMessage createRefreshAllMessage(List<AlarmWhitelistItem> allItems) {
            AlarmWhitelistChangeMessage message = new AlarmWhitelistChangeMessage();
            message.setMessageId(generateMessageId());
            message.setOperationType(OperationType.REFRESH_ALL);
            message.setAllItems(allItems);
            message.setTimestamp(LocalDateTime.now());
            return message;
        }
        
        private static String generateMessageId() {
            return System.currentTimeMillis() + "-" + Thread.currentThread().getId();
        }
    }
}
