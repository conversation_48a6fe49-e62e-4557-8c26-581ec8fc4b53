{{- if and .Values.infrastructure.elasticsearch.enabled .Values.infrastructure.elasticsearch.operator.enabled .Values.initialization.enabled .Values.initialization.elasticsearch.enabled -}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: cert-system-init-config
  namespace: {{ .Values.global.namespace }}
  labels:
    {{- include "nta.labels" . | nindent 4 }}
data:
  cert_system_import.py: |
    #!/usr/bin/env python3
    import os
    import json
    import time
    import logging
    from elasticsearch import Elasticsearch

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger('cert-system-import')

    # 连接Elasticsearch
    def get_es_client():
        es_host = os.environ.get('ES_HOST', 'elasticsearch-es-http')
        es_port = int(os.environ.get('ES_PORT', '9200'))
        es_user = os.environ.get('ES_USER', 'elastic')
        es_password = os.environ.get('ES_PASSWORD', 'elastic')

        es_url = f"http://{es_host}:{es_port}"
        return Elasticsearch(
            [es_url],
            http_auth=(es_user, es_password) if es_user and es_password else None
        )

    # 创建cert_system索引并导入数据
    def import_cert_system_data():
        es = get_es_client()
        index_name = "cert_system"
        template_name = "{{ .Values.initialization.elasticsearch.systemBuiltInCertificates.template }}"
        data_file = "/app/config/data/cert_system.json"

        # 检查索引是否存在
        exists = es.indices.exists(index=index_name)

        if not exists:
            # 创建索引
            try:
                # 检查模板是否存在
                templates = es.indices.get_template()
                if template_name in templates:
                    logger.info(f"模板 {template_name} 存在，使用模板创建索引")
                else:
                    logger.warning(f"模板 {template_name} 不存在，使用默认设置创建索引")

                es.indices.create(index=index_name)
                logger.info(f"创建索引 {index_name} 成功")
            except Exception as e:
                logger.error(f"创建索引 {index_name} 失败: {e}")
                return False
        else:
            logger.info(f"索引 {index_name} 已存在")

        # 导入数据
        if os.path.exists(data_file):
            try:
                with open(data_file, 'r') as f:
                    data = json.load(f)

                # 导入数据
                for doc in data:
                    es.index(index=index_name, body=doc)

                logger.info(f"向索引 {index_name} 导入数据成功")
                return True
            except Exception as e:
                logger.error(f"向索引 {index_name} 导入数据失败: {e}")
                return False
        else:
            logger.error(f"数据文件 {data_file} 不存在")
            return False

    # 主函数
    def main():
        logger.info("开始导入cert_system索引数据")

        # 等待Elasticsearch就绪
        max_retries = 10
        retry_count = 0
        es_ready = False

        while not es_ready and retry_count < max_retries:
            try:
                es = get_es_client()
                health = es.cluster.health()
                status = health.get('status')

                if status in ['green', 'yellow']:
                    es_ready = True
                    logger.info(f"Elasticsearch集群状态: {status}")
                else:
                    retry_count += 1
                    logger.info(f"Elasticsearch集群状态: {status}，等待就绪，重试 {retry_count}/{max_retries}")
                    time.sleep(10)
            except Exception as e:
                retry_count += 1
                logger.warning(f"连接Elasticsearch失败: {e}, 重试 {retry_count}/{max_retries}")
                time.sleep(10)

        if not es_ready:
            logger.error("Elasticsearch未就绪，无法继续")
            exit(1)

        # 导入cert_system数据
        if import_cert_system_data():
            logger.info("cert_system数据导入成功")
        else:
            logger.error("cert_system数据导入失败")
            exit(1)

    if __name__ == "__main__":
        main()

  data/cert_system.json: |
{{ $.Files.Get "files/es-data/cert_system.json" | indent 4 }}
{{- end -}}
