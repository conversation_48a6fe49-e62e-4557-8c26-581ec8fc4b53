import sys
sys.path.append("./")

from PyGksec.GkUtils import common
from PyGksec.GkHelper.GkLogHelper import gk_logging

import PyGksec.GkHelper.AlarmHelper as alarm_helper
import PyGksec.GkHelper.ESHelper as eh
import PyGksec.GkHelper.TagHelper as tag_helper

from PyGksec.GkUtils import common

from Detect.APT.APT29.WellMessFeature import detectWellMessPayload,detectWellMessCookie


def detectWellMessFromES(x_raw):
    payload = common.get_data_from_dict(x_raw,"Client/Payload","")
    cookie = common.get_data_from_dict(x_raw,"Client/Cookie","")
    if detectWellMessPayload(payload) and \
        detectWellMessCookie(cookie):
        gk_logging.info(f"[info] detect wellmess ")
        x_raw["detected"] = 1
    else:
        x_raw["detected"] = 0
    return x_raw

def makeWellMessQuery(batch_id):
    table,detail = eh.get_basic_query(es_type="http",batch_id=batch_id)
    # detail["query"]["bool"]["must"].append({"term": {"Client.Title.keyword":"Cookie"}})   
    # detail["query"]["bool"]["must_not"].append({"term": {"Client.Payload.keyword":"NoPayload"}})                                            
    # nosxist_fields = ["Referer","Origin","Expect","Content-Encoding","Upgrade","If-Modified-Since","X-Online-Host","Upgrade-Insecure-Requests"]   
    # detail["query"]["bool"]["must_not"].append({"terms": {"Client.Title.keyword":nosxist_fields}})                          
    detail["query"]["bool"]["must"].append({"regexp": {"Client.Host.keyword":"([0-9]{1,3}\.){3}[0-9]{1,3}(:[0-9]+)?"}})     
    return table,detail


def detectHttpWellMess(task_id,batch_id):        
    table,detail = makeWellMessQuery(batch_id)
    result = eh.load_es_data_to_DF(table,process_func=detectWellMessFromES,detail=detail,outfile="tmp/wellmess.csv")
    if result is None:
        return
    if result[result.detected==1].shape[0]==0:
        return
    result = result[result.detected==1]
    result["AppId"] = 10637
    result.apply(lambda x : tag_helper.add_tag_for_session(session=x,tag_text="APT29 WellMess控制会话"),axis=1)
    result.apply(lambda x : tag_helper.add_tag_for_session(session=x,tag_text="HTTP隐蔽隧道会话"),axis=1)
    #result.dIp.map(lambda x : tag_helper.add_tag_for_target_toNebula(target=x ,tag_text="命令与控制服务器",task_id=task_id))


    # result.groupby(["sIp","dIp"],as_index=False).\
    #     filter(lambda df : alarm_helper.addAlarmOfSessions\
    #         ("APT29特种木马",df,cols=["dIp","SessionId"],batch_id=batch_id))


if __name__=="__main__":
    task_id,batch_id = eh.get_run_arg("http*")
    detectHttpWellMess(task_id,batch_id)
