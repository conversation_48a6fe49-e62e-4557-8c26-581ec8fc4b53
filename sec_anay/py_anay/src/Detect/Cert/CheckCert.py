# coding: utf-8
import sys 
sys.path.append("./")

import PyGksec.GkHelper.ModelHelper.CertHelper as cert_helper
from PyGksec.GkConfig import env
from PyGksec.GkUtils import common
from PyGksec.GkHelper.GkLogHelper import gk_logging

from Detect.Cert.LocalCert import GkCert_X509,OK

import json
import time
import os
import time
from OpenSSL import crypto
import pymysql
import pandas as pd
from websocket import create_connection
from enum import Enum

class VerifyCaKeyStatus(Enum):
    OK = 1
    Fake = 0
    Err = -1

debug = env["DEPLOY_MODE"]=="develop"
Pass_Exist = env["PASS_IMPORT_EXIST_CERT"]

try:
    ws = create_connection(env["CERT_HOST"])
except Exception as err:
    gk_logging.warn(err)


def hash2file(hash):
    first_dir,second_dir,name = hash[:4],hash[-4:],hash
    filename = f'/opt/GeekSecCA/cerfile/{first_dir}/{second_dir}/{name}'
    return filename

def sendWS_Data(dict_data):
    global ws
    key_value = json.dumps(dict_data)
    try:
        ws.send(key_value)
        result = json.loads(ws.recv())
        return result          
    except Exception as err:
        ws = create_connection("ws://127.0.0.1:19002")
        gk_logging.warn(err)
        return {}
    return

def getCertBySha(pemSha):
    r = commitRequest("sha",{"key":pemSha})
    if 'sumnum_pem'in r and r['sumnum_pem']>0:
        return r['data_pem'][0]
    return 

def commitRequest(request_type,key_map):
    request = key_map
    request["type"] = 'zj_exist'
    r = sendWS_Data(request)       
    return r

def checkByCA(gk_cert,issuer_pub_key):
    return -1
    # if not os.path.exists(cert_pem_file):
    #     return -1
    # try:
    #     os.system("openssl x509 -inform der -in %s -out %s"%(cert_pem_file,temp_pem_file))        
    #     fp = os.popen("./verify %s"%(temp_pem_file), "r")
    #     lines = fp.readlines()
    #     if len(lines)>0:
    #         line = lines[0].strip() 
    #         if  line== "1": # ok
    #             return VerifyCaKeyStatus.OK            
    #         elif line=="0": # fake
    #             return VerifyCaKeyStatus.Fake
    #     return VerifyCaKeyStatus.Err
    # except:
    #     return VerifyCaKeyStatus.Err
  

def checkSignedCertFather(ASN1SHA1):
    r = commitRequest("zj_signed_exist",{"key":ASN1SHA1})
    if "data" in r and len(r['data']) > 0:
        return r['data']
    return 

def submitSignedCert(son_cert,fater_cert):
    commitRequest("zj_signed_smb",{"key":son_cert,"value":fater_cert})      
    commitRequest("zj_verified_sumb",{"key":son_cert})
    # todo
    # add_father_cert_num(son_cert,fater_cert)

def saveCsr(gk_cert):
    filepath = hash2file(gk_cert.cert_json["ASN1SHA1"])
    subpaths = filepath.split('/')
    for end in [-2,-1]:
        path = '/'.join(subpaths[:end])
        if not os.path.exists('/'+path):
            os.mkdir(path)
    dst_cert = open(filepath, "wb")
    dst_cert.write(crypto.dump_certificate(crypto.FILETYPE_ASN1, gk_cert.X509_cert))
    dst_cert.close()
    return filepath



def getFatherCertByKeyId(auth_key):
    res = commitRequest("zj_father_key",{"key":auth_key})
    if "data" in res and res["count"] > 0:
        if type(res['data'][0]) == list and 'keyid' in res['data'][0][0]:
            return set(ca['keyid'] for ca in res['data'][0])
    return set()

def getFatherCertByKeyMd5(issuer_md5):
    res = commitRequest("zj_sub_md5",{"key":issuer_md5})
    if "data" in res and res["count"]>0 and 'CertID' in r['data'][0]:
        return set([ca['CertID'] for ca in res['data']])
    return set()

def getFatherCert(gk_cert):
    """
        Search Father Key
    """
    request = {}    
    father_key = common.get_data_from_dict(gk_cert.cert_json,"Extension/authorityKeyIdentifier","")
    father_key = cert_helper.formatKeyId(father_key)

    if len(father_key)>0:
        ca_cert = getFatherCertByKeyId(father_key)
    else:
        issuer_md5 = common.get_data_from_dict(gk_cert.cert_json,"IssuerMD5","")        
        ca_cert = getFatherCertByKeyMd5(issuer_md5)
    ca_cert = ca_cert - {gk_cert.cert_json["ASN1SHA1"]}
    return ca_cert


def checkWhiteCA(gk_cert,ca):
    if checkCertSystemCert(ca) or checkSignedCertFather(ca):
        submitSignedCert(gk_cert.cert_json["ASN1SHA1"],ca)
    else:
        ca_file_path = hash2file(ca)
        ca_cert = GkCert_X509(ca_file_path)
        checkCertSign(ca_cert)
        if checkSignedCertFather(ca):
            submitSignedCert(gk_cert.cert_json["ASN1SHA1"],ca)


def checkCertSign(gk_cert):
    isFakeCert,isIllegalCert = False,False
    verify_pem_status = VerifyCaKeyStatus.Err
    if gk_cert.tag_json["Self Signed Cert"] != "Yes":
        return isFakeCert,isIllegalCert

    caSet = getFatherCert(gk_cert)
    for ca in caSet:      
        issuer_cert = getCertBySha(ca)        
        issuer_pub_key = common.get_data_from_dict(issuer_cert,"PublicKey","")
        if len(issuer_pub_key)==0 or issuer_pub_key == "Decode Error":
            isIllegalCert = True
        # todo
        verify_pem_status = checkByCA(gk_cert,issuer_pub_key)    
        if verify_pem_status == VerifyCaKeyStatus.OK:
            break    
        
    if verify_pem_status==VerifyCaKeyStatus.OK:
        checkWhiteCA(gk_cert,ca)
        if common.get_data_from_dict(issuer_cert,"Extension/basicConstraints","")\
            .find("CA:FALSE") > 0:  
            isIllegalCert = True
        if issuer_cert["NotAfter"] < gk_cert.cert_json["NotAfter"]:
            isIllegalCert = True
    
    elif verify_pem_status==VerifyCaKeyStatus.Fake:
        isFakeCert = True

    return isFakeCert,isIllegalCert



def checkCollisionCert(gk_cert):
    res = commitRequest("zj_pem_md5",{"key":gk_cert.cert_json["PemMD5"]})        
    if "data" not in res or len(res['data'])==0:
        return False
    for sha in res['data'][0]['arr']:
        if sha!=gk_cert.cert_json["ASN1SHA1"]:
            return True
    return False



def checkCertSystemCert(ASN1SHA1):
    res = commitRequest("zj_exist",{"key":ASN1SHA1})
    cert_imported = common.get_data_from_dict(res,"exist",False)
    return cert_imported

def setSystemCertTags(gk_cert):
    gk_cert.tag_json["White Cert"]="Yes"

def setUserCertTags(gk_cert):
    isFakeCert,isIllegalCert = checkCertSign(gk_cert)
    isWhiteCA = not Pass_Exist and checkSignedCertFather(gk_cert.cert_json["ASN1SHA1"])

    gk_cert.tag_json["White Cert"] = "No"    
    gk_cert.tag_json["WhiteCA Cert"] = "Yes" if isWhiteCA else "No"
    gk_cert.tag_json["Fake Cert"] = "Yes" if isFakeCert else "No"
    gk_cert.tag_json["Illegal Cert"] = "Yes" if isIllegalCert else "No"
    gk_cert.tag_json["Collision Cert"] = "Yes" if checkCollisionCert(gk_cert) else "No"



def checkCert(filename):
    """
        基于关联信息扩充证书标签
    """
    # cert_id = cert_helper.getCertIdFromCertPath(filename)
    gk_cert = GkCert_X509(filename)
    if gk_cert.Status == OK:
        cert_id = gk_cert.cert_json["ASN1SHA1"] 
        if checkCertSystemCert(cert_id):
            setSystemCertTags(gk_cert)
        else:
            setUserCertTags(gk_cert)
    return gk_cert


if __name__=='__main__':
    filename = sys.argv[1]
    if len(sys.argv)>2:
        upload_cert = sys.argv[2]
    else:
        upload_cert = ""            
    gk_cert = checkCert(filename)
    gk_cert.showInfo()



