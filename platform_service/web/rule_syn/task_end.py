import pymysql 
import json
import time 
import os,sys
base_json = {}
with open("/opt/GeekSec/pubconfig/pubconfig.json",'r') as load_f:
    base_json = json.load(load_f)
from loadMysqlPasswd import  mysql_passwd
passwd = mysql_passwd()
db = pymysql.connect(host=base_json["tidb_host"],port=base_json["tidb_port"],user='root',password=passwd,db='th_analysis',charset='utf8mb4',cursorclass=pymysql.cursors.DictCursor)
cursor = db.cursor()
task_info = {}
if len(sys.argv) != 4:
    print("       ")
    sys.exit(1)
task_info["task_id"] = sys.argv[1]
task_info["batch_id"] = sys.argv[2]
task_info["batch_num_dir"] = sys.argv[3]
def isIP4or6(cfgstr):
    ipFlg = False
    if '/' in cfgstr:
        text = cfgstr[:cfgstr.rfind('/')]
    else:
        text = cfgstr
    try:
        addr = IPAddress(text)
        ipFlg = True
    except:
        ipFlg = False
    if ipFlg == True:
        return addr.version
    else:
        return 0
def idu_mysql(sql,cur,x_db):
    print(sql)
    cur.execute(sql)
    x_db.commit()
def s_mysql(sql,cur):
    print(sql)
    cur.execute(sql)
    return cur.fetchall()

keynote_doname ={}
def domain_read(task_id , cursor):
    sql = "select domain_name  ,task_id  from tb_inside_domain where  task_id  = " +str(task_id)

    cursor.execute(sql)
    result = cursor.fetchall()
    if result is  None:
        return 
    print(result)
    for rows in result :
        keynote_doname[rows["domain_name"]]= ""
keynote_ip ={}
def IP_Mask_crc(ip,Mask):
    if Mask == "*************":
        keynote_ip[ip]  = ""
    if Mask == "***********":
        ip_list = ip.split(".")
        ip_str = ip_list[0]+"."+ip_list[1]+"."+ip_list[2]+"."
        for i in range(1,256):
            keynote_ip[ip_str +str(i)] = ""
def read_ddos_config(task_id,cursor):
    sql = "select doss_json from  tb_ddos_config where task_id = "+str(task_id)
    cursor.execute(sql)
    result = cursor.fetchall()
    print(result)
    for row in result:
        print(row["doss_json"])
        conf_json = json.loads(row["doss_json"])
        ip = conf_json['IP']
        mask =conf_json['Mask']
        IP_Mask_crc(ip,mask)

def update_inside():
    sql = "select a.domain , a.ip from    tb_domain_attribute_ip  a , tb_inside_domain  b  where  b.domain_name = a.domain   and b.task_id = "+str(task_info["task_id"]) +" order by b.domain_name "
    print(sql)
    relset =  s_mysql(sql,cursor)
    domain_ip = {}
    for row in relset :
        domain =row['domain']
        ip = row['ip']
        if domain not in  domain_ip :
            domain_ip[domain] = ip
        else :
            domain_ip[domain] = domain_ip[domain] + ","+ ip
    for key in domain_ip :
        sql = "update tb_inside_domain set link_ip = \""+domain_ip[key]+"\"  where  domain_name = \""+key + "\" and  task_id =" +str(task_info["task_id"])
        idu_mysql(sql , cursor ,db )
    # 重点目标打标
def inside_sign_update():
    sql = "insert IGNORE  into tb_ip_tag select a.ip , a.tkey ,142, unix_timestamp(now()),1 from tb_ip_info a , tb_target b where b.target_type = 0  and b.target_name = a.ip    "
    idu_mysql(sql , cursor ,db )
    sql = "insert IGNORE  into tb_domain_tag select a.n_domain ,141, unix_timestamp(now()),1 from tb_domain_info  a , tb_target b where b.target_type = 3  and b.target_name = a.n_domain    "
    idu_mysql(sql , cursor ,db )
    # 内部IP  # 内部域名
    for ip in  keynote_ip:
        sql = "insert IGNORE tb_ip_tag select a.ip , a.tkey ,143, unix_timestamp(now()),1 from tb_ip_info a where a.ip = \'"+ ip +"\'" ;
        idu_mysql(sql , cursor ,db )
    for domain in keynote_doname :
        sql = "insert IGNORE tb_domain_tag select a.n_domain , 144, unix_timestamp(now()),1 from tb_domain_info a where a.n_domain = \'"+ domain  +"\'" 
        idu_mysql(sql , cursor ,db )
    sql = "select target_name , target_type from tb_target "
    res = s_mysql(sql , cursor)
    print(res)
    for row in res :
        if row["target_type"] == 0: ### //0代表ip目标，1代表端口目标，2代表应用目标，3代表域名目标，4代表证书目标，5代表MAC目标，6代表连接目标 7 指纹
               sql = "update tb_target set data_begin_time = (select ifnull(min(begin_time),0) from tb_ip_info where  ip = '"+str(row["target_name"])+"') , data_end_time = (select ifnull(max(end_time),0) from tb_ip_info where  ip = '"+str(row["target_name"])+"'), black_list = (select ifnull(max(black_list) , 0)  from tb_ip_info where  ip = '"+str(row["target_name"])+"')  where target_name = '"+row["target_name"]+ "' and target_type = 0 " 
               idu_mysql(sql , cursor ,db )
        if row["target_type"] == 3:
               sql = "update tb_target set data_begin_time = (select ifnull(min(first_time),0) from tb_domain_attribute  where  n_domain = '"+str(row["target_name"])+"') , data_end_time = (select ifnull(max(last_time),0) from tb_domain_attribute  where  n_domain  = '"+str(row["target_name"])+"'), black_list = (select ifnull(max(black_list) , 0)  from tb_domain_attribute  where  n_domain = '"+str(row["target_name"])+"')  where target_name = '"+row["target_name"]+ "' and target_type = 3 " 
               idu_mysql(sql , cursor ,db )
        if row["target_type"] == 7:
               sql = "update tb_target set data_begin_time = (select ifnull(min(first_time),0) from ((select ifnull(min(a.first_time),0) as first_time from tb_client_finger a , tb_finger_infor b  where  b.finger_sha1 = '"+str(row["target_name"])+"' and a.finger = b.finger) union ( select ifnull(min(a.first_time),0) as first_time from tb_server_finger a ,tb_finger_infor b   where b.finger_sha1 = '"+str(row["target_name"])+"' and a.finger = b.finger)) as t ) , end_time = (select ifnull(min(last_time),0) from ((select ifnull(min(a.last_time),0) as last_time from tb_client_finger a , tb_finger_infor  b  where  b.finger_sha1 = '"+str(row["target_name"])+"' and a.finger = b.finger) union ( select ifnull(min(a.last_time),0) as last_time from tb_server_finger a , tb_finger_infor  b where b.finger_sha1 = '"+str(row["target_name"])+"' and a.finger = b.finger)) as t   ), black_list = (select ifnull(max(black_list) , 0)  from tb_finger_infor  b where  finger_sha1 = '"+str(row["target_name"])+"')  where target_name = '"+row["target_name"]+ "' and target_type = 7 " 

               idu_mysql(sql , cursor ,db )
        if row["target_type"] == 4:
               sql = "update tb_target set data_begin_time = (select ifnull(min(first_time),0) from tb_cert  where cert_sha1 = '"+str(row["target_name"])+"') , data_end_time = (select ifnull(max(last_time),0) from tb_cert where  cert_sha1  = '"+str(row["target_name"])+"'), black_list = (select ifnull(max(black_list) , 0)  from tb_cert_info  where   cert_sha1  = '"+str(row["target_name"])+"')  where target_name = '"+row["target_name"]+ "' and target_type = 4 " 
               idu_mysql(sql , cursor ,db )
read_ddos_config(str(task_info["task_id"]),cursor)
domain_read(str(task_info["task_id"]),cursor)

def task_end():
    #### 
    if (isIP4or6(task_info["batch_num_dir"]) != 0):
        mount_path = "/data/"+task_info["batch_num_dir"]
        os.system("umount "+mount_path)
    time.sleep(30)

task_end()
idu_mysql("UPDATE tb_task_batch SET state = 0  where batch_id = "+ task_info["batch_id"],cursor, db)
idu_mysql("UPDATE tb_task_analysis a  SET a.task_state = 0  where 0 = (select count(batch_id) from tb_task_batch where state = 1 and  task_id = "+task_info["task_id"] +")  and a.task_id = "+ task_info["task_id"] , cursor, db)
#os.system("rm -rf /data/.task_pcap/"+str(task_info["task_id"]))
